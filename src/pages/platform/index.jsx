import { useEffect, useState } from 'react';
import { useModel, useSearchParams } from 'umi';
import { Result } from 'antd';
import { flatten } from "@/utils/tree";
import locale from "@/locale";
import './index.less'

/**
 * 第三方接入使用iframe模式，直接配置菜单时，跳转至platform页面，iframe加载菜单配置对应的iframeUrl地址
 */
const Platform = () => {
    const { menus, sideBarExpand } = useModel('menu');
    const { token, userInfo } = useModel('user');
    const [ searchParams ] = useSearchParams();
    const code = searchParams.get('code');
    const [iframeUrl, setIframeUrl] = useState('');

    useEffect(() => {
        if (menus && code) {
            const menuArr = flatten(menus) || [];
            const url = menuArr.find(item => item.code == code)?.iframeUrl;
            setIframeUrl(url);
        }
    }, [menus, code]);

    const handleMessage = (e) => {
        if (e.data?.message == 'GET_USER_INFO') {
            const iframe = document.getElementById('myIframe');
            iframe?.contentWindow?.postMessage(
                {
                    message: 'USER_INFO',
                    data: {
                        userToken: token,
                        userInfo: userInfo,
                        curOrg: sessionStorage.getItem('curOrg'),
                        activeApp: sessionStorage.getItem('activedApp'),
                        subParams: sessionStorage.getItem('subTreeParams') || '',
                    }
                }, 
                '*',
            );
        }
    };

    useEffect(() => {
        const iframe = document.getElementById('unifyIframe');
        if (iframe) {
            window.addEventListener('message', handleMessage);
        }
        return () => {
            window.removeEventListener('message', handleMessage);
        };
    }, []);

    return (
        <div className="iframe-page-warp" style={sideBarExpand ? null : { width: 'calc(100% - 56px)', left: 56 }}>
            {
                iframeUrl ?  <iframe
                    src={iframeUrl}
                    title="unifyIframe"
                    name="unifyIframe"
                    id="unifyIframe"
                /> : <Result title={locale.error.noIframeUrl} />
            } 
        </div>
    );
};

export default Platform;
