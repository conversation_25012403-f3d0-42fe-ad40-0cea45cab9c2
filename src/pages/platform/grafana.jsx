import { useEffect, useState } from "react";
import { useLocation, useModel } from "umi"
import "./index.less";

function GrafanaWarp () {
  const location = useLocation();
  const { sideBarExpand } = useModel("menu");
  const [frameUrl, setFrameUrl] = useState('');
  
  useEffect(() => {
    setFrameUrl(location.pathname + location.search);
  }, [location])

  return (
    <div className="iframe-page-warp" style={sideBarExpand ? null : { width: 'calc(100% - 56px)', left: 56 }}>
      <iframe id="grafana-frame" src={frameUrl}/>
    </div>
  )
}
export default GrafanaWarp