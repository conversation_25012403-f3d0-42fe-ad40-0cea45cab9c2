import React from 'react';
import './index.less';

const friendLinks = [
  { label: 'BizDevOps', url: 'https://bizdevops.trinasolar.com' },
  { label: 'TIS', url: 'https://tis.trinasolar.com/' },
];
const friendLinks1 = [
  { label: 'iPaaS', url: 'https://ipaas-uat.trinasolar.com' },
  { label: 'SCA', url: 'https://sca.trinasolar.com/' }
];



const internalSupport = [
  { name: '吕凌晖', email: '<EMAIL>' },
  { name: '杨刚', email: '<EMAIL>' }
];

const vendorSupport = [
  { name: '彭书友', email: '<EMAIL>' },
  { name: '刘顺', email: '<EMAIL>' }
];

const Footer = () => {
  return (
    <footer className="footer custom-footer">
      <div className="footer-columns">
        {/* 友情链接 */}
        <div className="footer-col">
          <div className="footer-title">友情链接</div>
          <ul className="footer-list">
            {friendLinks.map(link => (
              <li key={link.label}>
                <div>{link.label}：</div>
                <a href={link.url} target="_blank" rel="noopener noreferrer" style={{color:'#4fa3ff'}}>{link.url}</a>
              </li>
            ))}
          </ul>
        </div>
        <div className="footer-col">
          <div className="footer-title" style={{marginBottom:38}}></div>
          <ul className="footer-list">
            {friendLinks1.map(link => (
              <li key={link.label}>
                <div>{link.label}：</div>
                <a href={link.url} target="_blank" rel="noopener noreferrer" style={{color:'#4fa3ff'}}>{link.url}</a>
              </li>
            ))}
          </ul>
        </div>
        {/* 内部支持 */}
        <div className="footer-col">
          <div className="footer-title">内部支持</div>
          <ul className="footer-list" style={{minWidth:360}}>
            {internalSupport.map(person => (
              <li key={person.email}>
                <div>支持人员：{person.name}</div>
                <div>联系方式：<span href={`mailto:${person.email}`} >{person.email}</span></div>
              </li>
            ))}
          </ul>
        </div>
        {/* 厂商支持 */}
        <div className="footer-col">
          <div className="footer-title">厂商支持</div>
          <ul className="footer-list" style={{minWidth:360}}>
            {vendorSupport.map(person => (
              <li key={person.email}>
                <div>支持人员：{person.name}</div>
                <div>联系方式：<span href={`mailto:${person.email}`} >{person.email}</span></div>
              </li>
            ))}
          </ul>
        </div>
      </div>
    </footer>
  );
};

export default Footer; 