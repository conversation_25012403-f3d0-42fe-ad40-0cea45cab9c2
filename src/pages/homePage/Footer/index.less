@min-width: 1272px;
@max-width: 2072px;
.footer {
  background: #2B3040;
  padding: 40px 460px 24px;
  color: #fff;
  
  .footer-content {
    width: 100%;
    margin: 0 auto;
    
    .footer-nav {
      display: grid;
      grid-template-columns: repeat(4, 1fr) 2fr 1fr;
      gap: 40px;
      margin-bottom: 40px;
      
      .nav-section {
        h3 {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 20px;
          color: #fff;
        }
        
        ul {
          list-style: none;
          padding: 0;
          margin: 0;
          
          li {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.65);
            margin-bottom: 16px;
            cursor: pointer;
            transition: color 0.3s;
            
            &:hover {
              color: #fff;
            }
          }
        }
      }
      
      .company-info {
        .logo {
          height: 24px;
          margin-bottom: 20px;
          opacity: 0.9;
        }
        
        .contact {
          p {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.65);
            margin-bottom: 12px;
            line-height: 1.5;
          }
        }
      }
      
      .qrcode {
        text-align: center;
        
        h3 {
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 16px;
          color: #fff;
        }
        
        img {
          width: 100px;
          height: 100px;
          background: #fff;
          padding: 4px;
          border-radius: 2px;
        }
      }
    }
    
    .footer-links {
      position: relative;
      padding: 16px 0;
      margin: 24px 0;
      
      &::before,
      &::after {
        content: '';
        position: absolute;
        left: 0;
        right: 0;
        height: 1px;
        background: rgba(255, 255, 255, 0.08);
      }
      
      &::before {
        top: 0;
      }
      
      &::after {
        bottom: 0;
      }
      
      .related-links {
        display: flex;
        align-items: center;
        gap: 24px;
        color: rgba(255, 255, 255, 0.45);
        font-size: 14px;
        
        a {
          color: rgba(255, 255, 255, 0.45);
          text-decoration: none;
          transition: color 0.3s;
          
          &:hover {
            color: rgba(255, 255, 255, 0.85);
          }
        }
      }
    }
    
    .footer-bottom {
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .icp-info {
        display: flex;
        align-items: center;
        gap: 32px;
        color: rgba(255, 255, 255, 0.45);
        font-size: 13px;
        
        .police-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          img {
            height: 14px;
            opacity: 0.6;
          }
        }
      }
      
      .page-info {
        color: rgba(255, 255, 255, 0.45);
        font-size: 13px;
      }
    }
  }
}

@media screen and (max-width: 1920px) {
  .footer {
    padding: 40px 340px 24px;
  }
}

@media screen and (max-width: 1600px) {
  .footer {
    padding: 40px 220px 24px;
  }
}

@media screen and (max-width: 1200px) {
  .footer {
    padding: 40px 140px 24px;
    
    .footer-content {
      .footer-nav {
        grid-template-columns: repeat(3, 1fr);
        gap: 32px;
        
        .company-info {
          grid-column: span 2;
        }
        
        .qrcode {
          grid-column: span 1;
        }
      }
    }
  }
}

@media screen and (max-width: 768px) {
  .footer {
    padding: 32px 120px 20px;
    
    .footer-content {
      padding: 0 20px;
      
      .footer-nav {
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;
        margin-bottom: 32px;
        
        .nav-section {
          h3 {
            margin-bottom: 16px;
          }
          
          ul li {
            margin-bottom: 12px;
          }
        }
        
        .company-info,
        .qrcode {
          grid-column: span 2;
          text-align: center;
          
          .contact {
            display: flex;
            flex-direction: column;
            align-items: center;
            
            p {
              margin-bottom: 8px;
            }
          }
        }
      }
      
      .footer-links {
        margin: 20px 0;
        
        .related-links {
          flex-wrap: wrap;
          justify-content: center;
          gap: 16px;
        }
      }
      
      .footer-bottom {
        flex-direction: column;
        gap: 16px;
        text-align: center;
        
        .icp-info {
          flex-direction: column;
          gap: 12px;
        }
      }
    }
  }
} 

.footer .footer-nav a {
  color: rgba(255, 255, 255, 0.45);
  transition: color 0.3s;
}

.footer .footer-nav a:hover {
  color: #fff;
}

.custom-footer {
  background: #2B3040;
  color: #fff;
  padding: 0 !important;
  min-width: @min-width;
}

.footer-columns {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 20px;
  margin: 0 auto;
  width: 100%;
  max-width: @max-width;
  min-width: @min-width;
  padding: 40px 180px 24px !important;
}

.footer-col {
  flex: 1 1 0;
  min-width: 220px;
}

.footer-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 18px;
  color: #fff;
  letter-spacing: 1px;
}

.footer-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-list li {
  font-size: 15px;
  color: #d6e0f0;
  margin-bottom: 14px;
  line-height: 1.7;
  word-break: break-all;
  white-space: normal;
  overflow: visible;
  text-overflow: unset;
}

.footer-list a {
  color: #4fa3ff;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-list a:hover {
  color: #fff;
  text-decoration: underline;
}

/* 响应式适配 */
@media (max-width: 900px) {
  .footer-columns {
    flex-direction: column;
    gap: 32px;
    align-items: stretch;
    max-width: 95vw;
  }
  .footer-col {
    min-width: 0;
  }
}

.footer-col-support {
  min-width: 420px;
  max-width: 520px;
  flex: 2 1 0;
}

@media (max-width: 1400px) {
  .footer-col-support {
    min-width: 0;
    max-width: 100%;
    flex: 1.5 1 0;
  }
}