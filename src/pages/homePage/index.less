@min-width: 1272px;
@max-width: 2072px;
.home-container {
  // min-height: 100vh;
  width: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  min-width: @min-width !important;
  .home-header {
    text-align: left;
    height: 400px;
    width: 100%;
    background-image: url('~@/assets/images/header.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    padding: 24px 0;
    min-width: @min-width !important;
    .header-content{
      min-width: @min-width !important;
      max-width: @max-width !important;
      margin: 100px auto 0;
      padding: 0 80px;
    }
    h1 {
      font-size: 32px;
      margin-bottom: 16px;
      font-weight: 500;
      color: #000;
    }

    .description {
      color: #666;
      font-size: 14px;
      line-height: 1.8;
      width: 50%;
    }
  }
  .content-wrapper {
    width: 100%;
    margin: 0 auto;
    min-width: @min-width !important;
    max-width: @max-width !important;
    padding: 0 80px;
  }

  // 响应式调整
  @media screen and (min-width: 1920px) {
    .dealPadding {
      padding: 24px 240px;
    }
  }
  // 响应式调整
  @media screen and (max-width: 1920px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  @media screen and (max-width: 1600px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  @media screen and (max-width: 1200px) {
    .dealPadding {
      padding: 24px 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .dealPadding {
      padding: 24px 20px;
    }

    .home-header {
      padding: 24px;

      h1 {
        font-size: 24px;
      }

      .description {
        font-size: 14px;
      }
    }
  }

  .statisticsContainer {
    max-width: 1200px;
    margin: 0 auto;
  }

  .statistics-row {
    margin-bottom: 48px;
  }

  .statistics-card-wrapper {
    position: relative;
    height: 24px;

  }

  .statistics-cards {
    position: absolute;
    top: -70px;
    width: 100%;
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;
    opacity: 0.8;

    .stat-card {
      background: #fff;
      border-radius: 4px;
      padding: 20px;
      display: flex;
      align-items: center;
      gap: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      backdrop-filter: blur(5px);
      min-width: 166px !important;
      .stat-icon {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: #f5f7fa;
        flex-shrink: 0;
      }

      .stat-content {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
        height: 50px;
        justify-content: space-between;
        .stat-label {
          font-size: 14px;
          color: #4e5969;
        }

        .stat-value-unit {
          display: flex;
          align-items: center;
          gap: 4px;
          height: 30px;
          .stat-value {
            font-size: 24px;
            font-weight: 500;
            color: #1f2329;
            line-height: 1.2;
            &-small {
              font-size: 18px;
            }
          }

          .stat-unit {
            font-size: 14px;
            font-weight: 500;
            color: #86909c;
          }
        }
      }
    }
  }
   // 响应式布局
   @media screen and (min-width: 1400px) {
    .statistics-cards {
      grid-template-columns: repeat(6, 1fr);
    }
  }
  // 响应式布局
  @media screen and (max-width: 1400px) {
    .statistics-cards {
      grid-template-columns: repeat(6, 1fr);
    }
  }

  @media screen and (max-width: 768px) {
    .statistics-cards {
      grid-template-columns: repeat(6, 1fr);
      gap: 16px;

      .stat-card {
        padding: 16px;
        .stat-icon {
          width: 32px;
          height: 32px;
        }

        .stat-content {
          .stat-value {
            font-size: 20px;
          }

          .stat-label {
            font-size: 13px;
          }

          .stat-unit {
            font-size: 11px;
          }
        }
      }
    }
  }

  @media screen and (max-width: 480px) {
    .statistics-cards {
      grid-template-columns: 1fr;
    }
  }

  .statistic-card {
    background: #fff;
    border-radius: 4px;
    transition: all 0.3s;
    height: 100%;

    &:hover {
      transform: translateY(-4px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    .card-content {
      display: flex;
      align-items: center;
      padding: 24px;

      .icon-wrapper {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        background: rgba(24, 144, 255, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .anticon {
          font-size: 24px;
          color: #1890ff;
        }
      }

      .info {
        flex: 1;

        .title {
          color: #666;
          font-size: 14px;
          margin-bottom: 8px;
        }

        .count {
          font-size: 24px;
          font-weight: 600;
          color: #000;
          line-height: 1;

          .unit {
            font-size: 14px;
            margin-left: 4px;
            font-weight: normal;
            color: #666;
          }
        }
      }
    }
  }

  .dataCard {
    margin-bottom: 24px;
    background: white;
    border-radius: 8px;

    :global {
      .ant-statistic-title {
        color: #666;
        font-size: 14px;
        margin-bottom: 8px;
      }

      .ant-statistic-content {
        color: #1f1f1f;
        font-size: 24px;
        font-weight: bold;
      }
    }
  }

  .featuresContainer {
    .featureCard {
      height: 100%;
      background: white;
      border-radius: 8px;

      :global {
        .ant-card-head {
          border-bottom: 1px solid #f0f0f0;

          .ant-card-head-title {
            color: #1f1f1f;
          }
        }
      }
    }

    .featureList {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        position: relative;
        padding: 12px 0 12px 24px;
        color: #666;
        font-size: 14px;
        border-bottom: 1px dashed #f0f0f0;

        &:last-child {
          border-bottom: none;
        }

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 6px;
          height: 6px;
          background: #1890ff;
          border-radius: 50%;
        }
      }
    }
  }

  .platform-section {
    margin: 48px 0 0 0;

    .section-title {
      font-size: 20px;
      font-weight: 500;
      color: #000;
      text-align: center;
      margin-bottom: 40px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: -12px;
        transform: translateX(-50%);
        width: 32px;
        height: 4px;
        background: #1890ff;
      }
    }

    .platform-content {
      background: #fff;
      border-radius: 4px;

      .unified-services {
        background: #EBF5FF;
        border-radius: 4px;
        padding: 24px;
        margin-bottom: 24px;

        .service-title {
          color: #1890ff;
          font-size: 16px;
          font-weight: 500;
          margin-bottom: 16px;
          text-align: center;
        }

        .service-items {
          display: flex;
          justify-content: space-between;
          gap: 16px;

          .service-item {
            flex: 1;
            background: #fff;
            padding: 12px;
            text-align: center;
            border-radius: 4px;
            color: #1890ff;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;

            &:hover {
              color: #40a9ff;
              box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
            }
          }
        }
      }

      .support-platforms {
        display: flex;
        justify-content: space-between;
        gap: 16px;
        margin-bottom: 16px;
        margin-top: 30px;

        .platform-item {
          flex: 1;
          background: rgb(233, 238, 253);
          padding: 8px 12px;
          text-align: center;
          border-radius: 4px;
          color: #1890ff;
          font-size: 14px;
          transition: all 0.3s;
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 4px;
          position: relative;

          .lightning-icon {
            font-size: 24px;
            line-height: 1;
            height: 24px;
            position: absolute;
            top: -28px;
          }

          &:hover {
            background: rgba(233, 238, 253, 0.8);
          }
        }
      }

      .standards {
        display: flex;
        gap: 24px;
        margin-bottom: 16px;

        .standard-item {
          flex: 1;
          background: rgb(238, 248, 254);
          border-radius: 4px;
          padding: 12px 16px;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 8px;

          .icon {
            width: 24px;
            height: 24px;
            background: #1890ff;
            color: #fff;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 500;
            flex-shrink: 0;
          }

          .text {
            flex: 1;
            line-height: 1.5;
            color: #333;
            font-size: 14px;
            text-align: center;
          }
        }
      }

      .core-modules {
        display: flex;
        gap: 20px;
        margin-bottom: 16px;

        .module-card {
          flex: 1;
          background: #EBF5FF;
          border-radius: 8px;
          padding: 20px;
          min-width: 0;

          .card-title {
            background: linear-gradient(90deg, #0F6AF6 5%, #71AAFC 106%);
            border-radius: 15px;
            color: #fff;
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 16px;
            text-align: center;
            white-space: nowrap;
            padding: 8px 16px;
          }

          .card-items {
            display: flex;
            flex-direction: column;
            gap: 12px;

            .card-item {
              background: #fff;
              border-radius: 15px;
              padding: 10px 16px;
              font-size: 13px;
              color: #666;
              text-align: center;
              transition: all 0.3s;
              word-break: break-word;
              min-height: 40px;
              display: flex;
              align-items: center;
              justify-content: center;



              &:hover {
                color: #1890ff;
              }
            }
          }
        }
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 1200px) {
    .platform-section {
      .platform-content {
        .core-modules {
          flex-wrap: wrap;

          .module-card {
            flex: 0 0 calc(33.33% - 14px);
          }
        }
      }
    }

    .statistics-row {
      margin-bottom: 32px;
    }
  }

  @media screen and (max-width: 768px) {
    .platform-section {
      .platform-content {
        padding: 16px;

        .unified-services {
          .service-items {
            flex-wrap: wrap;

            .service-item {
              flex: 0 0 calc(50% - 8px);
            }
          }
        }

        .core-modules {
          .module-card {
            flex: 0 0 calc(50% - 10px);
          }
        }

        .support-platforms {
          .platform-item {
            flex: 0 0 calc(33.33% - 11px);
          }
        }

        .standards {
          flex-direction: column;
          gap: 16px;

          .standard-item {
            padding: 12px 16px;

            .icon {
              width: 24px;
              height: 24px;
              font-size: 12px;
            }

            .text {
              font-size: 13px;
            }
          }
        }
      }
    }

    .statistics-row {
      margin-bottom: 24px;
    }

    .statistic-card {
      .card-content {
        padding: 16px;

        .icon-wrapper {
          width: 40px;
          height: 40px;
          margin-right: 12px;

          .anticon {
            font-size: 20px;
          }
        }

        .info {
          .count {
            font-size: 20px;
          }
        }
      }
    }
  }

  .key-metrics-section {
    margin: 48px 0;
    background: #fff;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
    background-color: #F8FAFD;

    .section-title {
      font-size: 20px;
      font-weight: 500;
      color: #000;
      text-align: center;
      margin-bottom: 40px;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        left: 50%;
        bottom: -12px;
        transform: translateX(-50%);
        width: 32px;
        height: 4px;
        background: #1890ff;
      }
    }

    .metrics-content {
      display: flex;
      gap: 48px;
      position: relative;
      z-index: 1;

      .metrics-list {
        flex: 1;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 24px;

        .metric-item {
          .metric-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
          }

          .metric-value {
            font-size: 24px;
            font-weight: 600;
            color: #000;
            line-height: 1.4;

            .unit {
              font-size: 14px;
              color: #666;
              margin-left: 4px;
              font-weight: normal;
            }
          }
        }
      }

      .world-map {
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        img {
          width: 100%;
          height: auto;
          max-width: 480px;
          opacity: 0.8;
        }
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 1200px) {
    .key-metrics-section {
      padding: 24px;

      .metrics-content {
        gap: 32px;

        .metrics-list {
          gap: 16px;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    .key-metrics-section {
      padding: 20px;
      margin: 24px 0;

      .section-title {
        margin-bottom: 24px;
        font-size: 18px;
      }

      .metrics-content {
        flex-direction: column;
        gap: 24px;

        .metrics-list {
          grid-template-columns: 1fr;
          gap: 16px;

          .metric-item {
            .metric-value {
              font-size: 20px;
            }
          }
        }

        .world-map {
          img {
            max-width: 100%;
          }
        }
      }
    }
  }

  .module-section {
    margin: 48px 0;

    .module-content {
      display: flex;
      gap: 48px;
      align-items: flex-start;

      .module-diagram {
        flex: 0 0 45%;
        max-width: 45%;

        img {
          width: 100%;
          height: auto;
          display: block;
        }
      }

      .module-info {
        max-width: 55%;
        .module-header {
          margin-bottom: 16px;

          .module-description {
            font-size: 14px;
            line-height: 1.8;
            color: #666;
          }
        }

        .module-list {
          display: flex;
          flex-direction: column;
          gap: 12px;

          .module-item {
            display: flex;
            align-items: flex-start;
            gap: 16px;

            .module-number {
              width: 32px;
              height: 32px;
              background: #1890ff;
              border-radius: 50%;
              color: #fff;
              font-size: 14px;
              font-weight: 500;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .module-info {
              flex: 1;
              padding: 0;
              max-width: 100%;

              .module-title {
                font-size: 16px;
                font-weight: 500;
                color: #000;
                margin-bottom: 8px;
              }

              .module-desc {
                font-size: 14px;
                color: #666;
                line-height: 1.6;
              }
            }
          }
        }
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 1200px) {
    .module-section {
      .module-content {
        gap: 32px;

        .module-diagram {
          max-width: 45%;
        }
      }
    }
  }

  @media screen and (max-width: 768px) {
    .module-section {
      margin: 32px 0;

      .module-content {
        flex-direction: column;
        gap: 24px;

        .module-diagram {
          max-width: 100%;
        }

        .module-info {
          .module-header {
            margin-bottom: 24px;
          }

          .module-list {
            gap: 24px;

            .module-item {
              gap: 12px;

              .module-number {
                width: 28px;
                height: 28px;
                font-size: 12px;
              }

              .module-info {
                .module-title {
                  font-size: 15px;
                }

                .module-desc {
                  font-size: 13px;
                }
              }
            }
          }
        }
      }
    }
  }

  .bottom-features {
    background: #fff;
    border-radius: 4px;

    .features-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 24px;

      .feature-card {
        background: rgb(233, 238, 253);
        border-radius: 4px;
        padding: 24px;

        .card-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 16px;

          .icon {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            font-weight: bold;
            color: #fff;

            &.blue {
              background: #1890ff;
            }

            &.cyan {
              background: #13C2C2;
            }

            &.gold {
              background: #FAAD14;
            }
          }

          .title {
            font-size: 16px;
            font-weight: 500;
            color: #000;
          }
        }

        .card-content {
          font-size: 14px;
          color: #666;
          line-height: 1.6;
        }
      }
    }
  }

  // 响应式调整
  @media screen and (max-width: 1200px) {
    .bottom-features {
      padding: 24px;

      .features-grid {
        gap: 16px;
      }
    }
  }

  @media screen and (max-width: 768px) {
    .bottom-features {
      margin: 32px 0;
      padding: 16px;

      .features-grid {
        grid-template-columns: 1fr;
        gap: 16px;

        .feature-card {
          padding: 16px;

          .card-header {
            gap: 8px;
            margin-bottom: 12px;

            .icon {
              width: 28px;
              height: 28px;
              font-size: 14px;
            }

            .title {
              font-size: 15px;
            }
          }

          .card-content {
            font-size: 13px;
          }
        }
      }
    }
  }

  .module-diagram {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;

    img {
      max-width: 800px; // 设置最大宽度，可以根据需要调整
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }

  .footer-nav a {
    color: #fff;
  }
}