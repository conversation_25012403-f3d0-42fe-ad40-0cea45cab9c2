import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';

import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import './index.less';

import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Modal } from 'antd';

import { getCountry,getCategories,getHolidayList,updateBaseline,customizeHoliday ,deleteHoliday } from '@/services';
import { ExclamationCircleOutlined,DownOutlined ,UpOutlined  } from '@ant-design/icons';
import AddHoliday from './Component/addHoliday'
import ImportHoliday from './Component/importHoliday'
import storage from '@/utils/storage';
import { localStorageEnum } from '@/enums/storageEnum';
import { BaseUrlEnum } from '@/enums/httpEnum'
import axios from 'axios';
const { RangePicker } = DatePicker;
const { confirm } = Modal;


const FestivalHolidayManage = () => {
//////////////开始
const [name, setName] = useState('');
const [countryCode, setCountryCode] = useState('CN');
const [type, setType] = useState('官方');
const [date, setDate] = useState([]);
const [countryList, setCountryList] = useState([]);
const [isAddOpen, setIsAddOpen] = useState(false);
const [data, setData] = useState([]);
const linkList = [
  {
    title: '节假日管理'
  }
]
const param = {};
const [expandedRowKeys, setExpandedRowKeys] = useState([]);
const [isImportOpen, setIsImportOpen] = useState(false);
const [paramList, setParamList] = useState([]);
const [isImport, setIsImport] = useState(false);
const [importData, setImportData] = useState({});
const [fileList, setFileList] = useState([]);
const [showImportInfo, setShowImportInfo] = useState(false);
const [dataNew, setDataNew] = useState([]);

  ////////////////////////////开始
  useEffect(() => {
    getCountryList()
  }, []);
  useEffect(() => {
    getHolidayData()
  }, [name,countryCode,type,date]);
  useEffect(() => {
    if(data.length > 0 ){
      data.map(item => {
        const found = countryList.find(country => country.countryCode === item.countryCode);
        item.countryName = found?.countryName
        if(item.holidayInfoList.length > 0){
          item.holidayInfoList.forEach(element => {
            const info = countryList.find(country => country.countryCode === element.countryCode);
            element.countryName = info?.countryName
          });
          // const foundList = item.holidayInfoList.find(country => country.countryCode === item.countryCode);
          // item.countryName = foundList?.countryName
        }
       
      }); 
    }
    setDataNew([...data])
  }, [countryList,data]);
    //获取节日列表
    const getHolidayData = () => {
      const dataParam = {
        name:name,
        countryCode:countryCode,
        type:type?type:'',
        startDate:date[0],
        endDate:date[1]

      }
      getHolidayList(dataParam).then((res) => {
        if(res){
          const list = res || [];
          setData(list)
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
    //获取国家
    const getCountryList = () => {
      getCountry().then((res) => {
        if(res){
          setCountryList(res || [])
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
    //创建假日
   const handleCreateHoliday = (param) => {
    const paramData = {
      countryCode:param.countryCode,
      name:param.name,
      type:param.type,
      startDate:param.startDate,
      endDate:param.endDate
    }
    customizeHoliday(paramData).then((res) => {
      if(res){
        message.success("创建成功")
        setIsAddOpen(false)
        getHolidayData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
    
  }
  //删除节假日
  const showDelConfirm = (item,type) => {
    confirm({
      title: '删除?',
      icon: <ExclamationCircleOutlined />,
      content: `确定删除节假日 ${item.nameCn}？`,
      onOk() {
        handleDelete(item,type)
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };

  const handleDelete = (item,type) => {
    let list = [];
    if(type === 'parent'){
      list = item.holidayInfoList?.map(item => item.date);
    }else{
      list.push(item.date)
    }
    const param = {
      countryCode:item.countryCode,
      date:list.join(',')
    }
    deleteHoliday(param).then((res) => {
      if(res){
        message.success("删除成功")
        getHolidayData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  

    // 父级表格列配置
    const parentColumns = [
      {
        title: '名称',
        dataIndex: 'nameCn',
        key: 'nameCn',
        render: (text, record) => (
          <div 
            style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}
            onClick={() => handleExpand(record.date)}
          >
            <span style={{ marginRight: 8 }}>{text}</span>
            {expandedRowKeys.includes(record.date) ? 
              <UpOutlined style={{ fontSize: 12 }} /> : 
              <DownOutlined style={{ fontSize: 12 }} />
            }
          </div>
        ),
      },
      {
        title: '日期范围',
        dataIndex: 'period',
        key: 'period',
      },
      {
        title: '国家',
        dataIndex: 'countryName',
        key: 'countryName',
        render: (_, record) => (
          <Space size="middle">
            <span>{record.countryName}({record.countryCode})</span>
          </Space>
        ),
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record) => (
          <Space size="middle">
            <a onClick={()=>{showDelConfirm(record,'parent')}}>删除</a>
          </Space>
        ),
      },
    ];
  
    // 子级表格列配置
    const childColumns = [
      {
        title: '日期',
        dataIndex: 'date',
        key: 'date',
      },
      {
        title: '类型',
        dataIndex: 'type',
        key: 'type',
      },
      {
        title: '国家',
        dataIndex: 'countryName',
        key: 'countryName',
        render: (_, record) => (
          <Space size="middle">
            <span>{record.countryName}({record.countryCode})</span>
          </Space>
        )
      },
      
      {
        title: '更新时间',
        dataIndex: 'updateTime',
        key: 'updateTime',
      },
      {
        title: '操作',
        key: 'action',
        render: (_, record) => (
          <Space size="middle">
            <a onClick={()=>{showDelConfirm(record,'child')}}>删除</a>
          </Space>
        ),
      }
    ];
  
    // 处理展开/收起
    const handleExpand = (date) => {
      setExpandedRowKeys(prevKeys => {
        if (prevKeys.includes(date)) {
          return prevKeys.filter(key => key !== date);
        } else {
          return [...prevKeys, date];
        }
      });
    }
  const onChange = (dates, dateStrings) => {
    const start = dates?dates[0].format('YYYY-MM-DD'):'';
      const end =dates?dates[1].format('YYYY-MM-DD'):'';
    setDate([start, end])
  };
  const addHoliday = (value) => {
    setIsAddOpen(true)
  };
  const importHolidayBefore = (value) => {
    setImportData({})
    setShowImportInfo(false)
    setIsImportOpen(true)
  };

    const beforeUpload = async (file) => {
      const isExcel = file.type === 'application/vnd.ms-excel' || file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isExcel) {
        message.error('只能上传 Excel 文件!');
        return false;
      }
      try {
        const formData = new FormData();
        formData.append('file', file);
        const response = await axios.post(`${BaseUrlEnum.BASEHOLIDAY}/api/import/check?type=1`, formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          }
        });
        const importData = response.data.data;
        const newArray = importData.successList?.map(item => item.importFieldValueDTOList[0])
        param.list = newArray;
          setParamList(newArray)
          setImportData(importData)
          setIsImport(importData.failedList.length === 0)
          setShowImportInfo(true)
          if(importData.failedList.length > 0){
            message.error('导入失败，文件中存在错误数据！',5);
          }
          return importData.failedList.length === 0
          
      } catch (error) {
        console.log(error)
        message.error(error);
        setShowImportInfo(false);
        return false
      }
    };
    const customRequest = async ({ file, onSuccess, onError }) => {
      if(param.list.length === 0){
        return;
      }
      try {
        const response = await axios.post(`${BaseUrlEnum.BASEHOLIDAY}/api/import/data`, {importVOs: param.list,type:1})
        onSuccess(response.data, file);
        message.success('上传成功');
        setTimeout(() => {
          setIsImportOpen(false)
        }, 5000);
        
        getHolidayData()

      } catch (error) {
        onError(error);
        message.error('上传失败');
      }
    };
    
    const props = {
      name: 'file',
      action: `${BaseUrlEnum.BASEHOLIDAY}/api/import/data?type=1`,
      maxCount: 1,
      data: {
        // 这里是额外参数
        importVOs: paramList
      },
      onChange(info) {
          if (info.file.status === 'done') {
    
              if (info.file.response.code === 201) {
                  console.log(info.file.name);
                  message.success('导入成功');
                  setFileList(info.fileList);
                  setTimeout(() => {
                    setIsImportOpen(false)
                  }, 5000);
                  
              } else {
                  message.success('导入成功');
                  setTimeout(() => {
                    setIsImportOpen(false)
                  }, 5000);
                 
              }
          }else if (info.file.status === 'error') {
              message.error('导入失败');
          }
          if (isImport) {
            setFileList(info.fileList);
        }
      },
      fileList: fileList,
      beforeUpload: beforeUpload,
  };
  const onSearch = (value) => {
    console.log('search:', value);
  };
  const filterOption = (input, option) => {
    return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
  };
  



  return (
    <div className="festivalHoliday ">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div  className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header">
          <div className="middleware-base-info">
          <Row>
            <div className='flex-app' style={{marginBottom:'25px'}}>
                <div className='header-icon'></div>
                <div>节假日列表</div>
            </div>
          </Row>
            <Row>
                <Col span={24} style={{display:'flex',justifyContent:'space-between'}}>
                   <Space>
                    <Button style={{margin:'0px 5px'}} type='primary' onClick={()=>{addHoliday()}}>新增节假日</Button>
                    <Button style={{margin:'0px 5px'}} type='primary' onClick={()=>{importHolidayBefore()}}>导入</Button>
                
                   </Space>
                    <div className='mySubscriptions-search'>
                    <Space>
                        <Input placeholder="输入节假日名称" style={{width:'200px'}} value={name} onChange={(e)=>{setName(e.target.value)}} allowClear />
                        <Select onChange={(value)=>{setCountryCode(value)}} value={countryCode} style={{width:'200px'}} placeholder="国家" showSearch  onSearch={onSearch}
                        filterOption={filterOption}>
                        {countryList.map(item => (
                          <Option key={`${item.countryCode}`} value={item.countryCode}>{`${item.countryName}(${item.countryCode})`}</Option>
                        ))}
                      </Select>
                        <Select onChange={(value)=>{setType(value)}} style={{width:'200px'}} placeholder="分类" allowClear value={type}>
                          <Option key={'官方'} value={"官方"}>官方</Option>
                          <Option key={'自定义'} value={"自定义"}>自定义</Option>
                        </Select>
                     
                    <RangePicker  format={"YYYY-MM-DD"}   onChange={onChange}/>
                    </Space>
                       
                    </div>
                </Col>
                
            </Row>
          
          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div>
              <Table
              columns={parentColumns}
              dataSource={dataNew}
              rowKey="date"
              
              pagination={false}
              expandable={{
                expandedRowRender: (record) => (
                  <Table
                    columns={childColumns}
                    dataSource={record.holidayInfoList}
                    rowKey="id"
                    pagination={false}
                    bordered
                    rowClassName={"child-table-color"}
                  />
                ),
                expandedRowKeys,
                onExpand: (expanded, record) => {
                  handleExpand(record.date);
                },
                expandIcon: () => null, // 隐藏默认展开图标
              }}
            />
          </div>
        </div>
      </div>
      {
        isAddOpen && <AddHoliday setIsAddOpen={setIsAddOpen} isAddOpen={isAddOpen} handleCreateHoliday={handleCreateHoliday} countryList={countryList}/>
       }
       {
        isImportOpen && <ImportHoliday showImportInfo={showImportInfo} 
        beforeUpload={beforeUpload} customRequest={customRequest}
        setShowImportInfo={setShowImportInfo} setIsImportOpen={setIsImportOpen} isImportOpen={isImportOpen} props={props} isImport={isImport} importData={importData}/>
       }
     
    </div>
  );
};



export default FestivalHolidayManage;