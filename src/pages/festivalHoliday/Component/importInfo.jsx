import React, { useEffect, useState } from 'react';

import { BaseUrlEnum } from '@/enums/httpEnum'
import {Modal ,List ,Tabs  ,DatePicker,Col, Row,Upload ,message } from 'antd';
import axios from 'axios';
const options = [];
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

import { FileExcelOutlined,UploadOutlined} from '@ant-design/icons';
import { importContentRule,checkContent } from '@/services';


const ImportInfo = ({importDataInfo,showImportInfo,setShowImportInfo}) => {

const [isJpgOrPng, setIsJpgOrPng] = useState(false);
const [importData, setImportData] = useState([]);
const onChange = (key) => {
    console.log(key);
  };
const items = [
    {
      key: '1',
      label: '全部数据',
      children: 'Content of Tab Pane 1',
    },
    {
      key: '2',
      label: '失败数据',
      children: 'Content of Tab Pane 2',
    }
]
 
  return (
    <div>
        <Modal
            title="导入数据"
            open={showImportInfo}
            onCancel={()=>{ setShowImportInfo(false)}}
            onOk={()=>{setShowImportInfo(false)}}
            footer={null}
        >
        <div style={{maxHeight:'500px'}}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="成功数据" key="1">
                <List
                    itemLayout="horizontal" // 布局方式：水平或垂直
                    dataSource={[]} // 数据源
                    renderItem={item => (
                    <List.Item>
                        <List.Item.Meta
                        title={"名称"} // 标题部分显示name
                        description={item.name} // 描述部分显示rule
                        />
                    </List.Item>
                    
                    )}
                />
            </TabPane>
            <TabPane tab="失败数据" key="2">
                <List
                itemLayout="horizontal" // 布局方式：水平或垂直
                dataSource={[]} // 数据源
                renderItem={item => (
                <List.Item>
                    <List.Item.Meta
                    title={"名称"} // 标题部分显示name
                    description={item.name} // 描述部分显示rule
                    />
                    <List.Item.Meta
                    title={"失败原因"} // 标题部分显示name
                    description={item.rule} // 描述部分显示rule
                />
            
                </List.Item>
                
                )}
            />
            </TabPane>
        </Tabs>
        </div>
        </Modal>
    </div>
  );
};



export default ImportInfo;