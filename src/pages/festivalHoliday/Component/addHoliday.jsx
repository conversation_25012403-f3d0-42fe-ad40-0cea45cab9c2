import React, { useEffect, useState } from 'react';


import {Modal ,Input,Button ,Space ,DatePicker,Col, Row,Select,Form } from 'antd';

const options = [];
const { RangePicker } = DatePicker;



const AddHoliday = ({isAddOpen,setIsAddOpen,handleCreateHoliday,countryList}) => {
const [form] = Form.useForm();
const [date, setDate] = useState([]);
  const onFinish = (values) => {
    values.startDate = date[0]
    values.endDate = date[1]
    handleCreateHoliday(values)
    
  };

  const onReset = () => {
    setIsAddOpen(false);
  };
  const onChange = (dates, dateStrings) => {
    const start = dates[0].format('YYYY-MM-DD');
      const end = dates[1].format('YYYY-MM-DD');
    setDate([start, end])
  };
  const onSearch = (value) => {
    console.log('search:', value);
  };
  const filterOption = (input, option) => {
    return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
  };
 
  return (
    <div>
        <Modal
            title="新增节假日"
            open={isAddOpen}
            onCancel={()=>{ setIsAddOpen(false)}}
            onOk={()=>{setIsAddOpen(false)}}
            footer={null}
            
        >
        <Form
        
            form={form}
            name="control-hooks"
            onFinish={onFinish}
            style={{width:'100%'}}
            layout='vertical'
            className="addBaseline"
            
        >
            <Form.Item name="name" label="节假日名称" rules={[{ required: true }]} >
                <Input placeholder='请填写节假日名称' maxLength={20}  />
            </Form.Item>
            <Form.Item name="type" label="节假日类型" rules={[{ required: true, message: '请选择节假日类型'  }]} >
                <Select
                    placeholder="请选择"
                    
                    
                >
                    <Option key={'官方'} value={"官方"}>官方</Option>
                    <Option key={`自定义`} value={'自定义'}>自定义</Option>
                </Select>
            </Form.Item>
            <Form.Item name="countryCode" label="国家" rules={[{ required: true,message: '请选择国家' }]} >
                <Select   placeholder="国家"  showSearch  onSearch={onSearch} filterOption={filterOption}>
                    {countryList.map(item => (
                    <Option key={`${item.countryCode}`} value={item.countryCode}>{`${item.countryName}(${item.countryCode})`}</Option>
                    ))}
                </Select>
            </Form.Item>
            <Form.Item name="date" label="时间范围" rules={[{ required: true,message: '请选择时间范围' }]} >
                <RangePicker format={"YYYY-MM-DD"}   onChange={onChange} style={{width:'100%'}}/>
            </Form.Item>
          
            <Form.Item style={{textAlign:'right',marginTop:'35px'}}>
                <Space>
                    <Button htmlType="button" onClick={onReset}>
                    取消
                    </Button>
                    <Button type="primary" htmlType="submit">
                    确定
                    </Button>
                   
                </Space>
            </Form.Item>
        </Form>
        </Modal>
    </div>
  );
};



export default AddHoliday;