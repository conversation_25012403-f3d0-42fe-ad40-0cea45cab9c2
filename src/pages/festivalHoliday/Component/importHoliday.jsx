// import React, { useEffect, useState } from 'react';

// import { BaseUrlEnum } from '@/enums/httpEnum'
// import {Modal ,List,Button ,Space ,DatePicker,Col, Tabs,Upload ,message } from 'antd';
// import axios from 'axios';


// const { TabPane } = Tabs;
// import { FileExcelOutlined,UploadOutlined} from '@ant-design/icons';
// import { importContentRule,checkContent } from '@/services';


// const ImportHoliday = ({isImportOpen,setIsImportOpen,props,isImport,importData,showImportInfo,setShowImportInfo}) => {
// const [importContentList, setImportContentList] = useState([ {
//   "alias": "string",
//   "code": "string",
//   "name": "string",
//   "required": true,
//   "rule": "100个字符以内"
// },
// {
//   "alias": "string",
//   "code": "string",
//   "name": "string",
//   "required": true,
//   "rule": "100个字符以内"
// }
// ]);

// // const [showImportInfo, setShowImportInfo] = useState(false);
// const [activeTab, setActiveTab] = useState("1");
// // const [paramList, setParamList] = useState([]);
// // const [fileList, setFileList] = useState([]);
// // const [isImport, setIsImport] = useState(false);
// // const [importData, setImportData] = useState({});



//   const  downLoad = () => {
//     window.open(`${BaseUrlEnum.BASEHOLIDAY}/api/import/template?type=1`,'_blank')
//   }
// //获取填写规则
//   const getImportContent= () => {
//     importContentRule({type:'1'}).then((res) => {
//       if(res){
//         setImportContentList(res || [])
//       }else{
//         message.error(res?.message || res?.msg)
//       }
//     }).catch((data) => {
//       message.error(data?.msg)
//     })
//   }

// // const beforeUpload = (file) => {
// //   const formData = new FormData();
// //     formData.append('file', file);
// //       axios.post(`${BaseUrlEnum.BASEHOLIDAY}/api/import/check?type=1`, formData, {
// //         headers: {
// //           'Content-Type': 'multipart/form-data',
// //         },
// //       })
// //       .then((response) => {
// //         const importData = response.data;
// //         const newArray = importData.successList.map(item => item.importFieldValueDTOList[0])
// //         setParamList(newArray)
// //         setImportData(importData)
// //         setShowImportInfo(true)
// //         // if(importData.failedList.length === 0){//没有失败的数据
// //         //   setIsImport(true)
// //         // }else{
// //         //   setIsImport(false)
// //         // }
// //         setIsImport(importData.failedList.length === 0)
// //         return importData.failedList.length === 0
// //       })
// //       .catch((error) => {
// //         message.error(error)
// //       });

// // };
// //   const props = {
// //     name: 'file',
// //     action: `${BaseUrlEnum.BASEHOLIDAY}/api/import/data?type=1`,
// //     maxCount: 1,
// //     data: {
// //       // 这里是额外参数
// //       importVOs: paramList
// //     },
// //     onChange(info) {
// //         if (info.file.status === 'done') {
  
// //             if (info.file.response.code === 201) {
// //                 console.log(info.file.name);
// //                 message.success('导入成功');
// //             } else {
// //                 message.success('导入成功');
// //             }
// //         }
// //         if (info.file.status === 'error') {
// //             message.error('导入失败');
// //         }
       
// //     },
// //     fileList: fileList,
// //     beforeUpload: beforeUpload,
// // };
//   useEffect(() => {
//     getImportContent()
//   }, []);
 
//   return (
//     <div>
//         <Modal
//             title="模板导入"
//             open={isImportOpen}
//             onCancel={()=>{ setIsImportOpen(false)}}
//             onOk={()=>{setIsImportOpen(false)}}
//             footer={null}
            
            
//         >
//         {
//           !showImportInfo?  <div style={{maxHeight:'500px'}}>
//           <Button onClick={()=>{downLoad()}} type='primary'>下载模板<FileExcelOutlined  /></Button> 
//           <div style={{maxHeight:'300px',overflow:'auto'}}>
//               <List
//               itemLayout="horizontal" // 布局方式：水平或垂直
//               dataSource={importContentList} // 数据源
//               renderItem={item => (
//                 <List.Item>
//                   <List.Item.Meta
//                     title={"填写规则属性"} // 标题部分显示name
//                     description={item.name} // 描述部分显示rule
//                   />
//                   <List.Item.Meta
//                   title={"填写规则"} // 标题部分显示name
//                   description={item.rule} // 描述部分显示rule
//                 />
//                 <List.Item.Meta
//                 title={"是否必填"} // 标题部分显示name
//                 description={item.required?'是':'否'} // 描述部分显示rule
//               />
//                 </List.Item>
                
//               )}
//             />
//           </div>
//       </div>:
//       <div>
//       <Tabs activeKey={activeTab} onChange={setActiveTab}>
//         <TabPane tab={`成功数据${importData.successList?.length}`} key="1">
//             <List
//                 itemLayout="horizontal" // 布局方式：水平或垂直
//                 dataSource={importData.successList} // 数据源
//                 renderItem={item => (
//                 <List.Item>
//                     <List.Item.Meta
//                     title={"名称"} // 标题部分显示name
//                     description={item.title} // 描述部分显示rule
//                     />
//                 </List.Item>
                
//                 )}
//             />
//         </TabPane>
//         <TabPane tab={`失败数据${importData.failedList?.length}`} key="2">
//             <List
//             itemLayout="horizontal" // 布局方式：水平或垂直
//             dataSource={importData.failedList} // 数据源
//             renderItem={item => (
//             <List.Item>
//                 <List.Item.Meta
//                 title={"名称"} // 标题部分显示name
//                 description={item.title} // 描述部分显示rule
//                 />
//                 <List.Item.Meta
//                 title={"失败原因"} // 标题部分显示name
//                 description={item.desc} // 描述部分显示rule
//             />
//             </List.Item> 
//             )}
//         />
//         </TabPane>
//     </Tabs>
//   </div> 
//         }
      
//         <Space>
//           <Button type='primary' onClick={()=>{setShowImportInfo(false)}} style={{display:showImportInfo?'block':'none'}}>上一步</Button>
//           <Upload {...props}>
//             <Button icon={<UploadOutlined />} type='primary'>上传文件</Button>
//           </Upload>
//         </Space>
      
//         </Modal>
   
       
//     </div>
//   );
// };



// export default ImportHoliday;
import React, { useEffect, useState } from 'react';

import { BaseUrlEnum } from '@/enums/httpEnum'
import {Modal ,List,Button ,Space ,DatePicker,Col, Tabs,Upload ,message } from 'antd';
import axios from 'axios';


const { TabPane } = Tabs;

import { FileExcelOutlined,UploadOutlined,DownloadOutlined} from '@ant-design/icons';
import { importContentRule,checkContent } from '@/services';


const ImportHoliday = ({isImportOpen,setIsImportOpen,props,isImport,importData,showImportInfo,setShowImportInfo,beforeUpload,customRequest}) => {
const [importContentList, setImportContentList] = useState([]);

// const [showImportInfo, setShowImportInfo] = useState(false);
const [activeTab, setActiveTab] = useState("1");
  // const  downLoad = () => {
  //   window.open(`${BaseUrlEnum.BASEHOLIDAY}/api/import/template?type=1`,'_self')
  // }
  const downLoad = async () => {
    try {
      const response = await axios.get(`${BaseUrlEnum.BASEHOLIDAY}/api/import/template?type=1`, {
        responseType: 'blob', // 重要！告诉axios返回Blob对象
      });

      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '节假日导入模板.xlsx');
      document.body.appendChild(link);
      link.click();
      
      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
      // 错误处理
    }
  };
//获取填写规则
  const getImportContent= () => {
    importContentRule({type:'1'}).then((res) => {
      if(res){
        setImportContentList(res || [])
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }

  useEffect(() => {
    getImportContent()
  }, []);
 
  return (
    <div>
        <Modal
            title="模板导入"
            open={isImportOpen}
            onCancel={()=>{ setIsImportOpen(false)}}
            onOk={()=>{setIsImportOpen(false)}}
            footer={null}
            width={600}
            forceRender={false} 
            
            
        >
        {
          !showImportInfo?  <div style={{maxHeight:'500px'}}>
          <Button onClick={()=>{downLoad()}} type='primary'>下载模板<DownloadOutlined /></Button> 
          <div style={{maxHeight:'300px',overflow:'auto'}}>
              <List
              itemLayout="horizontal" // 布局方式：水平或垂直
              dataSource={importContentList} // 数据源
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    title={"填写规则属性"} // 标题部分显示name
                    description={item.name} // 描述部分显示rule
                  />
                  <List.Item.Meta
                  title={"填写规则"} // 标题部分显示name
                  description={item.rule} // 描述部分显示rule
                  style={{width:'200px'}}
                />
                <List.Item.Meta
                title={"是否必填"} // 标题部分显示name
                description={item.required?'是':'否'} // 描述部分显示rule
              />
                </List.Item>
                
              )}
            />
          </div>
      </div>:
      <div>
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={`校验成功数据${importData.successList?.length}`} key="1">
            <List
                itemLayout="horizontal" // 布局方式：水平或垂直
                dataSource={importData.successList} // 数据源
                renderItem={item => (
                <List.Item>
                    <List.Item.Meta
                    title={"数据所在行号"} // 标题部分显示name
                    description={item.title} // 描述部分显示rule
                    />
                </List.Item>
                
                )}
            />
        </TabPane>
        <TabPane tab={`校验失败数据${importData.failedList?.length}`} key="2">
            <List
            itemLayout="horizontal" // 布局方式：水平或垂直
            dataSource={importData.failedList} // 数据源
            renderItem={item => (
            <List.Item>
                <List.Item.Meta
                title={"数据所在行号"} // 标题部分显示name
                description={item.title} // 描述部分显示rule
                />
                <List.Item.Meta
                title={"失败原因"} // 标题部分显示name
                description={item.desc} // 描述部分显示rule
            />
            </List.Item> 
            )}
        />
        </TabPane>
    </Tabs>
  </div> 
        }
      
        <Space style={{display: 'flex',alignItems:'baseline',paddingTop:'10px',borderTop:'1px solid #ddd'}}>
          <Button type='primary' onClick={()=>{setShowImportInfo(false)}} style={{display:showImportInfo?'block':'none'}}>上一步</Button>
          <Upload  beforeUpload={beforeUpload}
          customRequest={customRequest} 
          maxCount={1}
          >
            <Button icon={<UploadOutlined />} type='primary'>上传文件</Button>
          </Upload>
        </Space>
      
        </Modal>
   
       
    </div>
  );
};



export default ImportHoliday;