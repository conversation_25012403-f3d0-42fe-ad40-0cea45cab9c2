.festivalHoliday {
  
    .detail-header {
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;
  
      .nav-path {
        span {
          color: #666;
          margin: 0 4px;
          font-size: 14px;
  
          &:first-child {
            margin-left: 0;
          }
        }
      }
  
      .back-button {
        cursor: pointer;
        color: #1890ff;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;
  
        &:hover {
          opacity: 0.8;
        }
      }
    }
  
    // 响应式调整
    @media screen and (min-width: 1920px) {
      .dealPadding {
        padding: 24px calc((100vw - 81%) / 2);
      }
    }
  
    // 响应式调整
    @media screen and (max-width: 1920px) {
      .dealPadding {
        padding: 24px 120px;
      }
    }
  
    // 响应式调整
    @media screen and (max-width: 1800px) {
      .dealPadding {
        padding: 24px calc((100vw - 81%) / 2);
      }
    }
     // 响应式调整
     @media screen and (max-width: 1700px) {
      .dealPadding {
        padding: 24px calc((100vw - 86%) / 2);
      }
    }
  
    @media screen and (max-width: 1600px) {
      .dealPadding {
        padding: 24px calc((100vw - 1200px) / 2);
      }
    }
  
    @media screen and (max-width: 1200px) {
      .dealPadding {
        padding: 24px 40px;
      }
    }
  
    @media screen and (max-width: 768px) {
      .dealPadding {
        padding: 24px 20px;
      }
  
      .home-header {
        padding: 24px;
  
        h1 {
          font-size: 24px;
        }
  
        .description {
          font-size: 14px;
        }
      }
    }
    .nav-path-ui{
      padding-top: 8px;
      padding-bottom: 8px;
      background-color: #fff;
    }
    
    .middleware-header {
      background-color: #fff;
      width: 100%;
      height: 120px;
      margin: 8px;
      .middleware-base-info-api {
        display: flex;
        gap: 20px;
        padding-top: 15px;
        padding-left: 50px;
        .middleware-logo-api {
          width: 150px;
          height: 150px;
         
        }
        .logo-title {
          display: flex;
          align-items: center;
          gap: 16px;
  
         
  
          .title-wrapper {
            .title {
              font-size: 20px;
              color: #333;
              font-weight: 500;
              line-height: 1.4;
            }
  
            .subtitle {
              font-size: 14px;
              color: #666;
              margin: 10px 0px;
            }
          }
        }
  
        .action-buttons {
          display: flex;
          gap: 16px;
          margin-top: 8px;
  
          .action-btn {
            padding: 6px 16px;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            border: none;
            background: none;
            transition: all 0.3s;
  
            &.primary {
              background: #1890ff;
              color: #fff;
  
              &:hover {
                background: #40a9ff;
              }
            }
  
            &:not(.primary):not(.link) {
              background: #fff;
              border: 1px solid #d9d9d9;
              color: #333;
  
              &:hover {
                border-color: #1890ff;
                color: #1890ff;
              }
            }
  
            &.link {
              color: #1890ff;
              padding: 6px 0;
  
              &:hover {
                opacity: 0.8;
              }
            }
          }
        }
      }
    }
  
    .tabs-container {
      border-bottom: 1px solid #f0f0f0;
      background: #fff;
  
      .ant-tabs {
        .ant-tabs-nav {
          margin: 0;
          padding: 0;
          
          &::before {
            display: none;
          }
  
          .ant-tabs-nav-wrap {
            padding: 0 24px;
          }
  
          .ant-tabs-tab {
            padding: 12px 0;
            margin: 0 24px 0 0;
            font-size: 14px;
            color: #666;
            transition: all 0.3s;
  
            &:hover {
              color: #1890ff;
            }
  
            &.ant-tabs-tab-active {
              .tab-text {
                color: #1890ff;
                font-weight: 500;
              }
            }
  
            .tab-item {
              position: relative;
              
              .tab-text {
                position: relative;
                z-index: 1;
              }
            }
          }
  
          .ant-tabs-ink-bar {
            background: #1890ff;
            height: 2px;
          }
        }
  
        .ant-tabs-content {
          padding: 24px;
        }
      }
    }
  
    .info-section {
      width: calc(100% - 20px);
      height: calc(100% - 208px);
      min-height: 400px;
      background-color: #fff;
      padding: 16px;
      margin: 8px;
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
        position: relative;
        padding-left: 12px;
  
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background: #1890ff;
          border-radius: 2px;
        }
      }
  
      .info-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        padding: 24px;
        border-radius: 4px;
  
        .info-item {
          display: flex;
          align-items: flex-start;
  
          .label {
            width: 120px;
            color: #666;
            flex-shrink: 0;
          }
  
          .value {
            color: #333;
          }
  
          .status-tag {
            display: inline-flex;
            align-items: center;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 14px;
            gap: 6px;
  
            &.normal {
              background: rgba(82, 196, 26, 0.1);
              color: #52c41a;
  
              .dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #52c41a;
              }
            }
  
            &.warning {
              background: rgba(250, 173, 20, 0.1);
              color: #faad14;
  
              .dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #faad14;
              }
            }
  
            &.error {
              background: rgba(255, 77, 79, 0.1);
              color: #ff4d4f;
  
              .dot {
                width: 6px;
                height: 6px;
                border-radius: 50%;
                background-color: #ff4d4f;
              }
            }
          }
        }
      }
      .text-overflow-box{
        width: 220px;
      }
      .text-overflow{
        max-width: 200px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  
    .helm-section {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
        position: relative;
        padding-left: 12px;
  
        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4px;
          height: 16px;
          background: #1890ff;
          border-radius: 2px;
        }
      }
  
      .helm-content {
        border-radius: 4px;
        padding: 16px;
        min-height: 400px;
        color: #fff;
  
        .file-explorer {
          display: flex;
          gap: 20px;
          background: #1e1e1e;
          border-radius: 4px;
          padding: 16px;
          min-height: 600px;
  
          .file-tree-container {
            flex: 0 0 250px;
            min-width: 250px;
            border-right: 1px solid #333;
            padding-right: 16px;
            overflow-y: auto;
            height: 600px;
  
            .file-tree {
              list-style: none;
              padding: 0;
              margin: 0;
              font-size: 14px;
              color: #d4d4d4;
  
              li {
                padding: 4px 0;
                cursor: pointer;
                white-space: nowrap;
  
                &:hover {
                  color: #fff;
                }
  
                &.folder {
                  > span {
                    font-weight: bold;
                    color: #dcdcaa;
                  }
  
                  > ul {
                    margin-left: 16px;
                  }
                }
  
                &.file {
                  color: #9cdcfe;
  
                  &:hover {
                    text-decoration: underline;
                  }
                }
              }
            }
          }
  
          .file-content {
            flex: 1;
            min-width: 0;
            background: #1e1e1e;
            border-radius: 4px;
            height: 600px;
            overflow: hidden;
  
            .no-file-selected {
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #666;
              font-size: 14px;
            }
  
            .cm-editor {
              height: 100%;
              .cm-scroller {
                padding: 8px;
                overflow: auto;
                max-height: 600px;
              }
              .cm-content {
                min-height: 600px;
              }
            }
          }
        }
      }
    }
    .mySubscriptions-search{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
    
    }
    .middleware-base-info{
      padding: 15px 20px;
    }
   
  } 
  .addBaseline{
    width: 100px;
    :where(.css-dev-only-do-not-override-2xxe2r).micro-form-item{
      margin-bottom: 10px !important;
    }
 
  }
  .child-table-color{
      background-color: #f0f0f0; /* 浅灰色 */
    }
  
  
  