.sidebar {
  width: @sidebar-width;
  height: 100%;
  display: flex;
  flex-direction: column;
	background: #ffffff;
	box-shadow: 0px 2px 8px 0px #cfcfd2;
	transition: width 0.2s ease-in-out;
  z-index: 10;
	&.sidebar-temp-expand {
		width: @sidebar-width !important;
		background: rgba(247, 248, 250, 1);
		box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.1);
		.menus {
			padding-top: 10px !important;
			li {
				width: 184px!important;
				// background: none!important;
				div.active {
					// background: none!important;
				}
				.child {
					display: block !important;
				}
				.parent {
					width: 100% !important;
					padding-left: 16px !important;
					padding-right: 16px !important;
					align-items: center;
					& > span.anticon {
						display: block !important;
					}
					& > p {
						display: block !important;
					}
				}
			}
		}
		.micro-menu-sub:not(.micro-menu-hidden) {
			display: block !important;
		}
		.micro-menu-submenu-arrow {
			display: block !important;
		}
	}
	&.sidebar-contract {
		width: 68px;
		overflow: hidden;
		z-index: 99;
		.menus {
			li {
				width: 44px;
				padding: 0 !important;
				display: flex;
				justify-content: center;
				align-items: center;
				& .micro-menu-title-content{
					display: none;
				}
				&.micro-menu-submenu-selected {
					.micro-menu-submenu-title {
						background-color: rgba(40, 105, 246, 0.1);
					}
				}
				&.micro-menu-submenu-open {
					.micro-menu-submenu-title {
						color: rgba(0, 0, 0, 0.65);
						background: #f7f8fa;
					}
				}
				&.active {
					background: #f0f8ff;
				}
				div.active {
					background: #f0f8ff;
				}
				.child {
					display: none;
				}
				.parent {
					width: 40px;
					padding: 0;
					justify-content: center;
					& > span.anticon {
						display: none;
					}
					& > p {
						display: none;
					}
				}
			}
			.micro-menu-sub {
				display: none;
			}
			.micro-menu-submenu-arrow {
				display: none;
			}
		}
	}
  .sidebar-content {
    width: 100%;
    height: 100%;
    overflow: hidden;
    padding: 12px 12px 0 12px;
    display: flex;
    flex-direction: column;
    .menus {
      width: 100%;
      flex: 1;
      overflow-y: auto;
      .micro-menu {
        font-size: 14px;
        li {
          border-radius: 4px;
					margin: 4px 0 !important;
          &.micro-menu-submenu-selected {
            .micro-menu-submenu-title {
              color: rgba(40, 105, 246, 1);
              background-color: rgba(40, 105, 246, 0.1);
            }
          }
          &.micro-menu-submenu-open {
            .micro-menu-submenu-title {
              color: rgba(0, 0, 0, 0.65);
              background: #f7f8fa;
            }
            .micro-menu-submenu-title:hover {
              color: rgba(0, 0, 0, 0.65);
              background: rgba(0, 0, 0, 0.03);
            }
          }
          &.micro-menu-submenu {
            padding-left: 0 !important;
            .micro-menu-submenu-title {
              width: 100%;
              border-radius: 4px;
              margin-left: 0;
              padding-left: 12px !important;
            }
            .micro-menu-item {
              padding-left: 34px !important;
            }
            .micro-menu-sub {
              li {
                margin-left: 0;
                &.micro-menu-item {
                  padding-left: 42px !important;
                }
              }
            }
          }
        }
      }
      .micro-menu-inline {
        border-inline-end: none;
      }
      .micro-menu-item:not(.micro-menu-item-selected):hover {
        color: rgba(0, 0, 0, 0.65);
        background: rgba(0, 0, 0, 0.03);
      }
      .micro-menu-item-selected {
        background: @menu-selected-bg-color;
        color: @primary-color;
        &::after {
          opacity: 0;
        }
      }
      .micro-menu-sub.micro-menu-inline {
        background: none;
      }
      .micro-menu-submenu-title:hover {
        color: rgba(0, 0, 0, 0.65);
        background: rgba(0, 0, 0, 0.03);
      }
      .svgIcon {
        svg {
          width: 20px;
          height: 20px;
          vertical-align: middle;
        }
      }
      .menuIcon {
        font-size: 20px;
        line-height: 0;
      }
    }
  }
  .toggle-box {
    display: flex;
    background: #f9fdff;
    height: 32px;
    font-size: 15px;
    align-items: center;
    justify-content: center;
    width: 100%;
    .iconBox {
      height: 40px;
      width: 44px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      color: #000;
      &:hover {
        background: rgba(0, 0, 0, 0.03);
      }
      .menuIcon {
        font-size: 20px;
        line-height: 0;
      }
    }
  }
}
.space-icon-tip {
  left: 16px !important;
}
