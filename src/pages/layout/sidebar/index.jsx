import React, { useEffect, useState, useMemo } from "react";
import { Menu, Spin } from "antd";
import ChangeAppInfo from "@/components/ChangeAppInfo";
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  ContainerOutlined
} from "@ant-design/icons";
import { useModel } from "umi";
import { flattenMenuData, jump } from "@/utils/menus"
import "./index.less";

export default function Sidebar() {
  const { sidebarMenus, sideBarExpand, setSideBarExpand, selectedKeys, setSelectedKeys, openKeys, setOpenKeys } = useModel("menu");
  const { showChangeAppInfo } = useModel('appSystemInfo')
  const [menusData, setMenusData] = useState([])

  const { nodeMap } = useMemo(
    () => flattenMenuData(sidebarMenus),
    [sidebarMenus]
  );

  useEffect(() => {
    // 菜单数据转化
    if(!sidebarMenus?.length) return
    const dealMenus = (menus) => {
      if(!menus?.length) return []
      return menus.map((menu) =>{
        if(menu?.children?.length){
          return {
            key:menu.id,
            label:menu.name,
            icon: <ContainerOutlined />,
            children: dealMenus(menu.children)
          }
        }else{
          return {
            key:menu.id,
            label:menu.name,
            title:menu.name,
            icon: <ContainerOutlined />
          }
        }
      })
    }
    const formatMenus = dealMenus(sidebarMenus)
    setMenusData(formatMenus)
  },[sidebarMenus])
  
  
  const onOpenChange = (openKeys) => {
    setOpenKeys(openKeys);
  };

  // 点击菜单跳转
  const goPath = (key) => {
    if(!key) return
    const currMenu = nodeMap.get(key)
    if(currMenu) jump(currMenu)
  }
  
  // 菜单选中回调
  const onSelect = ({ key, selectedKeys }) => {
    setSelectedKeys(selectedKeys)
    goPath(key)
  }

  return (
    <div  className={`sidebar ${sideBarExpand ? "sidebar-expand" : "sidebar-contract" } `}>
      <div
        className="sidebar-content"
      > 
        {showChangeAppInfo && <ChangeAppInfo />}
        <div className="menus">
          <Menu
            mode="inline"
            selectedKeys={selectedKeys}
            openKeys={openKeys}
            onOpenChange={onOpenChange}
            onSelect={onSelect}
            items={menusData}
          />
        </div>
      </div>
      
      <div className="toggle-box">
        {sideBarExpand ? 
          (<div
              className="iconBox"
              onClick={() => {setSideBarExpand(false)}}
            >
            <span className="menuIcon">
              <MenuFoldOutlined />
            </span>
          </div>) : (
          <div
            className="iconBox"
            onClick={() => {setSideBarExpand(true)}}
          >
            <span className="menuIcon">
              <MenuUnfoldOutlined />
            </span>
          </div>
        )}
      </div>
    </div>
  )}
