import { useLocation, Outlet, useModel } from "umi";
import React, { useEffect } from "react";
import dayjs from 'dayjs';
import Header from "./header";
import Sidebar from "./sidebar";
import { removeWatermark, watermark } from '@/utils/watermark';
import "./basic.less";
import "./index.less";
import "../../../src/tailwind.css";

export default function BackLayout({isHome}) {
  const { enableWatermark, layoutConfig } = useModel("sysConfig");
  const { userInfo } = useModel('user')

  // 是否开启页面水印
  useEffect(() => {
    const userName = userInfo?.userRealname;
    const orgName = userInfo?.orgName;
    const time = dayjs(new Date()).format('MM月DD日');
    if (enableWatermark === '1') {
      if (userName && time) {
        if (orgName) {
          watermark({
            content: `${userName}-${time}-${orgName}`,
          });
        } else {
          watermark({
            content: `${userName}-${time}`,
          });
        }
      }
    }
    
    return () => {
      removeWatermark();
    }
  }, [enableWatermark, userInfo])

  return (
    <React.Fragment>
      { 
        <div className="layout">
          {!layoutConfig.hiddenHeader && <Header isHome={isHome}/>}
          <div className="main">
            {!layoutConfig.hiddenSideBar && <Sidebar />}
            <div id="sub-app-box" className="sub-app-box">
              <Outlet />
            </div>
          </div>
        </div>
      }
    </React.Fragment>
  );
}
