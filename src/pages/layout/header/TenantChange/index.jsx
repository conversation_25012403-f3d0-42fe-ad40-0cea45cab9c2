import { useEffect, useState } from "react";
import { useModel } from "umi";
import { Popover, Tree, message } from "antd";
import { SwapOutlined } from "@ant-design/icons";
import { getUserInfo, getUserDetailInfo, changeTenant } from "@/services";
import storage from "@/utils/storage";
import { localStorageEnum } from '@/enums/storageEnum';
import "./index.less";

export default function TenantChange() {
  const { userInfo, setUserInfoStore, setUserDetailInfo } = useModel("user");
  const { getAsyncMenus } = useModel('menu')
  const [tenantList, setTenantList] = useState([]);
  const USERINFO = localStorageEnum.USERINFO;
  useEffect(() => {
    if(userInfo?.tenants){
      setTenantList(userInfo?.tenants ?? []);
    }
  }, [userInfo]);

  const handleChangeTenant = async(item) => {
    if(item.id ===  userInfo.tenantId){
      return
    }
    try {
      await changeTenant(item);
      location.reload();
      // const userInfoNew = await getUserInfo();
      // const currTenantInfo = userInfoNew.tenants?.find(item => item.id === userInfoNew.tenantId)
      // if(currTenantInfo){
      //   userInfoNew.tenantCode = currTenantInfo?.code
      //   userInfoNew.tenantName = currTenantInfo?.name
      // }
      // setUserInfoStore(userInfoNew)
      // storage.setLocal(USERINFO, userInfoNew)
      // const userDetailInfoNew = await getUserDetailInfo();
      // setUserDetailInfo(userDetailInfoNew)
      // getAsyncMenus()
    } catch (error) {
      console.error("切换租户失败", error);
      message.error("切换租户失败")
    }
  };
  return userInfo && userInfo.tenants ? (
    <Popover
      trigger={["click"]}
      zIndex={5000}
      content={
        <ul>
          {
            tenantList.map((item) => (
              <li  className={userInfo && userInfo.tenantId === item.id ? "tenant-switch-item active" : "tenant-switch-item"} key={item.id} onClick={() => handleChangeTenant(item)}>
                {item.name}
              </li>
            ))
          }
        </ul>
      }
      arrow={false}
      placement="left"
      overlayClassName="header-switch-list-box organization-switch"
    >
      <div
        className="org-switch-item"
        onClick={(e) => e.stopPropagation()}
      >
        <p>{userInfo?.tenantName}</p>
        <SwapOutlined className="switch-icon" />
      </div>
    </Popover>
  ) : null;
}