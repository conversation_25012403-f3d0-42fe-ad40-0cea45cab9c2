@primary-color: #226EE7;

.noticeIcon {
    font-size: 12px;
    position: relative;
    cursor: pointer;
    // padding: 0 10px;
    img {
        width: 14px;
    }
    .messageIcon {
		width: 32px;
		height: 32px;
		display: flex;
		border-radius: 4px;
		align-items: center;
		justify-content: center;
        font-size: 20px;
		&:hover,
		&:focus {
			background: rgba(0, 0, 0, 0.03);
		}
		&:active {
			background: rgba(40, 105, 246, 0.10);
			color: #2869F6;
		}
    }
    .noticeCount {
        font-size: 0.8em;
        position: absolute;
        top: -2px;
        right: 4px;
        padding: 0 2px;
        background-color: #D04443;
        height: 12px;
        line-height: 12px;
        border-radius: 12px;
        min-width: 12px;
        box-sizing: border-box;
        color: #fff;
        text-align: center;
        font-size: 0.6em;
    }
}
.operationList {
    .operationHeader {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-bottom: 12px;
        font-size: 12px;
        .operationLeft {
            width: 180px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        .operationStatusSelect {
            background: #e6eef5;
            border-radius: 4px;
            font-size: 12px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: @primary-color;
            padding: 4px 16px;
            cursor: pointer;
        }
        .operationStatus {
            border-radius: 4px;
            font-size: 12px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #999999;
            padding: 4px 16px;
            cursor: pointer;
        }
        .operationStatus:hover {
            color: @primary-color;
            background: #e6eef5;
        }
        .operationReader {
            width: 64px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: @primary-color;
            cursor: pointer;

            div {
                display: flex;
            }

            svg {
                path {
                    fill: @primary-color;
                }
            }
        }
    }
    .operationItemBox {
        padding: 16px 0;
        border-bottom: 1px solid #eeeeee;
        &:hover {
        background: #f0f8ff;
        p {
            background: #f0f8ff !important;
        }
        }
    }
    .operationItem {
        display: flex;
        font-size: 12px;
        color: #262626;
        cursor: pointer;
        .operationItemIcon {
        width: 40px;
        height: 40px;
        border-radius: 40px;
        background: #72d996;
        font-size: 18px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        text-align: center;
        line-height: 40px;
        margin-right: 16px;
        }
        .operationItemContent {
        word-break: break-all;
        flex: 1;
        .operationItemTitle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .operationItemTitleInfo {
            display: flex;
            align-items: center;
            justify-content: space-between;
            font-family: PingFangSC-Medium, PingFang SC;
            font-size: 14px;
            color: #333;
            font-weight: 500;
            .operationItemTitleInfoText {
                max-width: 112px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            }
        }
        p {
            word-break: break-all;
            color: #666666 !important;
            span {
            color: #666666 !important;
            }
        }
        .unreadIcon {
            width: 6px;
            height: 6px;
            border-radius: 6px;
            background-color: #f00;
            margin-left: 4px;
        }
        }
        .operationItemTime {
        font-size: 12px;
        color: #999;
        }
    }
}

.noticeMessage {
    header {
        border-bottom: 1px solid #EBEBEB;
        display: flex;
        justify-content: space-between;
        h4 {
            &::before {
                border-left: 2px solid #226ee7;
                content: "";
                font-size: 12px;
                padding-right: 8px;
                vertical-align: middle;
            }
        }
    }
    main {
        max-height: 320px;
        overflow-x: hidden;
        overflow-y: auto;
        dl {
            border-bottom: 1px dashed #EBEBEB;
            padding: 4px 2px;
            &:last-child {
                border-bottom: none;
            }
            dt {
                font-weight: normal;
                padding: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
                span {
                    cursor: pointer;
                    &:hover,
                    &:focus {
                        color: #1677ff;
                    }
                }
            }
            dd {
                display: flex;
                justify-content: space-between;
                color: #979797;
                padding: 2px;
            }
        }
    }
    footer {
        border-top: 1px solid #EBEBEB;
        text-align: center;
        padding: 4px;
    }
}
.noticeTag {
    border: none;
    color: #979797;
    margin-inline-end: 0
}

