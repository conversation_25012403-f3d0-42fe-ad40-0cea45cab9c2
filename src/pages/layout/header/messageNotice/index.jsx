import locale from "@/locale";
import { useEffect, useState } from 'react';
import { Popover, Button, Tag, Empty } from 'antd';
import { RightOutlined } from '@ant-design/icons';
import { useModel } from 'umi';
// import { Icon } from "@hc/caas-components";
import { fetchNoticeList } from '@/services/notice';
import { createWebSocket } from '@/utils/websocket';
import storage from "@/utils/storage";
import styles from './index.less';
import './override.less'

const MessageNotice = () => {
    const { userInfo } = useModel('user');
    const { setCurNav } = useModel("nav");
    const { setAppId } = useModel("globalState");
    const { setMenus } = useModel("menu");
    const [isOpen, setIsOpen] = useState(false);
    const [unreadCount, setUnreadCount] = useState(0);
    const [messageList, setMessageList] = useState([]);

    // 创建websocket链接，接受站内实时消息通知
    const createNoticeSocket = (id) => {
        let domain = 'ws';
        if (window.location.protocol.toLocaleLowerCase() === 'https:') {
          domain = 'wss';
        }
        const socketUrl = `${domain}://${window.location.host}/noticeSocketApi?uid=${userInfo.id}`;
        let ws = createWebSocket(socketUrl);
        ws.onmessage = function (e) {
            let data = JSON.parse(e.data);
            setUnreadCount(data?.unreadCount || 0);
          };
    };

    // 获取消息通知列表
    const getNoticeList = (params, oldList) => {
        fetchNoticeList(params).then((res) => {
            setMessageList(res?.records || []);
        });
    };

    useEffect(() => {
        if (userInfo?.id) {
            createNoticeSocket(userInfo.id);
        }
    }, [userInfo]);

    useEffect(() => {
        if (userInfo?.id && isOpen) {
            getNoticeList(
                {
                    userId: userInfo.id,
                    current: 1,
                    size: 3,
                    status: 0 // 未读
                },
                [],
            );
        }
    }, [userInfo, isOpen]);

    const pageToMessage = (url) => {
        setMenus([]);
        setCurNav(null);
        setAppId(null);
        storage.removeSession("activedApp");
        storage.removeSession("activeAppId");
        storage.removeSession("activeAppCode");
        window.history.pushState(null, null, `/#${url}`);
        // url && history.push(url);
    }

    const messageNode = (
        <div className={styles.noticeMessage}>
            <header>
                <h4>{locale.message.latestMessage}</h4>
                <Button
                    type='link'
                    onClick={() => {
                        pageToMessage('/messageCenter/subscription')
                        setIsOpen(false);
                    }}
                >
                    {locale.message.subscription}
                </Button>
            </header>
            <main>
                {
                    messageList?.length > 0 ?
                    messageList.map((item) => (
                        <dl key={item.id}>
                            <dt>
                                <span onClick={() => {
                                    storage.setSession('message', {
                                         ...item,
                                        userId: userInfo?.id
                                    });
                                    pageToMessage('/messageCenter/messageList/detail')
                                }}>
                                    {item.title}
                                </span>
                            </dt>
                            <dd>
                                <span>{item.publishTime}</span>
                                <Tag className={styles.noticeTag}>{item.messageType}</Tag>
                            </dd>
                        </dl>
                    )) :
                    <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={locale.message.noMessage} />
                }
            </main>
            <footer>
                <Button
                    type='link'
                    onClick={() => {
                        pageToMessage('/messageCenter/messageList')
                        setIsOpen(false);
                    }}
                >
                    {locale.message.viewMore}
                    <RightOutlined />
                </Button>
            </footer>
        </div>
    );

    const handleOpenChange = (val) => {
        setIsOpen(val);
    };


    return (
        <div>
            <Popover
                placement="bottomRight"
                content={messageNode}
                trigger="hover"
                overlayClassName={'message-popover'}
                open={isOpen}
                onOpenChange={handleOpenChange}
            >
                <div className={styles.noticeIcon}>
					<span className={styles.messageIcon}>
						{/* <Icon name="v35_Bell" /> */}
					</span>
                    {unreadCount > 0 &&
                        <div className={styles.noticeCount}>
                            {unreadCount}
                        </div>
                    }
                </div>
            </Popover>
        </div>
    );
};

export default MessageNotice;
