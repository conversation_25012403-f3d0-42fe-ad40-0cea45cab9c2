import { useState, useEffect } from "react";
import { useModel, history, useLocation } from "umi";
import {
  Dropdown,
  Image,
  Divider,
  Popover,
  notification,
  Button,
  message,
  Typography,
  Radio,
  List
} from "antd";
import "./index.less";
import Icon, { BellOutlined, CheckOutlined } from "@ant-design/icons";
import { getCurrentUserNotice, setAllNoticeRead } from "@/services";


export default function Operate({isHome}) {
    const [isDropdownOpen, setIsDropdownOpen] = useState(false);
    const [activeTab, setActiveTab] = useState('全部');
    const [notificationList, setNotificationList] = useState([]);
    const [unreadCount, setUnreadCount] = useState(0)

    useEffect(() => {
        fetchAllNoticeList()
    },[activeTab])
    useEffect(() => {
        fetchUnreadNum()
    },[])

    const fetchAllNoticeList = () => {
        const query = {
            current: 1,
            size: 20,
        }
        if (activeTab === '未读') {
            query.isRead = 0
        }
        getCurrentUserNotice(query).then(res => {
            setNotificationList(res.records)
            if (activeTab === '未读') {
                setUnreadCount(res.total)
            }
        })
    }
    const fetchUnreadNum = () => {
        const query = {
            current: 1,
            size: 20,
            isRead: 0,
        }
        getCurrentUserNotice(query).then(res => {
            setUnreadCount(res.total)
        })
    }

    const handleSetAllRead = () => {
        const query = {
            allRead: true,
        }
        setAllNoticeRead(query).then((res) => {
            if (res) {
                message.success('设置成功')
                fetchAllNoticeList()
                fetchUnreadNum()
            } else {
                message.error('设置失败')
            }
        })
    };
    const handleSetSingleRead = (item) => {
        const query = {
            noticeId: item.noticeId,
            noticeRelationId: item.noticeRelationId,
            allRead: false,
            allUser: item.allUser,
        }
        setAllNoticeRead(query).then((res) => {
            if (res) {
                fetchAllNoticeList()
                fetchUnreadNum()
            }
        })
    }

    const handleTabChange = ({ target: { value } }) => {
        setActiveTab(value);
    };

    const handleAllNotice = () => {
        setIsDropdownOpen(false)
        history.push(`/notice/all`);
    }

    const handleClickItem = (item) => {
        handleSetSingleRead(item)
        setIsDropdownOpen(false)
        if (item.noticeType === 1) {
            history.push(`/notice/all`);
        } else {
            // history.push(`${item.routePath}/${item.routeParams.instanceId}/${item.routeParams.taskId}`)
            let route = `${item.routePath}/${item.routeParams.instanceId}`;
            if (item.routeParams.instanceId && item.routeParams.taskId) {
                route += `/${item.routeParams.taskId}`;
            }
            history.push(route);
        }
    }

    const overlay = () => {
        return (
            <div className="notice-dropdown">
                <div style={{ display: 'flex', alignItems: 'center', padding: '0 20px' }}>
                    <Typography.Title level={3} style={{ margin: 0, fontWeight: 500, flex: 1 }}>通知</Typography.Title>
                    <span style={{ color: '#2b7fff', float: 'right', cursor: 'pointer' }} onClick={handleSetAllRead}>
                        全部设为已读
                    </span>
                </div>
                <div className="notice-tabs" style={{padding: '20px 40px'}}>
                    <Radio.Group
                        style={{width: '100%'}}
                        defaultValue={activeTab} 
                        buttonStyle="solid"
                        onChange={handleTabChange}
                    >
                        <Radio.Button style={{width: '50%', textAlign: 'center'}} value="全部">全部</Radio.Button>
                        <Radio.Button style={{width: '50%', textAlign: 'center'}} value="未读">未读</Radio.Button>
                    </Radio.Group>
                </div>
                <div className="notice-list">
                    <List
                        dataSource={notificationList}
                        renderItem={(notification) => (
                            <List.Item
                                key={notification.id}
                                style={{ backgroundColor: notification.isRead ? 'transparent' : '#eff6ff', 
                                    border: notification.isRead ? '1px solid #eee': '1px solid #c6dfff',
                                    borderRadius: '8px',
                                    marginBottom: '10px',
                                    cursor: 'pointer',
                                }}
                                onClick={() => handleClickItem(notification)} 
                            >
                                <List.Item.Meta
                                    avatar={
                                        <div style={{ color: notification.isRead ? 'green' : 'blue', marginLeft: '10px', marginTop: '7px', fontSize: '10px' }}>
                                            {notification.isRead ? <CheckOutlined /> : <div style={{ width: 10, height: 10, borderRadius: '50%', backgroundColor: '#2b7fff' }} />}
                                        </div>
                                    }
                                    title={<strong style={{fontSize: '16px'}}>{notification.title}</strong>}
                                    description={
                                        <div>
                                            <div style={{fontSize: '16px'}}>{notification.content}</div>
                                            <div>{notification.releaseDesc}</div>
                                        </div>
                                    }
                                />
                            </List.Item>
                        )}
                    />
                </div>
                <div style={{width: '100%',textAlign: 'center', color: '#2b7fff', height: '46px', lineHeight: '46px'}}>
                    <span onClick={handleAllNotice} style={{cursor: 'pointer'}}>查看全部通知</span>
                </div>
            </div>
        );
    };

    return(
        <div className="notice">
            <Dropdown
                trigger={["click"]}
                placement="bottomCenter"
                open={isDropdownOpen}
                onOpenChange={setIsDropdownOpen}
                dropdownRender={() => {
                    return overlay();
                }}
            >
                <span className="ant-dropdown-link" onClick={(e) => e.preventDefault()}>
                    <BellOutlined className='fa fa-bell' />
                    {unreadCount > 0 && (
                        <span className="notice-count">
                            {unreadCount > 99 ? '99+' : unreadCount}
                        </span>
                    )}
                </span>
            </Dropdown>
            
        </div>
    )
}