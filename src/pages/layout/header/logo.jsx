import locale from "@/locale";
import React, { useState } from "react";
import { Image } from "antd";
import { useModel, history } from "umi";
import { homePath } from "@/constants"

export default function Logo() {
  const { sideBarExpand } = useModel("menu", ({sideBarExpand}) => ({
    sideBarExpand
  }))
  
  const { initialState } = useModel("@@initialState");
  const { appConfig } = initialState;
  
  const handleClick = () => {
    history.push(homePath);
  }
  const handleClickStop = (e) => {
    e.preventDefault(); // 阻止默认跳转行为
    e.stopPropagation(); // 阻止事件冒泡
    // 这里可以添加其他自定义逻辑
  };
  return (
    <div className={`logo-box ${sideBarExpand ? '' : 'logo-only'}`} onClick={handleClick}> 
      <div className="logo">
        <a 
          href="https://tasp-test.trinasolar.com/"
          target="_blank"
          onClick={handleClickStop}
        >
          <Image src={appConfig?.logoUrl} preview={false} rootClassName="logo-img"/>
        </a>
       
      </div>
      <h4>{appConfig?.title}</h4>
    </div>
  );
}
