import React,{ useState,useEffect } from 'react';
import { Button, Modal,Descriptions,Select,Form,message,Divider,Input } from 'antd';
import { getApiOrg } from '@/services';


const apply = ({isModalOpen,handleCancel,subscribeHandle,info}) => {
  const [orgSel, setOrgSel] = useState([]);
  const [orgValue, setOrgValuel] = useState('');
  const [subOrgName, setSubOrgName] = useState('');
  const [clientOpts, setClientOpts] = useState([]);
  const [clientId, setClientId] = useState('');
  const [newAppName, setNewAppName] = useState('');
  // 是否新建应用组信息
  const [isNewApp, setIsNewApp] = useState(false);
  const handleOk = () => {
    if(!orgValue){
      message.error('请选择订阅组')
      return;
    }
    const params = {
      subOrgId:orgValue,
      subOrgName:subOrgName,
    }
    isNewApp ? params.newAppName = newAppName : params.clientId = clientId
    subscribeHandle(params)
    
  };

  const handleChange = (value, option) => {
    setOrgValuel(value)
    setSubOrgName(option.label)
    getCLientOptions(option?.clientList ?? [])
  };
  const getApiOrgList = async () => {
    try {
        const data = await getApiOrg({pubOrgId:info.pubOrgId});
        let orgSel = [];
        data.forEach(element => {
          orgSel.push({
            value: element.id,
            label: element.name,
            clientList: element.clientList
          })
        });
        setOrgSel(orgSel)
        
    } catch (error) {
      console.log(error);
    }
  }
  const getCLientOptions = (clientList) => {
    const tmpClientOpts = [];
    clientList.forEach(item => {
      tmpClientOpts.push({
        value: item.clientId,
        label: `${item.pubOrgName}-clientId: ${item.clientId}`
      })
    });
    setClientOpts(tmpClientOpts)
  }
  const onFinish = (values) => {
    console.log('Success:', values);
  };

  useEffect(() => {
    getApiOrgList()
  }, []);
  return (
    <div style={{paddingTop:'0px'}}>
        <Modal title="能力申请" open={isModalOpen} onOk={handleOk} onCancel={handleCancel} className="custom-modal" okText={'确定'} cancelText={'取消'}>
            
            <Divider style={{marginBottom:'20px'}}/>
            <Descriptions title="" column={1} styles={{ label: {color:'#333'}, content: {color:'#8a8a8a',paddingLeft:'15px'} }}>
                <Descriptions.Item label="API名称">{info.name}</Descriptions.Item>
                <Descriptions.Item label="API编码">{info.code}</Descriptions.Item>
                <Descriptions.Item label="API版本">{info.version}</Descriptions.Item>
                <Descriptions.Item label="发布组">{info.pubOrgName}</Descriptions.Item>
            </Descriptions>
            <Divider style={{marginBottom:'20px'}} />
            <Form
             name="basic"
             onFinish={onFinish}
             layout='vertical'
            >
             <Form.Item
               label="订阅组名称"
               name="org"
               rules={[{ required: true, message: '请选择订阅组' }]}

             >
             <Select     
              style={{ width: "100%" }}
              onChange={handleChange}
              options={orgSel}
              value={orgValue}
              showSearch={true}
              filterOption={(input, option) =>
                (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
              }
            />
             </Form.Item>
             {
              isNewApp ? 
              <Form.Item
                label="新建订阅组应用"
                name="newAppName"
              >
                <Input onChange={(e) => setNewAppName(e.target.value)}/>
              </Form.Item> : 
              <Form.Item
                label="订阅组应用信息"
                name="clientId"
              >
                <Select     
                  style={{ width: "100%" }}
                  onChange={(value) => setClientId(value)}
                  options={clientOpts}
                  value={clientId}
                  showSearch={true}
                  filterOption={(input, option) =>
                    (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                  }
                />
              </Form.Item>
             }
           </Form>
           {
             !isNewApp && <Button type="primary" onClick={() => setIsNewApp(true)}>没有应用？新建一个</Button>
           }
        </Modal>
    </div>
  );
};

export default apply; 