// src/components/InterfaceDoc.js
import React from 'react';
import { Menu, Layout, Typography, Table } from 'antd';
const { Header, Content } = Layout;

const { Title } = Typography;

const InterfaceDoc = () => {
  const data = [
    { key: '1', name: 'name', required: '必填', example: '示例值1', description: '描述1' },
    { key: '2', name: 'id', required: '必填', example: '示例值2', description: '描述2' },
    { key: '3', name: 'code', required: '必填', example: '示例值3', description: '描述3' },
  ];

  return (
    <Layout>
      <Header>
        <Title level={1}>接口文档</Title>
      </Header>
      <Content style={{ padding: '24px' }}>
        <Menu mode="inline" defaultSelectedKeys={['1']} style={{ marginBottom: '24px' }}>
          <Menu.Item key="1">接口1</Menu.Item>
          <Menu.Item key="2">接口2</Menu.Item>
          <Menu.Item key="3">接口3</Menu.Item>
        </Menu>
        <div>
          <Title level={2}>接口1</Title>
          <p>接口地址: <strong>http://***********:32126/httpbin</strong></p>
          <p>请求方式: GET</p>
          <Title level={3}>请求参数</Title>
          <Title level={4}>Path参数</Title>
          <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
          <Title level={4}>Query参数</Title>
          <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
        </div>
      </Content>
    </Layout>
  );
};

export default InterfaceDoc;
