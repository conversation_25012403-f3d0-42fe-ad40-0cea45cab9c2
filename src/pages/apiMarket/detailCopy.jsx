import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import InterfaceDoc from './detailBottom';
import './detail.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE, API_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Spin, notification ,Input,Typography,Table,message } from 'antd';
import  apiLogoIMage from '@/assets/images/apiLogo.png';
import { subscribe,getApiDetail } from '@/services';
// 开始
import Apply from './apply';
const { Search } = Input;
const apiDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { id } = location || {};
  const apiId = id;
  useEffect(() => {
   // getApiDetailInfo();
    ////////测试数据/////////////
    const data ={

      "code": "ctripbiztravel-itm",

      "id": 786,

      "name": "携程商旅-信息技术管理",

      "pubOrgId": null,

      "pubOrgName": "信息技术发布组",

      "tagId": 92,

      "tagName": "信息技术",

      "version": "1.0.0",

      "gatewayUrl": null,

      "sandboxClientId": "********************************",

      "sandboxClientSecret": "********************************",

      "apiEndpoints": [

          {

              "path": "/643ce18d54813df9cffbcee4",

              "httpMethod": "POST",

              "parameters": 
          [

                  {

                      "name": "body",

                      "in": "body",

                      "type": "object",

                      "description": "",

                      "required": false,
                      "exampleValue": "",

                      "schemaRequired": [

                          "Auth",

                          "SearchType"

                      ],

                     

                      "schema": {

                          "type": "object",

                          "required": [

                              "Auth",

                              "SearchType"

                          ],

                          "properties": {

                              "OrderID": {

                                  "type": "string",

                                  "description": "订单号，SearchType为2、4时，订单号必填"

                              },

                              "SearchType": {

                                  "type": "string",

                                  "description": "查询产品类别, 2 机票（国内+国际） 3 火车票（国内） 4酒店（国内+海外）5租车（国内）6打车 10国内机票 11国际机票 12国内酒店 13国际酒店"

                              },

                              "Auth": {

                                  "type": "object",

                                  "description": "接入账号、ticket用作身份验证",

                                  "properties": {

                                      "Appkey": {

                                          "type": "string",

                                          "description": "接入账号，由携程分配给客户公司"

                                      },

                                      "Ticket": {

                                          "type": "string",

                                          "description": "令牌"

                                      }

                                  },

                                  "required": [

                                      "Appkey",

                                      "Ticket"

                                  ]

                              },

                              "DateFrom": {

                                  "type": "string",

                                  "description": "开始时间；订单号和预订时间必填一项，格式：yyyy-MM-dd或yyyy-MM-dd HH:mm:ss\n\n必须与产线组合使用"

                              },

                              "DateTo": {

                                  "type": "string",

                                  "description": "结束时间；订单号和预订时间必填一项，格式：yyyy-MM-dd或yyyy-MM-dd HH:mm:ss\n\n必须与产线组合使用"

                              },

                              "FltSearchTypeExtend": {

                                  "type": "string",

                                  "description": "机票订单搜索类型（退改签）；\n\n按照退改签查询机票时，此项和DateFrom、DateTo必填。此项值必须为“RefundTime”或“RebookedTime”\n\n按照行程单打印查询时，此项为‘\"PrintTime\"，DateFrom和DateTo为行程单打印时间段"

                              },

                              "TrainSearchTypeExtend": {

                                  "type": "string",

                                  "description": "火车票订单搜索类型（退改签）；按照退改签查询火车票时，此项和DateFrom、DateTo必填。此项值必须为“RefundChangeTime”"

                              },

                              "HtlSearchTypeExtend": {

                                  "type": "string",

                                  "description": "酒店订单搜索类型（取消时间）；按照取消时间查询酒店时，此项和DateFrom、DateTo必填。此项值必须为“cancelDate”"

                              },

                              "CarSearchTypeExtend": {

                                  "type": "string",

                                  "description": "用车订单搜索类型（取消时间）；按照取消时间查询用车时，此项和DateFrom、DateTo必填。此项值必须为“cancelDate”"

                              },

                              "PageIndex": {

                                  "type": "string",

                                  "description": "分页页码，仅针对时间段+指定产线（区分国内国际）"

                              },

                              "PageSize": {

                                  "type": "string",

                                  "description": "分页大小，默认10"

                              }

                          }

                      },

                      "fields": [

                          {

                              "name": "OrderID",

                              "type": "string",

                              "description": "订单号，SearchType为2、4时，订单号必填",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "SearchType",

                              "type": "string",

                              "description": "查询产品类别, 2 机票（国内+国际） 3 火车票（国内） 4酒店（国内+海外）5租车（国内）6打车 10国内机票 11国际机票 12国内酒店 13国际酒店",

                              "required": true,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "Auth",

                              "type": "object",

                              "description": "接入账号、ticket用作身份验证",

                              "required": true,

                              "item": null,

                              "children": [

                                  {

                                      "name": "type",

                                      "type": "",

                                      "description": "",

                                      "required": false,

                                      "item": null,

                                      "children": null

                                  },

                                  {

                                      "name": "description",

                                      "type": "",

                                      "description": "",

                                      "required": false,

                                      "item": null,

                                      "children": null

                                  },

                                  {

                                      "name": "properties",

                                      "type": "",

                                      "description": "",

                                      "required": false,

                                      "item": null,

                                      "children": null

                                  },

                                  {

                                      "name": "required",

                                      "type": "",

                                      "description": "",

                                      "required": false,

                                      "item": null,

                                      "children": null

                                  }

                              ]

                          },

                          {

                              "name": "DateFrom",

                              "type": "string",

                              "description": "开始时间；订单号和预订时间必填一项，格式：yyyy-MM-dd或yyyy-MM-dd HH:mm:ss\n\n必须与产线组合使用",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "DateTo",

                              "type": "string",

                              "description": "结束时间；订单号和预订时间必填一项，格式：yyyy-MM-dd或yyyy-MM-dd HH:mm:ss\n\n必须与产线组合使用",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "FltSearchTypeExtend",

                              "type": "string",

                              "description": "机票订单搜索类型（退改签）；\n\n按照退改签查询机票时，此项和DateFrom、DateTo必填。此项值必须为“RefundTime”或“RebookedTime”\n\n按照行程单打印查询时，此项为‘\"PrintTime\"，DateFrom和DateTo为行程单打印时间段",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "TrainSearchTypeExtend",

                              "type": "string",

                              "description": "火车票订单搜索类型（退改签）；按照退改签查询火车票时，此项和DateFrom、DateTo必填。此项值必须为“RefundChangeTime”",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "HtlSearchTypeExtend",

                              "type": "string",

                              "description": "酒店订单搜索类型（取消时间）；按照取消时间查询酒店时，此项和DateFrom、DateTo必填。此项值必须为“cancelDate”",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "CarSearchTypeExtend",

                              "type": "string",

                              "description": "用车订单搜索类型（取消时间）；按照取消时间查询用车时，此项和DateFrom、DateTo必填。此项值必须为“cancelDate”",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "PageIndex",

                              "type": "string",

                              "description": "分页页码，仅针对时间段+指定产线（区分国内国际）",

                              "required": false,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "PageSize",

                              "type": "string",

                              "description": "分页大小，默认10",

                              "required": false,

                              "item": null,

                              "children": null

                          }

                      ],

                      "items": null

                  }

              ],

              "response": [

                  {

                      "statusCode": "200",

                      "description": null,

                      "body": null,

                      "fields": [

                          {

                              "name": "Status",

                              "type": "object",

                              "description": "",

                              "required": null,

                              "item": null,

                              "children": [

                                  {

                                      "name": "type",

                                      "type": "",

                                      "description": "",

                                      "required": null,

                                      "item": null,

                                      "children": null

                                  },

                                  {

                                      "name": "properties",

                                      "type": "",

                                      "description": "",

                                      "required": null,

                                      "item": null,

                                      "children": null

                                  }

                              ]

                          },

                          {

                              "name": "ItineraryList",

                              "type": "array",

                              "description": "",

                              "required": null,

                              "item": {

                                  "name": null,

                                  "type": "object",

                                  "description": "",

                                  "required": null,

                                  "item": null,

                                  "children": [

                                      {

                                          "name": "HotelOrderInfoList",

                                          "type": "array",

                                          "description": "",

                                          "required": null,

                                          "item": null,

                                          "children": null

                                      },

                                      {

                                          "name": "FlightOrderInfoList",

                                          "type": "array",

                                          "description": "描述机票订单的完整数据结构",

                                          "required": null,

                                          "item": null,

                                          "children": null

                                      },

                                      {

                                          "name": "TrainOrderInfolist",

                                          "type": "array",

                                          "description": "",

                                          "required": null,

                                          "item": null,

                                          "children": null

                                      },

                                      {

                                          "name": "CarQuickOrderInfoList",

                                          "type": "array",

                                          "description": "",

                                          "required": null,

                                          "item": null,

                                          "children": null

                                      },

                                      {

                                          "name": "CarOrderInfoList",

                                          "type": "array",

                                          "description": "描述国内租车订单的完整数据结构",

                                          "required": null,

                                          "item": null,

                                          "children": null

                                      }

                                  ]

                              },

                              "children": null

                          },

                          {

                              "name": "TotalRecord",

                              "type": "integer",

                              "description": "总条数(分页时返回)",

                              "required": null,

                              "item": null,

                              "children": null

                          },

                          {

                              "name": "TotalSize",

                              "type": "integer",

                              "description": "总页数(分页时返回)",

                              "required": null,

                              "item": null,

                              "children": null

                          }

                      ]

                  }

              ]

          },
          {

            "path": "/643ce18d54813df9cffbcee4/ddddddddddddddd",

            "httpMethod": "POST"}

      ]

  }
  // deta.apiEndpoints?.fo
  const apiEndpoints = data.apiEndpoints || [];
  setApiEndpoints(apiEndpoints)
  setActiveLi(apiEndpoints[0]?.path)
  // console.log(pathArray)
  setDetailInfo(data)
  }, [])
  const handleBack = () => {
    history.back();
  };

 
  ///////开始//////////////
  const linkList = [
    {
      href: '/#/apiMarket',
      title: 'API市场'
    },
    {
      href: `/#/apiMarket/detail/${id}`,
      title: `API详情`
    },
  ]
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [detailInfo, setDetailInfo] = useState({});
  const [apiEndpoints, setApiEndpoints] = useState([]);
  const [activeLi, setActiveLi] = useState('');
  const [paramList, setParamList] = useState([]);
  const [bodyParam, setBodyParam] = useState([]);
  
  
  
  
  
  const data = [
    { key: '1', name: 'name', required: '必填', example: '示例值1', description: '描述1' },
    { key: '2', name: 'id', required: '必填', example: '示例值2', description: '描述2' },
    { key: '3', name: 'code', required: '必填', example: '示例值3', description: '描述3' },
  ];

  const applyOPerate = () => {
    setIsModalOpen(true)
  }
  const handleCancel = () => {
    setIsModalOpen(false)
  }
  /////订阅/////
  const subscribeHandle =  (param) => {
    try {
        const dataParam = {
            ...param,
            productId:apiId
        }
        subscribe(dataParam).then((res) => {
          if(res){
            message.success("订阅成功")
            handleCancel()
          }else{
            message.error("订阅失败")
          }
        }).catch(() => {
          message.error("订阅失败")
        })
    } catch (error) {
      console.log(error);
    }
  }
  ////////详情////////////
  const getApiDetailInfo =  () => {
    const param = {
      id:apiId
    }
    getApiDetail(param).then((res) => {
      if(res){
      setDetailInfo(res)
      }
    })
  }
  const processChildren =  (api,array) => {
    if(api.type === 'object'){
      array = [...array,...api.fields]
    }else if(api.type === 'array'){
      array = [...array,...api.item]
    }else{
      array.push({
        name: api.name,
        description: api.description,
        required: api.required,
        exampleValue: api.exampleValue
      })
    }
    return array;
  }
  
  const changeActiveLi =  (item) => {
    setActiveLi(item.path);
    const data = apiEndpoints.filter((list)=>list.path === item.path);
    const paramList = data[0].parameters || []
    const headerParam =  paramList.filter((item)=>item['in'] === 'header')
    const queryParam =  paramList.filter((item)=>item['in'] === 'query')
    const pathParam =  paramList.filter((item)=>item['in'] === 'path')
    const bodyParam =  paramList.filter((item)=>item['in'] === 'body')
    if(bodyParam.length > 0){
     let array =  processChildren(bodyParam[0],[]);
     setBodyParam(array)

     console.log(array)
    }
  
    console.log(bodyParam[0])
    
    setParamList(paramList)
  }
  const columns = [
    {
      title: '参数名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '是否必填',
      dataIndex: 'required',
      key: 'required',
    },
    {
      title: '参数描述',
      dataIndex: 'description',
     
      key: 'description'
    },
  ];

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header  nav-path-ui">
        {/* <div className="nav-path">
          <span>产品与服务</span>
          <span>/</span>
          <span>产品详情</span>
        </div> */}
        <BreadcrumbNav list={linkList}></BreadcrumbNav>
        <div className="back-button" onClick={handleBack}>
          <LeftOutlined />
          <span>返回</span>
        </div>
      </div>
      <div  className='micro-spin-container-box'>
        {/* 中间件标题区域 */}
        <div className="middleware-header dealPadding">
          <div className="middleware-base-info">
            <div className='logo-box'>
              <img src={apiLogoIMage} className="middleware-logo" />
            </div>
            <div >
              <div className="logo-title">
                <div className="title-wrapper">
                  <div className="title">{detailInfo.name}</div>
                  <div className="subtitle">编码：{detailInfo.code}</div>
                  <div className="subtitle">版本：{detailInfo.version}</div>
                  <div className="subtitle">发布组：{detailInfo.pubOrgName}</div>
                </div>
              </div>
              <div className="action-buttons">
                <button className="action-btn primary" onClick={()=>{applyOPerate()}}>申请能力</button>
              </div>
            </div>

          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div className="info-section-box">
            <div className="info-section-left">
              <h3 className="section-title">基本信息</h3>
              <div>
                <Search placeholder="Search" onChange={(e) => onChange(e)} />
                <div>
                  <ul className="info-section-ul">
                  {
                    apiEndpoints.map((item)=>  <li className={activeLi === item.path?'activeLi':'normalLi'} onClick={()=>{changeActiveLi(item)}}>{item.path}</li>)
                  }
                  
                  </ul>
                </div>
              </div>
            </div>
            <div className="info-section-right">
              <div>
                <div className="info-section-right-title">接口1</div>
                <div className="info-section-right-des">
                  <p>接口地址: {}</p>
                  <p>请求方式: GET</p>
                  <p>接口描述: qqqq</p>
                </div>
              
                <div className="info-section-right-title-noBorder">请求参数</div>
                <div className="info-section-right-title-noBorder info-section-right-title-small">Path参数</div>
                <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
                <div className="info-section-right-title-noBorder info-section-right-title-small">Query参数</div>
                <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
                <div className="info-section-right-title-noBorder info-section-right-title-small">Header参数</div>
                <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
                <div className="info-section-right-title-noBorder info-section-right-title-small">Body参数</div>
                <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
                <div className="info-section-right-title-noBorder info-section-right-title-small">响应参数</div>
                <div className="info-section-right-title-noBorder info-section-right-title-small-min">响应头</div>
                <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
                <div className="info-section-right-title-noBorder info-section-right-title-small-min">响应体</div>
                <Table columns={[{ title: '参数名称', dataIndex: 'name' }, { title: '是否必填', dataIndex: 'required' }, { title: '示例值', dataIndex: 'example' }, { title: '参数描述', dataIndex: 'description' }]} dataSource={data} />
                <div className="info-section-right-title-noBorder info-section-right-title-small-min">预览</div>
              </div>
            </div>
          </div>
        </div>
      </div>
     
      
        {
          isModalOpen?<Apply isModalOpen={isModalOpen} handleCancel={handleCancel} subscribeHandle={subscribeHandle}/>:''
        }
    
    </div>
  );
};

export default apiDetail; 