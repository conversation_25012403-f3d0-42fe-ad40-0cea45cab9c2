import React, { useState } from 'react';
import { Table, Input, Tree } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
 
const MyTreeTable = ({ data }) => {
  const [searchText, setSearchText] = useState('');
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [filteredData, setFilteredData] = useState(data);
 
  const onExpand = (expandedKeysValue) => {
    setExpandedKeys(expandedKeysValue);
    setAutoExpandParent(false);
  };
 
  const filterData = (value, children) => {
    if (!value) return children;
    return children.filter(item => item.title.toLowerCase().includes(value.toLowerCase()));
  };
 
  const handleSearch = e => {
    const value = e.target.value;
    const filtered = data.map(item => ({ ...item, children: filterData(value, item.children || []) }));
    setFilteredData(filtered);
    setSearchText(value);
  };
 
  const columns = [
    { title: 'Name', dataIndex: 'title', key: 'title' }
  ];
  
  const renderTreeData = () => (
    <Tree treeData={filteredData} defaultExpandAll /> // 直接使用 Tree 来渲染过滤后的数据，默认展开所有节点以展示搜索结果。
  );
  
  return (
    <div>
      <Input.Search placeholder="Search" enterButton={<SearchOutlined />} onSearch={handleSearch} style={{ marginBottom: 8 }} />
      <Table columns={columns} dataSource={filteredData} pagination={false} rowKey="key" /> // 使用 Tree 的方式渲染数据，而非 Table 的默认方式。这里可以改为使用自定义的 render 方法来展示 Tree。
      {/* 或者使用以下方式直接在 Table 中展示 */}
      {/* <Table columns={columns} dataSource={filteredData} pagination={false} rowKey="key" expandedRowRender={renderTreeData} onExpand={onExpand} expandedRowKeys={expandedKeys} autoExpandParent={autoExpandParent} /> */}
    </div>
  );
};