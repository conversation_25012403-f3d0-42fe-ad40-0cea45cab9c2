// import React, { useEffect, useRef, useState,useMemo } from "react";
// import { Input, Button, Affix, Select, Checkbox,Menu,message,Empty,Tree } from "antd";
// import "./index.less";
// // import "./products.less";
// import { history } from "umi";
// const { SubMenu, Item: MenuItem } = Menu;
// import PassSer from '../passService/passSer';
// import { getAllMiddlewareAppList,addFavorite,removeFavorite } from '@/services/middleware';



// const Products = ({searchValue}) => {
//   // const allData = MockAllData;
//   const [curData, setCurData] = useState([]);
//   const [selectedGroup, setGroup] = useState([]);
//   const [showSearch, setSearch] = useState(false);
//   const [selectedKeys, setSelectedKeys] = useState(['全部']);
//   const [allList, setAllList] = useState({});
//   const [productList, setProductList] = useState([]);
//   const [commonproductList, setCommonproductList] = useState([]);
//   const [list, setList] = useState([]);
//   const [typeListData, setTypeListData] = useState([]);
//   const [isEmputy, setIsEmputy] = useState([]);
  
//   useEffect(() => {  
//   //getList()   
//     const MockAllData =  {
   
//       "allProducts": [
//           {
//               "id": 1,
//               "productId": 38,
//               "name": "Elasticsearch",
//               "description": "CloudDB Elasticsearch数据库",
//               "url": null,
//               "type": "数据库",
//               "supplier": "",
//               "category": "中间件",
//               "version": "6.8.22",
//               "logoUrl": "https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/middleware/elasticsearch-1.10.0.svg"
//           }
//       ]
//   }
//     const typeList = [];
//     MockAllData.allProducts?.forEach((pro1)=>{
//       MockAllData.favoriteProducts?.forEach((pro2)=>{
//           if(pro1.id === pro2.id){
//             pro1.isCollect = true
//           }
//       })
//     })
//     const typeListData = [{type:'常用产品',data:MockAllData.favoriteProducts}];
//       MockAllData.allProducts?.forEach((item)=>{
//         typeList.push(item.type)
//       })
//       const typeListAll = [...new Set(typeList)];
//       typeListAll.forEach((list)=>{
//         typeListData.push({
//           type:list,
//           data:[]
//         })
//       })
//       typeListAll.unshift('常用产品');
//       typeListAll.unshift('全部');
//       setList(typeListAll)
//       typeListData.forEach((item)=>{
//         MockAllData.allProducts.forEach((list)=>{
//           if(list.type === item.type){
//             item.data.push(list)
//           }
//         })
//       }) 
//       searchValue ? typeListData.filter((i) => {
//           i.data = i.data.filter((c) => c.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1)
//         })
//       : typeListData;  
//       let isEmputy = true;
//         typeListData.forEach((item)=>{
//           if(item.data?.length > 0){
//             isEmputy = false;
//           }
//         })
//         setIsEmputy(isEmputy)
//         console.log('hahah');
//         console.log(isEmputy);
//         console.log(typeListData) 
//       setTypeListData(typeListData)
//       setProductList(MockAllData.allProducts || [])
//       setCommonproductList(MockAllData.favoriteProducts || [])
    
    
//   }, [searchValue]);

//   const getSelectKey = (item) => {
//     setSelectedKeys([item.key])
//   }
//   const getList = () => {
//     getAllMiddlewareAppList({}).then(res => {
//       console.log('获取到数据')
//       console.log(res)
//       const MockAllData = res;
//       const typeList = [];
//       MockAllData.allProducts?.forEach((pro1)=>{
//         MockAllData.favoriteProducts?.forEach((pro2)=>{
//             if(pro1.id === pro2.id){
//               pro1.isCollect = true
//             }
//         })
//       })
//       const typeListData = [{type:'常用产品',data:MockAllData.favoriteProducts}];
//         MockAllData.allProducts?.forEach((item)=>{
//           typeList.push(item.type)
//         })
//         const typeListAll = [...new Set(typeList)];
//         typeListAll.forEach((list)=>{
//           typeListData.push({
//             type:list,
//             data:[]
//           })
//         })
//         typeListAll.unshift('常用产品');
//         typeListAll.unshift('全部');
//         setList(typeListAll)
//         typeListData.forEach((item)=>{
//           MockAllData.allProducts.forEach((list)=>{
//             if(list.type === item.type){
//               item.data.push(list)
//             }
//           })
//         }) 
//         searchValue ? typeListData.filter((i) => {
//             i.data = i.data.filter((c) => c.name.toLowerCase().indexOf(searchValue.toLowerCase()) > -1)
//           })
//         : typeListData;  
//         let isEmputy = true;
//         typeListData.forEach((item)=>{
//           if(item.data.length > 0){
//             isEmputy = false;
//           }
//         })
//         setIsEmputy(isEmputy)
//         console.log('hahah');
//         console.log(isEmputy);
//         console.log(typeListData)
//         setTypeListData(typeListData)
//         setProductList(MockAllData.allProducts || [])
//         setCommonproductList(MockAllData.favoriteProducts || [])

//       }); 
//   }
//   const handleCollecte = (event,type,key) => {
//     event.stopPropagation();
//     if(type === 'add'){
//       addFavorite({id:key}).then((res) => {
//         console.log('收藏')
//         console.log(res)
//         if(res){
//           message.success("收藏成功")
//           getList()
//         }else{
//           message.error("收藏失败")
//         }
//       }).catch(() => {
//         message.error("收藏失败")
//       })
//     }else{
//       removeFavorite({id:key}).then((res) => {
//         console.log('取消收藏')
//         console.log(res)
//         if(res){
//           message.success("取消收藏成功")
//           getList()
//         }else{
//           message.error("取消收藏失败")
//         }
//       }).catch(() => {
//         message.error("取消收藏失败")
//       })
//     }
  
//   }
//   return (
//     <div className="products-wrapper dealPadding">
//       <div className="left-sidebar">
//         <Affix offsetTop={22}>
//           <div className="group-name item-padding">全部产品</div>
//           <div>
//             <div className="item-padding">产品类别</div>
//             {list.length > 0  && list.map((g) => {
//               return (
//                <div className="menu-box" style={{width:'240px'}}>
//                 <Menu multiple={false} selectedKeys={selectedKeys} onClick={( item, key)=>{getSelectKey(item,key)}}>
//                   <MenuItem key={g} title={g}>{g}</MenuItem>
//                 </Menu>
//                </div>
//               );
//             })}
//           </div>
//         </Affix>
//       </div>

//       <div className="main-content">
//       {
//         isEmputy?<Empty description="暂无数据" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}/>:
//         (
//           typeListData?.length > 0 && typeListData.map((item)=>{
//             return(
//               (selectedKeys[0] === '全部' || selectedKeys[0] === item.type) && (item.data.length > 0) && 
//               <div className="product-box no-search-bar" >
//                 <div className="group-name">{item.type}</div>
//                 <PassSer prodcut={item.data} productType={item.type} handleCollecte={handleCollecte}/>
//               </div>
//             )
//           })
//         )
//       }
       
     
        
//       </div>
//     </div>
//   );
// };

// const apiMarket = () => {
//   const [selectedFeature, setSelectedFeature] = useState(
//     "提供统一的应用与技术组件API发布、检索与管理平台，支持与iPaaS平台集成适配，实现API全生命周期管理，并通过分类展示、卡片详情和多维度检索实现应用与API资源的快速查找与调用"
//   );
//   const [selectedTitle, setSelectedTitle] = useState("API市场");
//   const [searchValue, setSearchValue] = useState("");
//   return (
//     <>
//       <div className="product-service">
//         <div className="content-wrapper">
//           {/* 页面标题 */}
//           <div className="home-header dealPadding">
//             <div className="header-content">
//               <h1>{selectedTitle}</h1>
//               <div className="description">{selectedFeature}</div>
//             </div>
//           </div>
//           <Products searchValue={searchValue} />
//         </div>
//       </div>
//     </>
//   );
// };

// export default apiMarket;


import React, { useEffect, useRef, useState,useMemo } from "react";
import { Input, Button, Affix, Select, Checkbox, Menu ,Tree} from "antd";
import { getApiTags,getApiList } from '@/services';
import "./index.less";
import ApiList from './apiList';
//import "./products.less";
import { history } from "umi";
const { Search } = Input;

  


// const x = 3;
// const y = 2;
// const z = 1;
// const defaultData = [];

// const generateData = (_level, _preKey, _tns) => {
//   const preKey = _preKey || '0';
//   const tns = _tns || defaultData;

//   const children = [];
//   for (let i = 0; i < x; i++) {
//     const key = `${preKey}-${i}`;
//     tns.push({ title: key, key });
//     if (i < y) {
//       children.push(key);
//     }
//   }
//   if (_level < 0) {
//     return tns;
//   }
//   const level = _level - 1;
//   children.forEach((key, index) => {
//     tns[index].children = [];
//     return generateData(level, key, tns[index].children);
//   });
// };
// generateData(z);

// const dataList = [];
// const generateList = (data) => {
//   for (let i = 0; i < data.length; i++) {
//     const node = data[i];
//     const { key } = node;
//     const { title } = node;
//     dataList.push({ key, title});
//     if (node.children) {
//       generateList(node.children);
//     }
//   }
// };
// generateList(defaultData);

// const getParentKey = (key, tree) => {
//   let parentKey;
//   for (let i = 0; i < tree.length; i++) {
//     const node = tree[i];
//     if (node.children) {
//       if (node.children.some(item => item.key === key)) {
//         parentKey = node.key;
//       } else if (getParentKey(key, node.children)) {
//         parentKey = getParentKey(key, node.children);
//       }
//     }
//   }
//   return parentKey;
// };


const Products = () => {

/////////////开始///////////////////////
// const [filteredTreeData, setFilteredTreeData] = useState([]);
const [filteredTreeData, setFilteredTreeData] = useState([]);
const [searchValue, setSearchValue] = useState("");
const [typeList, setTypeList] = useState([]);
const [dataList, setDataList] = useState([]);
const [total, setTotal] = useState();



const onChange = (e) => {
  const { value } = e.target;
  setSearchValue(value);
  if (value) {
    const filtered = filterTreeData(typeList, value);
    setFilteredTreeData(filtered);
  } else {
    setFilteredTreeData(typeList);
  }
};
const getTypeList = async () => {
    try {
        const data = await getApiTags({});
        console.log("我是类别数据")
        console.log(data)
        const parentList = data.filter((item)=>item.pid === 0);
        parentList.forEach((a)=>{
        a.title = a.name;
        a.key = a.id
        a.children = [];
        })
        const childList = data.filter((item)=>item.pid !== 0);
        parentList.forEach((item)=>{
        childList.forEach((list)=>{
            if(list.pid === item.id){
            item.children.push({
                title:list.name,
                key:list.id,
                pid:list.pid
            })
            }
        })
        })
        const filterData = parentList;
        setTypeList(parentList)
        setFilteredTreeData(filterData)
        console.log(filterData)

    } catch (error) {
      console.log(error);
    }
  }
const getApiListData = async (param) => {
    try {
        const dataParam = {
            ...param
        }
        const data = await getApiList(dataParam);
        console.log("我是api数据")
        console.log(data)
        setDataList(data.list || [])
        setTotal(data.total)
    } catch (error) {
      console.log(error);
    }
  }
 
useEffect(() => {
    getTypeList()
    getApiListData({pageSize:10,pageNo:1,name:'',pubOrgName:''})
    // 测试数据开始
    const data = [

        {
      
            "id": 1,
      
            "name": "业务解决方案",
      
            "pid": 0
      
        },
      
        {
      
            "id": 2,
      
            "name": "ISC",
      
            "pid": 1
      
        },
      
        {
      
            "id": 3,
      
            "name": "IFS",
      
            "pid": 1
      
        },
      
        {
      
            "id": 4,
      
            "name": "MTC",
      
            "pid": 1
      
        },
      
        {
      
            "id": 5,
      
            "name": "IPD",
      
            "pid": 1
      
        },
      
        {
      
            "id": 6,
      
            "name": "ERP1",
      
            "pid": 1
      
        },
      
        {
      
            "id": 7,
      
            "name": "ITR",
      
            "pid": 1
      
        },
      
        {
      
            "id": 8,
      
            "name": "智能智造",
      
            "pid": 0
      
        },
      
        {
      
            "id": 9,
      
            "name": "MES系统",
      
            "pid": 8
      
        },
      
        {
      
            "id": 10,
      
            "name": "技术平台",
      
            "pid": 0
      
        },
      
        {
      
            "id": 11,
      
            "name": "PaaS",
      
            "pid": 10
      
        },
      
        {
      
            "id": 12,
      
            "name": "物联网",
      
            "pid": 10
      
        },
      
        {
      
            "id": 13,
      
            "name": "大数据",
      
            "pid": 10
      
        },
      
        {
      
            "id": 14,
      
            "name": "业务流程",
      
            "pid": 0
      
        },
      
        {
      
            "id": 15,
      
            "name": "BPM接口",
      
            "pid": 14
      
        },
      
        {
      
            "id": 16,
      
            "name": "市场营销",
      
            "pid": 0
      
        },
      
        {
      
            "id": 17,
      
            "name": "合同管理",
      
            "pid": 16
      
        },
      
        {
      
            "id": 18,
      
            "name": "定价与促销计划",
      
            "pid": 16
      
        },
      
        {
      
            "id": 19,
      
            "name": "销售管理",
      
            "pid": 16
      
        },
      
        {
      
            "id": 20,
      
            "name": "订单处理",
      
            "pid": 16
      
        },
      
        {
      
            "id": 21,
      
            "name": "销售与营销策略",
      
            "pid": 16
      
        },
      
        {
      
            "id": 22,
      
            "name": "品牌战略",
      
            "pid": 16
      
        },
      
        {
      
            "id": 23,
      
            "name": "产品组合策略",
      
            "pid": 16
      
        },
      
        {
      
            "id": 24,
      
            "name": "营销",
      
            "pid": 16
      
        },
      
        {
      
            "id": 25,
      
            "name": "销售和定价执行",
      
            "pid": 16
      
        },
      
        {
      
            "id": 26,
      
            "name": "提案制定",
      
            "pid": 16
      
        },
      
        {
      
            "id": 27,
      
            "name": "索赔和客户反馈处理",
      
            "pid": 16
      
        },
      
        {
      
            "id": 28,
      
            "name": "工程",
      
            "pid": 0
      
        },
      
        {
      
            "id": 29,
      
            "name": "产品生命周期管理",
      
            "pid": 28
      
        },
      
        {
      
            "id": 30,
      
            "name": "研发战略",
      
            "pid": 28
      
        },
      
        {
      
            "id": 31,
      
            "name": "技术与生态系统战略",
      
            "pid": 28
      
        },
      
        {
      
            "id": 32,
      
            "name": "设计规则和政策",
      
            "pid": 28
      
        },
      
        {
      
            "id": 33,
      
            "name": "研究和发展",
      
            "pid": 28
      
        },
      
        {
      
            "id": 34,
      
            "name": "创新管理",
      
            "pid": 28
      
        },
      
        {
      
            "id": 35,
      
            "name": "研发组合管理",
      
            "pid": 28
      
        },
      
        {
      
            "id": 36,
      
            "name": "材料研究",
      
            "pid": 28
      
        },
      
        {
      
            "id": 37,
      
            "name": "产品开发",
      
            "pid": 28
      
        },
      
        {
      
            "id": 38,
      
            "name": "工具设计和构建",
      
            "pid": 28
      
        },
      
        {
      
            "id": 39,
      
            "name": "维护产品数据",
      
            "pid": 28
      
        },
      
        {
      
            "id": 40,
      
            "name": "采购和生产",
      
            "pid": 0
      
        },
      
        {
      
            "id": 41,
      
            "name": "生产计划",
      
            "pid": 40
      
        },
      
        {
      
            "id": 42,
      
            "name": "采购",
      
            "pid": 40
      
        },
      
        {
      
            "id": 43,
      
            "name": "需求管理与预测",
      
            "pid": 40
      
        },
      
        {
      
            "id": 44,
      
            "name": "物料计划",
      
            "pid": 40
      
        },
      
        {
      
            "id": 45,
      
            "name": "生产过程监控",
      
            "pid": 40
      
        },
      
        {
      
            "id": 46,
      
            "name": "供应商管理",
      
            "pid": 40
      
        },
      
        {
      
            "id": 47,
      
            "name": "采购策略",
      
            "pid": 40
      
        },
      
        {
      
            "id": 48,
      
            "name": "采购执行",
      
            "pid": 40
      
        },
      
        {
      
            "id": 49,
      
            "name": "光伏产品",
      
            "pid": 40
      
        },
      
        {
      
            "id": 50,
      
            "name": "储能产品",
      
            "pid": 40
      
        },
      
        {
      
            "id": 51,
      
            "name": "装配和分包",
      
            "pid": 0
      
        },
      
        {
      
            "id": 52,
      
            "name": "装配策略",
      
            "pid": 51
      
        },
      
        {
      
            "id": 53,
      
            "name": "装配总体规划",
      
            "pid": 51
      
        },
      
        {
      
            "id": 54,
      
            "name": "容量管理",
      
            "pid": 51
      
        },
      
        {
      
            "id": 55,
      
            "name": "配置管理",
      
            "pid": 51
      
        },
      
        {
      
            "id": 56,
      
            "name": "质量管理",
      
            "pid": 51
      
        },
      
        {
      
            "id": 57,
      
            "name": "分包管理",
      
            "pid": 51
      
        },
      
        {
      
            "id": 58,
      
            "name": "技术设计    BOM",
      
            "pid": 51
      
        },
      
        {
      
            "id": 59,
      
            "name": "装配",
      
            "pid": 51
      
        },
      
        {
      
            "id": 60,
      
            "name": "健康 安全与环境    HSE",
      
            "pid": 51
      
        },
      
        {
      
            "id": 61,
      
            "name": "工厂维护",
      
            "pid": 51
      
        },
      
        {
      
            "id": 62,
      
            "name": "生产工程",
      
            "pid": 51
      
        },
      
        {
      
            "id": 63,
      
            "name": "分销和项目交付",
      
            "pid": 0
      
        },
      
        {
      
            "id": 64,
      
            "name": "运输",
      
            "pid": 63
      
        },
      
        {
      
            "id": 65,
      
            "name": "库存管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 66,
      
            "name": "配送和仓储策略",
      
            "pid": 63
      
        },
      
        {
      
            "id": 67,
      
            "name": "交付项目组合策略",
      
            "pid": 63
      
        },
      
        {
      
            "id": 68,
      
            "name": "供应链效能监控",
      
            "pid": 63
      
        },
      
        {
      
            "id": 69,
      
            "name": "交付项目组合管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 70,
      
            "name": "交付项目管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 71,
      
            "name": "运输管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 72,
      
            "name": "安装",
      
            "pid": 63
      
        },
      
        {
      
            "id": 73,
      
            "name": "库存管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 74,
      
            "name": "采购管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 75,
      
            "name": "供应商管理",
      
            "pid": 63
      
        },
      
        {
      
            "id": 76,
      
            "name": "售后服务",
      
            "pid": 0
      
        },
      
        {
      
            "id": 77,
      
            "name": "服务策略与规划",
      
            "pid": 76
      
        },
      
        {
      
            "id": 78,
      
            "name": "服务网络设计",
      
            "pid": 76
      
        },
      
        {
      
            "id": 79,
      
            "name": "服务效能管理",
      
            "pid": 76
      
        },
      
        {
      
            "id": 80,
      
            "name": "备件管理",
      
            "pid": 76
      
        },
      
        {
      
            "id": 81,
      
            "name": "经销商管理",
      
            "pid": 76
      
        },
      
        {
      
            "id": 82,
      
            "name": "保修管理",
      
            "pid": 76
      
        },
      
        {
      
            "id": 83,
      
            "name": "装机客户管理",
      
            "pid": 76
      
        },
      
        {
      
            "id": 84,
      
            "name": "区域服务执行",
      
            "pid": 76
      
        },
      
        {
      
            "id": 85,
      
            "name": "服务中心交付",
      
            "pid": 76
      
        },
      
        {
      
            "id": 86,
      
            "name": "咨询与培训",
      
            "pid": 76
      
        },
      
        {
      
            "id": 87,
      
            "name": "技术支持",
      
            "pid": 76
      
        },
      
        {
      
            "id": 88,
      
            "name": "管理支持",
      
            "pid": 0
      
        },
      
        {
      
            "id": 89,
      
            "name": "人力资源管理",
      
            "pid": 88
      
        },
      
        {
      
            "id": 90,
      
            "name": "资金管理",
      
            "pid": 88
      
        },
      
        {
      
            "id": 91,
      
            "name": "业务流程管理",
      
            "pid": 88
      
        },
      
        {
      
            "id": 92,
      
            "name": "信息技术",
      
            "pid": 88
      
        },
      
        {
      
            "id": 93,
      
            "name": "财务会计与总账",
      
            "pid": 88
      
        },
      
        {
      
            "id": 94,
      
            "name": "企业业务战略与规划",
      
            "pid": 88
      
        },
      
        {
      
            "id": 95,
      
            "name": "合作与并购",
      
            "pid": 88
      
        },
      
        {
      
            "id": 96,
      
            "name": "业务发展",
      
            "pid": 88
      
        },
      
        {
      
            "id": 97,
      
            "name": "公关",
      
            "pid": 88
      
        },
      
        {
      
            "id": 98,
      
            "name": "胜任力管理",
      
            "pid": 88
      
        },
      
        {
      
            "id": 99,
      
            "name": "财务规划与预测",
      
            "pid": 88
      
        },
      
        {
      
            "id": 100,
      
            "name": "法律 风险和内部审计",
      
            "pid": 88
      
        },
      
        {
      
            "id": 101,
      
            "name": "投资者关系",
      
            "pid": 88
      
        },
      
        {
      
            "id": 102,
      
            "name": "人力资源战略",
      
            "pid": 88
      
        },
      
        {
      
            "id": 103,
      
            "name": "其他",
      
            "pid": 88
      
        }
      
      ]
      
      const parentList = data.filter((item)=>item.pid === 0);
      parentList.forEach((a)=>{
        a.title = a.name;
        a.key = a.id
        a.children = [];
      })
      const childList = data.filter((item)=>item.pid !== 0);
      parentList.forEach((item)=>{
        childList.forEach((list)=>{
          if(list.pid === item.id){
            item.children.push({
              title:list.name,
              key:list.id,
              pid:list.pid
            })
          }
        })
      })
      setTypeList(parentList)
      setFilteredTreeData(parentList)
      console.log(parentList)
      // 测试数据
    const MockAllData =   [
        {
                "code": "122as21111",
                "id": 0,
                "name": "aaaaaaaaa",
                "pubOrgId": 0,
                "pubOrgName": "测试组",
                "tagId": 0,
                "tagName": "",
                "version": "1.5.6"
        },
        {
            "code": "a1122as21111",
            "id": 1,
            "name": "ccccc",
            "pubOrgId": 0,
            "pubOrgName": "测试组",
            "tagId": 0,
            "tagName": "",
            "version": "1.3.4"
        }
    ]
    setDataList(MockAllData)
}, []);



const filterTreeData = (data, keyword) => {
  if(!keyword){
    setFilteredTreeData(data);
    return;
  }
  return data.reduce((acc, item) => {
    if (item.title.toLowerCase().includes(keyword.toLowerCase())) {
      acc.push(item);
    } else if (item.children && item.children.length > 0) {
      const children = filterTreeData(item.children, keyword);
      if (children.length > 0) {
        acc.push({ ...item, children });
      }
    }
    return acc;
  }, []);
};

  // const curEle = useRef();
  // const handleData = () => {
  //   let groupData = selectedGroup.length
  //     ? allData.filter((g) => selectedGroup.includes(g.groupId))
  //     : allData;

  //   const data = searchValue
  //     ? JSON.parse(JSON.stringify(groupData)).filter((i) => {
  //         let flag = false;
  //         if (i.group.includes(searchValue)) {
  //           flag = true;
  //         } else {
  //           i.children = i.children.filter((c) => c.name.includes(searchValue));
  //           flag = !!i.children.length;
  //         }
  //         return flag;
  //       })
  //     : groupData;

  //   setCurData(data);
  // };

  // useEffect(() => {
  //   handleData();
  // }, [allData, searchValue, selectedGroup]);



  const onClickCard = (card) => {
    if (card.path) {
      document.body.scrollTo(0, 0);
      history.push(card.path);
    }
  };
  return (
    <div className="products-wrapper dealPadding">
      <div className="left-sidebar">
        <Affix offsetTop={22}>
            <div>
            <div className="title-left">导航</div>
            <Search placeholder="Search" onChange={(e) => onChange(e)} />
            <Tree treeData={filteredTreeData} />
            </div>
        </Affix>
      </div>
      <div className="main-content">
        {
            <ApiList prodcut={dataList} getList={getApiListData} />
        }
    </div>
    </div>
  );
};

const ProductService = () => {
  const selectedFeature = "提供统一的应用与技术组件API发布、检索与管理平台，支持与iPaaS平台集成适配，实现API全生命周期管理，并通过分类展示、卡片详情和多维度检索实现应用与API资源的快速查找与调用"
  
  const selectedTitle ="API市场";

  return (
    <>
      <div className="product-service">
        <div className="content-wrapper">
          {/* 页面标题 */}
          <div className="home-header dealPadding">
            <div className="header-content">
              
              <h1>{selectedTitle}</h1>
              <div className="description">{selectedFeature}</div>
            </div>
          </div>
          <Products />
        </div>
      </div>
    </>
  );
};

export default ProductService;
