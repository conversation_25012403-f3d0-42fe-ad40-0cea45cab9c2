




import React, { useEffect, useRef, useState,useMemo } from "react";
import { Input, Button, Affix, Select, Checkbox, Menu ,Tree} from "antd";
import { getApiTags,getApiList } from '@/services';
import "./index.less";
import ApiList from './apiList';
//import "./products.less";
import { history } from "umi";
const { Search } = Input;
import { Segmented } from 'antd';
const Products = ({currentEnv}) => {
/////////////开始///////////////////////
// const [filteredTreeData, setFilteredTreeData] = useState([]);
const [filteredTreeData, setFilteredTreeData] = useState([]);
const [searchValue, setSearchValue] = useState("");
const [typeList, setTypeList] = useState([]);
const [dataList, setDataList] = useState([]);
const [total, setTotal] = useState();
const [tagId, setTagId] = useState('');




const onChange = (e) => {
  const { value } = e.target;
  setSearchValue(value);
  if (value) {
    const filtered = filterTreeData(typeList, value);
    setFilteredTreeData(filtered);
  } else {
    setFilteredTreeData(typeList);
  }
};
const getTypeList = async () => {
    try {
        const data = await getApiTags({});

        console.log("我是类别数据")
        console.log(data)
        const parentList = data.filter((item)=>item.pid === 0);
        parentList.forEach((a)=>{
        a.title = a.name;
        a.key = a.id
        a.children = [];
        })
        const childList = data.filter((item)=>item.pid !== 0);
        parentList.forEach((item)=>{
        childList.forEach((list)=>{
            if(list.pid === item.id){
            item.children.push({
                title:list.name,
                key:list.id,
                pid:list.pid
            })
            }
        })
        })
        const filterData = parentList;
        setTypeList(parentList)
        setFilteredTreeData(filterData)
        console.log(filterData)

    } catch (error) {
      setTypeList([])
      setFilteredTreeData([])
    }
  }
const getApiListData = async (param) => {
    try {
        const dataParam = {
            ...param
        }
        const data = await getApiList(dataParam);
        console.log("我是api数据")
        console.log(data)
        setDataList(data.list || [])
        setTotal(data.total)
    } catch (error) {
      setDataList([])
      setTotal(0)
    }
  }
useEffect(() => {
    getTypeList()
    getApiListData({pageSize:10,pageNo:1,name:'',pubOrgName:'',tagId:''})
}, [currentEnv]);



const filterTreeData = (data, keyword) => {
  if(!keyword){
    setFilteredTreeData(data);
    return;
  }
  return data.reduce((acc, item) => {
    if (item.title.toLowerCase().includes(keyword.toLowerCase())) {
      acc.push(item);
    } else if (item.children && item.children.length > 0) {
      const children = filterTreeData(item.children, keyword);
      if (children.length > 0) {
        acc.push({ ...item, children });
      }
    }
    return acc;
  }, []);
};

  // const curEle = useRef();
  // const handleData = () => {
  //   let groupData = selectedGroup.length
  //     ? allData.filter((g) => selectedGroup.includes(g.groupId))
  //     : allData;

  //   const data = searchValue
  //     ? JSON.parse(JSON.stringify(groupData)).filter((i) => {
  //         let flag = false;
  //         if (i.group.includes(searchValue)) {
  //           flag = true;
  //         } else {
  //           i.children = i.children.filter((c) => c.name.includes(searchValue));
  //           flag = !!i.children.length;
  //         }
  //         return flag;
  //       })
  //     : groupData;

  //   setCurData(data);
  // };

  // useEffect(() => {
  //   handleData();
  // }, [allData, searchValue, selectedGroup]);

  const onSelect = (selectedKeys) => {
    setTagId(selectedKeys[0])
  };
  return (
    <div className="products-wrapper dealPadding" style={{height:'100%',backgroundColor:'#fff'}}>
      <div className="left-sidebar">
        <div>
            <div>
            <div className="title-left">导航</div>
            <Search placeholder="搜索" onChange={(e) => onChange(e)} style={{margin:'10px 0px 15px 0px'}}/>
            <Tree treeData={filteredTreeData} onSelect={(selectedKeys, info)=>{onSelect(selectedKeys, info)}}/>
            </div>
        </div>
      </div>
      <div className="main-content">
        {
            <ApiList prodcut={dataList} getList={getApiListData} total={total} tagId={tagId} currentEnv={currentEnv} />
        }
    </div>
    </div>
  );
};

const ProductService = () => {
  const selectedFeature = "提供统一的应用与技术组件API发布、检索与管理平台，支持与iPaaS平台集成适配，实现API全生命周期管理，并通过分类展示、卡片详情和多维度检索实现应用与API资源的快速查找与调用"
  const [currentEnv, setCurrentEnv] = useState('UAT');
  const selectedTitle ="API市场";
  useEffect(() => {
   localStorage.setItem('currentEnv',currentEnv.toLocaleLowerCase())
}, [currentEnv]);
  return (
    <>
      <div className="product-service">
        <div className="content-wrapper">
          {/* 页面标题 */}
          <div  className={`dealPadding header-box-api home-header  ${currentEnv === 'UAT' ? 'home-header-uat' : 'prod-header'}`}>
            <div className="header-content">
              <h1 style={{display:'flex',alignItems:'center'}}>
                <span>{selectedTitle}</span>
               <div style={{width:'150px',marginLeft:'20px'}}>
                <Segmented options={['UAT', 'PROD']} value={currentEnv} onChange={(value)=>{setCurrentEnv(value)}}  block 
                style={{ backgroundColor: '#D8D8D8' }} />
               </div>
              </h1>
              <div className="description">{selectedFeature}</div>
            </div>
          </div>
          <Products currentEnv={currentEnv} />
        </div>
      </div>
    </>
  );
};

export default ProductService;
