.pass-ser-api {
  display: flex;
  gap: 24px;
  padding: 24px;
  background: #fff;
  .card-info-item{
    color: #333;
    display: flex;
    justify-content: space-between;
    margin-top: 12px;
    .card-title-left{
      max-width: 100px;
    }
    .card-content-right{
      flex: 1;
      display: -webkit-box; /* 设置为WebKit内核的弹性盒子模型 */
      -webkit-box-orient: vertical; /* 垂直排列 */
      -webkit-line-clamp: 3; /* 限制显示两行 */
      overflow: hidden; /* 隐藏超出范围的内容 */
      text-overflow: ellipsis; /* 使用省略号 */
    }
  }
.card-header-main{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
  // 响应式调整
  @media screen and (min-width: 1920px) {
    .dealPadding {
      padding: 24px 480px;
    }
  }

  // 响应式调整
  @media screen and (max-width: 1920px) {
    .dealPadding {
      padding: 24px 240px;
    }
  }

  @media screen and (max-width: 1600px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  @media screen and (max-width: 1200px) {
    .dealPadding {
      padding: 24px 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .dealPadding {
      padding: 24px 20px;
    }

    .home-header {
      padding: 24px;

      h1 {
        font-size: 24px;
      }

      .description {
        font-size: 14px;
      }
    }
  }
  .left-sidebar {
    width: 240px;
    flex-shrink: 0;
    border-right: 1px solid #e8e8e8;
    border-radius: 8px;
    padding: 16px 0;
    background-color: #F4FAFE;
    .navigation {
      padding: 16px;
     
      .navigation-title {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .nav-indicator {
          width: 4px;
          height: 16px;
          background: #1890ff;
          margin-right: 8px;
          border-radius: 2px;
        }

        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }
      }

      .nav-menu {
        .nav-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 12px 16px;
          margin-bottom: 4px;
          border-radius: 4px;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            background: #E6F3FB;
          }

          &.active {
            background: #E6F3FB;
            color: #1890ff;
          }

          .nav-item-title {
            font-size: 14px;
          }

          .nav-item-count {
            color: #8c8c8c;
            font-size: 12px;
          }
        }
      }
    }

    .category-item {
      padding: 12px 24px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #333;
      font-size: 14px;
      position: relative;
      transition: all 0.3s ease;

      &:hover {
        color: #1890ff;
        background: #e6f7ff;
      }

      &.active {
        color: #1890ff;
        background: #E6F3FB;
        font-weight: 500;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #1890ff;
          border-radius: 0 2px 2px 0;
        }
      }

      .category-name {
        position: relative;
        padding-left: 8px;
      }

      .category-count {
        color: #999;
        font-size: 12px;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 10px;
      }
    }

    .category-tree {
      .micro-tree-list-holder-inner {
        background-color: #F4FAFE !important;
      }
      .ant-tree {
        .ant-tree-node-content-wrapper {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          transition: all 0.3s;
          
          &:hover {
            background-color: #e6f7ff;
          }
          
          &.ant-tree-node-selected {
            background-color: #E6F3FB;
            color: #1890ff;
            font-weight: 500;
          }
        }
        
        .ant-tree-switcher {
          width: 24px;
          height: 24px;
          line-height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .ant-tree-switcher-icon {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
          }
        }
        
        .ant-tree-node-content-wrapper {
          .ant-tree-title {
            margin-left: 8px;
            font-size: 14px;
            color: #333;
          }
          
          .ant-tree-iconEle {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            
            svg {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    min-width: 0;

    .search-bar {
      margin-bottom: 24px;
      background: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .search-title {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
        font-weight: 500;
      }

      .search-filters {
        display: flex;
        align-items: center;
        gap: 32px;

        .search-item {
          &.sort-item {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;

            span {
              color: rgba(0, 0, 0, 0.65);
              font-size: 14px;
              line-height: 22px;
            }

            &:hover {
              span {
                color: #1890ff;
              }
              .sort-arrows {
                .arrow {
                  &.up {
                    border-bottom-color: #1890ff;
                  }
                  &.down {
                    border-top-color: #1890ff;
                  }
                }
              }
            }

            .sort-arrows {
              display: flex;
              flex-direction: column;
              gap: 2px;
              margin-left: 4px;
              height: 12px;

              .arrow {
                width: 0;
                height: 0;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;

                &.up {
                  border-bottom: 4px solid rgba(0, 0, 0, 0.45);
                }

                &.down {
                  border-top: 4px solid rgba(0, 0, 0, 0.45);
                }
              }
            }
          }
        }

        .search-inputs {
          display: flex;
          gap: 16px;

          .ant-input {
            width: 200px;
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            padding: 8px 12px;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
            
            &::placeholder {
              color: rgba(0, 0, 0, 0.35);
            }

            &:hover {
              border-color: #40a9ff;
            }

            &:focus {
              border-color: #40a9ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              outline: none;
            }
          }
        }
      }
    }

    .api-list {
      display: grid;
      gap: 20px;
     
      @media screen and (min-width: 2000px) {
        grid-template-columns: repeat(5, minmax(300px, 380px));
        gap: 16px;
      }
      @media screen and (max-width: 1920px) {
        grid-template-columns: repeat(4, minmax(280px, 380px));
        gap: 20px;
      }

      @media screen and (max-width: 1600px) {
        grid-template-columns: repeat(3, minmax(280px, 380px));
        gap: 16px;
      }

      @media screen and (max-width: 1200px) {
        grid-template-columns: repeat(3, minmax(250px, 380px));
      }

      .api-card {
        background: #FFFFFF;
        border-radius: 8px;
        border: 1px solid #E6E6E6;
        transition: all 0.3s ease;
        overflow: hidden;

        @media screen and (min-width: 1920px) {
          min-height: 180px;
        }

        @media screen and (max-width: 1919px) {
          min-height: 160px;
        }

        @media screen and (max-width: 1600px) {
          min-height: 150px;
        }
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
        .card-header {
          padding: 12px 16px;
          .header-content {
            display: flex;
            align-items: flex-start;

            .api-logo {
              font-size: 24px;
              line-height: 48px;
              height: 48px !important;
              width: 48px !important;
              color: #fff;
              text-align: center;
              background-color: #ffbc03;
              border-radius: 6px;
              img {
                width: 100%;
                height: 100%;
                object-fit: contain;
              }
            }

            .api-name {
              font-size: 14px;
              font-weight: 400;
              color: #1D2129;
              margin: 0;
              flex: 1;
            }

            .api-tags {
              display: flex;
              gap: 8px;
              color: #8a8a8a;
              margin-top: 8px;
              .database-tag {
                background: #EBF5FF;
                color: #4B7FE8;
                padding: 1px 8px;
                border-radius: 2px;
                font-size: 12px;
                line-height: 20px;
                border: 1px solid #A3C6FF;
              }

              .version-tag {
                background: #F0F9EB;
                color: #67C23A;
                padding: 1px 8px;
                border-radius: 2px;
                font-size: 12px;
                line-height: 20px;
                border: 1px solid #B3E19D;
              }
            }
          }
        }

        .card-info {
          padding: 0px 16px;
          padding-bottom: 12px;
          .info-item {
            display: flex;
            font-size: 13px;
            line-height: 20px;
            margin-bottom: 4px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: #86909C;
              margin-right: 4px;
              width: 56px;
              flex-shrink: 0;
            }

            .value {
              color: #4E5969;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              flex: 1;
            }
          }
        }
      }
      .api-card-disabled {
        background: #EEEEEE !important;
        border-radius: 8px;
        border: 1px solid #E6E6E6;
        transition: all 0.3s ease;
        overflow: hidden;
      }
    }
  }
  .collected{
    color: #ffbb00;
  }
  .unCollected{
    color: #aaaaaa;
  }
} 