.login-page-bg {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	.video {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}
.login-page {
	height: 100%;
	background-color: rgba(0, 0, 0, 0);
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	z-index: 1;

	.header {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		padding: 50px;
		.logo {
			margin-right: 24px;
			width: 144px;
			height: 34px;
		}
		.info {
			margin-top: 10px;
			margin-left: 5px;
			width: 256px;
			height: 14px;
			font-size: 10px;
			font-family: PingFang SC-Regular, PingFang SC;
			font-weight: 100;
			color: #ffffff;
			line-height: 12px;
			letter-spacing: 16px;
		}
	}
	.main {
		display: flex;
		justify-content: space-between;
		height: 100%;
		width: 100%;
		position: absolute;
		top: 0;
		left: 0;
		align-items: center;

		.formBox {
			position: absolute;
			width: 368px;
			height: 420px;
			background: rgba(255, 255, 255, 0.01);
			overflow: hidden;
			box-shadow: 2px 4px 30px 0px rgb(0 0 0 / 29%);
			border-radius: 10px;
			opacity: 1;
			border: 1px solid rgba(255, 255, 255, 0.3);
			z-index: 1;
			right: 12%;
			.formboxbg {
				position: absolute;
				width: calc(100% + 2px);
				height: calc(100% + 2px);
				top: -1px;
				left: -1px;
				backdrop-filter: blur(20px);
			}
			.logo {
				position: relative;
				height: 32px;
				width: 100%;
				margin-top: 37px;
				display: flex;
				justify-content: center;
				img {
					margin-left: -6px;
				}
			}
			p {
				position: relative;
				z-index: 1;
				font-size: 14px;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: rgba(255, 255, 255, 0.85);
				margin-top: 11px;
				text-align: center;
				line-height: 16px;
				letter-spacing: 2px;
			}
			.form {
				width: 312px;
				max-width: 90%;
				margin: 0 auto;
				margin-top: 42px;
				.micro-form-item {
					margin-bottom: 12px;
					.micro-form-item-label > label {
						font-weight: 400;
						color: white;
						line-height: 18px;
						letter-spacing: 1px;
						font-family: PingFangSC-Medium, PingFang SC;
					}

					.micro-input-affix-wrapper {
						border-radius: 6px;
						border-color: rgba(255, 255, 255, 0);
						background-color: rgba(255, 255, 255, 0.2) !important;
						.micro-input {
							box-shadow: 0 0 0px 1000px rgba(255, 255, 255, 0)
								inset !important;
							background-color: rgba(255, 255, 255, 0) !important;
							color: #ffffff !important;
							&::placeholder {
								color: #bfbfbf;
							}
						}
						input:-internal-autofill-previewed,
						input:-internal-autofill-selected {
							background-color: rgba(255, 255, 255, 0) !important;
							transition: background-color 5000s ease-in-out 0s !important;
							-webkit-text-fill-color: #ffffff !important;
						}
						.anticon {
							color: #ffffff;
						}
						.micro-input-prefix {
							margin: 0 10px 0 3px;
						}
					}
					.hasValue {
						background-color: rgba(255, 255, 255, 0.4) !important;
					}

					.micro-input-affix-wrapper-focused {
						background-color: rgba(255, 255, 255, 0.4) !important;
					}
					.micro-btn {
						margin-top: 61px;
						border-radius: 40px;
						background-color: #1c52de;
					}
				}
			}
		}
	}
	.copyRight {
		bottom: 5%;
		left: 0;
		width: 100%;
		position: absolute;
		text-align: center;
		font-size: 12px;
		font-family: PingFangSC-Regular, PingFang SC;
		font-weight: 400;
		color: rgba(255, 255, 255, 0.2);
		line-height: 14px;
	}
}
