import React, { useEffect, useState } from "react";
import { Modal, Form, Input, Popover, message } from "antd";
import { CloseCircleFilled, CheckCircleFilled } from "@ant-design/icons";
import JSEncrypt from "jsencrypt";
import locale from "@/locale";

const { Item, useForm } = Form;
const { Password } = Input;

let encryptor = new JSEncrypt();
encryptor.setPublicKey(
  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoUYIgzTFpppvfYudUWdAl38Q5NxzuP/msHDDep9Khy0dB3E2BmgtpeKHw0IOQWVAbUNm5vEuyrghskaPlrEttAfxk1b56BiOnPsjb4Q9wNW5FMTOD1pio8WO8r9lyj7KmJcmThavGWJBjP8utepnHo6ppFCkIEv4T/7aW4/LHMm/rckYHJyafh3K+/AQbYHN/TSFnC/xmhDwbHPB3ymBwiepuYTyCVmnBRy6a1b0IKamKcfjmhmTIA4G1ThfhO45Vv70CVlwNt+ta8HmK9kwQfjMUnFGE/fLBOsHUGDFsaJPgDuMCZ5OLnJbLELg4PyBqf9XWpXI3t2M6JeYoVnkzQIDAQAB"
);
function ModifyPassword(props) {
  const { visible, setVisible, username, onFinish } = props;
  const [checks, setChecks] = useState([false, false, false, false]);
  const [form] = useForm();

  useEffect(() => {
    form.resetFields();
  }, [visible]);

  const defaultTrigger = (
    <Form.Item
      label={locale.user.new_pwd}
      labelAlign="left"
      name="newPassword"
      rules={[{ required: true, message: locale.user.input_new_pwd }]}
    >
      <Input.Password onChange={(e) => handleChange(e.target.value, "new")} />
    </Form.Item>
  );

  const onOk = () => {
    form.validateFields().then((values) => {
      if (checks.includes(false)) {
        message.warning(locale.user.pwd_format_error);
        return;
      }
      if (values.reNewPassword !== values.newPassword) {
        message.warning(locale.user.pwd_twice_diff);
        return;
      }
      const sendData = {
        newPassword: encryptor.encrypt(values.reNewPassword),
        password: encryptor.encrypt(values.password),
        secondNewPassword: encryptor.encrypt(values.newPassword),
        organId: "1",
      };
      onFinish(sendData);
    });
  };

  const handleChange = (value, type) => {
    if (type === "new") {
      const temp = [...checks];
      if (/[A-Za-z]/.test(value)) {
        temp[0] = true;
      } else {
        temp[0] = false;
      }
      if (/\d/.test(value)) {
        temp[1] = true;
      } else {
        temp[1] = false;
      }
      if (value.includes(".") || value.includes("@") || value.includes("-")) {
        temp[2] = true;
      } else {
        temp[2] = false;
      }
      if (value.length >= 8 && value.length <= 16) {
        temp[3] = true;
      } else {
        temp[3] = false;
      }
      setChecks(temp);
    } else {
      const newValue = form.getFieldValue("newPassword");
    }
  };

  return (
    <div>
      <Modal
        open={visible}
        title={locale.header.modify_password}
        forceRender
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onOk}
      >
        <Form
          name="modifyPassword"
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 21 }}
          labelAlign="left"
        >
          <Item
            label={locale.user.old_pwd}
            name="password"
            required
            rules={[
              {
                required: true,
                message: locale.user.input_old_pwd,
              },
            ]}
          >
            <Password type="password"></Password>
          </Item>
          <Popover
            content={
              <div>
                <ul>
                  <li className={"edit-form-icon-style"}>
                    {checks[0] ? (
                      <CheckCircleFilled
                        style={{
                          color: "#68B642",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    ) : (
                      <CloseCircleFilled
                        style={{
                          color: "#Ef595C",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    )}
                    <span>{locale.user.pwd_format_one}</span>
                  </li>
                  <li className={"edit-form-icon-style"}>
                    {checks[1] ? (
                      <CheckCircleFilled
                        style={{
                          color: "#68B642",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    ) : (
                      <CloseCircleFilled
                        style={{
                          color: "#Ef595C",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    )}
                    <span>{locale.user.pwd_format_two}</span>
                  </li>
                  <li className={"edit-form-icon-style"}>
                    {checks[2] ? (
                      <CheckCircleFilled
                        style={{
                          color: "#68B642",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    ) : (
                      <CloseCircleFilled
                        style={{
                          color: "#Ef595C",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    )}
                    <span>
                      &quot;.&quot;{locale.common.or}
                      &quot;@&quot;{locale.common.or}
                      &quot;-&quot;
                    </span>
                  </li>
                  <li className="edit-form-icon-style">
                    {checks[3] ? (
                      <CheckCircleFilled
                        style={{
                          color: "#68B642",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    ) : (
                      <CloseCircleFilled
                        style={{
                          color: "#Ef595C",
                          marginRight: 4,
                          lineHeight: "20px",
                        }}
                      />
                    )}
                    <span>
                      {locale.user.pwd_length}
                      {(form.getFieldValue("newPassword") || "").length}(
                      {locale.user.pwd_format_length})
                    </span>
                  </li>
                </ul>
                {locale.user.pwd_format_all}
              </div>
            }
            placement="right"
          >
            {defaultTrigger}
          </Popover>
          <Item
            label={locale.user.new_pwd_confirm}
            required
            name="reNewPassword"
          >
            <Password
              type="password"
              onChange={(e) => handleChange(e.target.value, "reNew")}
            ></Password>
          </Item>
        </Form>
      </Modal>
    </div>
  );
}

export default ModifyPassword;
