import React, { useState, useRef, useEffect } from "react";
import { Button, Form, Input, Modal, notification } from "antd";
import { UserOutlined } from "@ant-design/icons";
import cookie from "react-cookies";
import { useModel, history, useLocation } from "umi";
import jsencrypt from "jsencrypt";
import ModifyPassword from "./modifyPassword";
import Local from "@/utils/storage";
import locale from "@/locale";
import "./index.less";
import 'antd/es/message/style/css';
import 'antd/es/modal/style/css';
import 'antd/es/notification/style/css';

import { accountSignIn, changeUserPassword } from "@/services/user";
import { getParamsFromSearchParams } from "@/utils/url";

const { useForm } = Form;
const encryptor = new jsencrypt();
encryptor.setPublicKey(
  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAoUYIgzTFpppvfYudUWdAl38Q5NxzuP/msHDDep9Khy0dB3E2BmgtpeKHw0IOQWVAbUNm5vEuyrghskaPlrEttAfxk1b56BiOnPsjb4Q9wNW5FMTOD1pio8WO8r9lyj7KmJcmThavGWJBjP8utepnHo6ppFCkIEv4T/7aW4/LHMm/rckYHJyafh3K+/AQbYHN/TSFnC/xmhDwbHPB3ymBwiepuYTyCVmnBRy6a1b0IKamKcfjmhmTIA4G1ThfhO45Vv70CVlwNt+ta8HmK9kwQfjMUnFGE/fLBOsHUGDFsaJPgDuMCZ5OLnJbLELg4PyBqf9XWpXI3t2M6JeYoVnkzQIDAQAB"
);

function Login() {
  const { setToken } = useModel("user");
  const { fetchWorkspaceConfig } = useModel("nav");
  const { bgImageName, copyRight, platformName, slogan } =
    useModel("sysConfig");
  const [uHasV, setUHasV] = useState(false);
  const [pHasV, setPHasV] = useState(false);

  const [loginLoading, setLoginLoading] = useState(false);
  const [passwordVisible, setPasswordVisible] = useState(false);
  const [form] = useForm();
  const tokenRef = useRef();
  const location = useLocation();
  const urlParams = getParamsFromSearchParams(location.search);

  useEffect(() => {
    Local.removeSession("all", true);
    Local.setLocal("token", null);
    Local.setLocal("userToken", null);
    Local.setLocal("userInfo", null);
  }, []);

  const isvideo = () => {
    let type = bgImageName ? bgImageName.split(".")[1] : "";
    return type === "mp4";
  };
  const handleLogin = () => {
    const isFromBackend = window.location.search.includes("isBackend");
    setToken(tokenRef.current);
    cookie.save("token", tokenRef.current, { path: "/" });
    Local.setLocal("token", tokenRef.current);
    Local.setLocal("userToken", tokenRef.current);
    if (isFromBackend) {
      // 新开页签，复制url
      window.history.pushState(null, null, "?isBackend=1#/");
      history.replace("/");
    } else {
      if (urlParams.back) {
        history.replace(urlParams.back);
      } else {
        fetchWorkspaceConfig(true);
      }
    }
  };
  /**
   * 用户登录
   */
  const onFinish = async (values) => {
    if (!values.password || !values.username) {
      notification.error({
        message: locale.notice.err,
        description: locale.user.input_user_password,
      });
      return;
    }
    const params = {
      username: values.username.trim(),
      password: encryptor.encrypt(values.password),
    };
    accountSignIn(params)
      .then((res) => {
        tokenRef.current = res.token;
        const passwordExpired = res.passwordExpired;
        if (passwordExpired === 4) {
          // 用户第一次登录，先进行密码修改
          setPasswordVisible(true);
        } else if (passwordExpired === 2) {
          // 用户密码即将过期提示
          Modal.info({
            title: "提示",
            content: "您的密码即将过期，请尽快修改密码",
            onOk() {
              handleLogin();
            },
          });
        } else if (passwordExpired === 3) {
          // 用户密码已经过期
          Modal.warning({
            title: "提示",
            content: "您的密码已过期，请先修改密码",
            onOk() {
              setPasswordVisible(true);
            },
          });
        } else {
          // 用户正常登录
          handleLogin();
        }
      })
      .finally(() => {
        setLoginLoading(false);
      });
  };

  // 修改密码
  const changePassword = (data) => {
    console.log("test", data, tokenRef.current);
    changeUserPassword(data, tokenRef.current).then(() => {
      handleLogin();
    });
  };

  return (
    <>
      <div className="login-page">
        <div className="header">
          <p className="logo">
            <img
              width={146}
              height={32}
              src={`${
                window.location.origin
              }/app-management/images/logo/login-logo.svg?noCache=${new Date().getTime()}`}
            />
          </p>
          <p className="info">
            <span>{slogan}</span>
          </p>
        </div>
        <div className="main">
          <div className="formBox">
            <div className="formboxbg"></div>
            <div className="logo">
              <img
                width={146}
                height={32}
                src={`${
                  window.location.origin
                }/app-management/images/logo/formBox-logo.svg?noCache=${new Date().getTime()}`}
              />
            </div>
            <p>
              <span>{platformName}</span>
            </p>
            <Form
              form={form}
              name="basic"
              className="form"
              layout="vertical"
              onFinish={onFinish}
              autoComplete="off"
            >
              <Form.Item
                label={locale.user.account}
                name="username"
                rules={[
                  {
                    message: locale.user.input_user,
                  },
                ]}
              >
                <Input
                  placeholder={locale.user.input_user}
                  prefix={<UserOutlined />}
                  onBlur={(e) => {
                    setUHasV(e.target.value ? true : false);
                  }}
                  className={`${uHasV ? "hasValue" : "hasnoValue"}`}
                />
              </Form.Item>

              <Form.Item
                label={locale.user.pwd}
                name="password"
                rules={[
                  {
                    message: locale.user.input_pwd,
                  },
                ]}
              >
                <Input.Password
                  onBlur={(e) => {
                    setPHasV(e.target.value ? true : false);
                  }}
                  className={`${pHasV ? "hasValue" : "hasnoValue"}`}
                  placeholder={locale.user.input_pwd}
                  prefix={
                    <img
                      src={`${
                        window.location.origin
                      }/app-management/images/logo/password.svg?noCache=${new Date().getTime()}`}
                    />
                  }
                />
              </Form.Item>

              <Form.Item>
                <Button
                  block
                  type="primary"
                  htmlType="submit"
                  loading={loginLoading}
                >
                  {locale.user.login}
                </Button>
              </Form.Item>
            </Form>
          </div>
        </div>
        <div className="copyRight">
          <pre>{copyRight}</pre>
        </div>
        <ModifyPassword
          username={form.getFieldValue("username")}
          onFinish={changePassword}
          visible={passwordVisible}
          setVisible={setPasswordVisible}
        />
      </div>
      <div className="login-page-bg">
        {isvideo() ? (
          <video
            className="video"
            src={`${
              window.location.origin
            }/app-management/images/logo/${encodeURIComponent(bgImageName)}`}
            autoPlay
            loop
            muted
          ></video>
        ) : (
          <img
            className="video"
            src={`${
              window.location.origin
            }/app-management/images/logo/${encodeURIComponent(bgImageName)}`}
          ></img>
        )}
      </div>
    </>
  );
}

export default Login;
