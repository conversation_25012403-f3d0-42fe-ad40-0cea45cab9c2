import React, { useEffect, useRef, useState } from "react";
import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Dropdown, Menu  } from 'antd';
const { RangePicker } = DatePicker;
import { scaffoldTemplate,techStackRank,docCount,apiCount ,statisticApp,statisticMiddle,getUserList,getUserSysCommit,getStatistics,getVisitCount,getDocCount,getApiCount,
  getPassCount,getShareCount,getScaffoldRank,getPipelineCount,getAppSystemListAll,
  getBussinessDomainslabel,
  getOpenSourceCount,
  statisticDomain,
  domainMapList,
  levelMapList,
  constructMapList} from '@/services';
import * as echarts from 'echarts';
import './platformBoard.less';
import CommenTextCard from './Component/CommenTextCard';
import PlatTableCard from './Component/PlatTableCard';
import CommenDataCard from './Component/CommenDataCard';
import  platTitle from '@/assets/images/platTitle.png';
import PaasBlockCard from './Component/PaasBlockCard';
import MiddleUsageGauge from './bar';

import Code from './Component/LineCard';
import  codeImg from '@/assets/images/code.png';
const dateFormat = 'YYYY-MM-DD';
import { useModel } from 'umi';
import dayjs from 'dayjs';

const PlatformBoardNew = () => {
 
  const [date, setDate] = useState('全部');
  const [selUser, setSelUser] = useState([]);

  const { userInfo } = useModel('user');
  
  ///////////////////////////////////////开始//////////////////////////////////
  // const [appData, setAppData] = useState([]);
  // const [appTitle, setAppTitle] = useState([]);
  const [appInfo, setAppInfo] = useState({});
  const [statisticsInfo, setStatisticsInfo] = useState({});
  const [visitInfo, setVisitInfo] = useState({});
  const [docinfo, setDocInfo] = useState({});
  const [apiInfo, setApiInfo] = useState({});
  const [openSourceInfo, setOpenSourceInfo] = useState({});
  const [passInfo, setPassInfo] = useState({});
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const formattedDate = oneMonthAgo.toISOString().split('T')[0];
  const formatteToday = today.toISOString().split('T')[0];
  const formattedDateTime = `${formattedDate} 00:00:00`;
  const formatteTodayTime = `${formatteToday} 23:59:59`;
console.log(formattedDateTime)
  const [timeRange, setTimeRange] = useState( [formattedDateTime,formatteTodayTime]);
  const [startTime, setStartTime] = useState(formattedDate);
  const [endTime, setEndTime] = useState(formatteToday);
  const [scaffoldInfo, setScaffoldInfo] = useState({});
  const [appList, setAppList] = useState([]);
  const [applicationId, setApplicationId] = useState();
  const [lineInfo, setLineInfo] = useState({});
  const [shareInfo, setShareInfo] = useState({});
  const [appSystemType, setAppSystemType] = useState('1');
  const lineRef = useRef(null)
  //应用系统统计
  const getStatisticApp = () => {
    // const dataParam = {
    //   startTime:endTime?`${endTime} 00:00:00`:'',
    //   endTime:startTime?`${startTime} 23:59:59`:''
    // }

    statisticApp().then((res) => {
      if(res){
        if(res.apps?.length){
          res.apps.forEach((item)=>{
            if(item.stageStatus === 'Constructing'){
              item.stageStatusName = '一级'
            }else if(item.stageStatus === 'Planning'){
              item.stageStatusName = '二级'
            }else if(item.stageStatus === 'OnlineApplying'){
              item.stageStatusName = '三级'
            }
          })
        }
        const data = res.apps || [];
        console.log(data)
        const appData = data.map(item => item.num);
        const appTitle = data.map(item => item.stageStatusName);
        console.log(appData)
        setAppInfo((prevParams) => ({
          ...prevParams,
          total:res.count,
          increase:res.increase
        }));
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  const getStatisticDomain = async () => {
    const params = {
      type: appSystemType
    }
    const fn = appSystemType === '1'? domainMapList : (appSystemType === '2' ? levelMapList : constructMapList)
    const mapRes = await fn();
    const mapObj = {};
    mapRes.forEach(item => {
      mapObj[item.value] = item.label;
    });
    statisticDomain(params).then(res => {
      const dataList = []
      const nameList = []
      res.forEach(item => {
        dataList.push(item.num)
        const label = mapObj[item.name];
        if (label) {
          nameList.push(label);
        } else {
            // 如果找不到对应的 label，就使用原来的 item.name
          nameList.push(item.name);
        }
      })

      setAppInfo((prevParams) => ({
        ...prevParams,
        dataList: dataList,
        dataTitle: nameList,
      }));
    })
  }
  const getStatisticLevel = async () => {
    const types = [1, 2, 3];
    // 创建 type 与 stageStatusName 的映射
    const mapRes = await levelMapList();
    const mapObj = {};
    mapRes.forEach(item => {
      mapObj[item.value] = item.label;
    });
    const params = {
      type: 2
    };
    // 并行发送请求
    statisticDomain(params).then(results => {
      const allDataList = [];

      results.forEach((item, index) => {
        allDataList.push({
          num: item.num,
          stageStatusName: mapObj[item.name]
        });
      });
      // 更新应用信息
      setAppInfo((prevParams) => ({
        ...prevParams,
        data: allDataList
      }));
    }).catch(error => {
        console.error('请求出错:', error);
    });
  }
  const handleInfoChange = (val) => {
    setAppSystemType(val)
  }
//应用程序统计
const getStatisticsInfo = () => {
  getStatistics().then((res) => {
    if(res){
      const data = res.technicalStackStats || [];
      // console.log(data)
      // const appData = data.map(item => item.count);
      // const appTitle = data.map(item => item.technical_stack_tags); 
      const dataList = [];
      data.forEach((item)=>{
        dataList.push({
          name:item.technical_stack_tags,
          value: item.count
        })
      })
      setStatisticsInfo({
        total:res.totalCount,
        dataList:dataList,
        increase:res.newApplicationsThisMonth
      })
       
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
//文档浏览统计
const getDocCountInfo = () => {
  getDocCount().then((res) => {
    if(res){
      setDocInfo(res)
    }
  }).catch((data) => {
    message.error('接口异常')
  })
}
//api统计
const getApiCountInfo = () => {
  getApiCount().then((res) => {
    if(res){
      const data =res.apiDistributionList || [];
      const dataList = [];
      data.forEach((item)=>{
        dataList.push({
          name:item.systemName,
          value: item.apiCount
        })
      })
      setApiInfo({
        info:res,
        dataList:dataList
        
      })  
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
// 开源组件使用情况统计
const getOpenSourceCountInfo = () => {
  getOpenSourceCount().then((res) => {
    if(res){
      const data =res.top10List || [];
      const dataPoor = [];
      const dataAll = [];
      const nameList = [];
      data.forEach((item)=>{
        dataPoor.push(item.value - item.baseValue)
        dataAll.push(item.baseValue)
        nameList.push(item.name)
      })
      setOpenSourceInfo({
        info: res,
        dataList: dataAll,
        dataPoor: dataPoor,
        dataTitle: nameList
      })  
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
//用户访问统计
const getVisitCountInfo = () => {
  const param={
    startTime:startTime?`${startTime} 00:00:00`:'',
    endTime:endTime?`${endTime} 23:59:59`:'',
  }
  getVisitCount(param).then((res) => {
    if(res){
      const data = res.visitSourceList || [];
      const dataList = [];
      let totalVisitCount = 0;
      data.forEach((item) => {
        totalVisitCount += item.visitCount;
      });
      data.forEach((item)=>{
        const percent = totalVisitCount === 0 ? 0 : (item.visitCount / totalVisitCount) * 100;
        dataList.push({
          name:item.sourceName,
          value: item.visitCount,
          percent: percent === 0 ? 0 : percent.toFixed(1)
        })
      })
      setVisitInfo({
        total:res.totalVisitCount,
        dataList:dataList
      })
       
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
//paas服务
const getPassCountInfo = () => {
  getPassCount().then((res) => {
    if(res){
     if(res.cpu){
        res.usedCpu = res.cpu.split('/')[0];
        res.totalCpu = res.cpu.split('/')[1];
     }
     if(res.memory){
      res.usedMemory = res.memory.split('/')[0];
      res.totalMemory = res.memory.split('/')[1];
    }
    if(res.storage){
      res.usedStorage = res.storage.split('/')[0];
      res.totalStorage = res.storage.split('/')[1];
    }
    const data = res.serviceApplyList || [];
    console.log(data)
    const appData = data.map(item => item.applyCount);
    const appTitle = data.map(item => item.serviceName);
    setPassInfo({
      data:res,
      appData:appData,
      appTitle:appTitle
    })
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}

//共享组件服务
const getShareCountInfo = () => {
  getShareCount().then((res) => {
    if(res){
      let keysArray =[];
      const infoList = res.trendMap || [];
      keysArray = Object.keys(infoList);
      setShareInfo({
        count:res.componentCount,
        dataList:res.trendMap,
        title:keysArray
      })
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
//脚手架
const getScaffoldRankData = () => {
  getScaffoldRank().then((res) => {
    if(res){
      const data = res.apps || [];
      console.log(data)
      const appData = data.map(item => item.count);
      const appTitle = data.map(item => item.scaffoldTemplateName);
      console.log(appData)
      setScaffoldInfo({
        total:res.count,
        dataList:appData,
        dataTitle:appTitle,
   
      })
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
const onSearch = (value) => {
  setName(value);
};
const filterOption = (input, option) => {
  return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
};
//获取全部应用系统
const getAppList = () => {
  getAppSystemListAll({
    isAll:true
  }).then((res) => {
    if(res){
    setAppList(res)
    if(res.length){
      setApplicationId(res[0].id)
    }
    
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
const [options, setOptions] = useState([]);
const [domain, setDomain] = useState('');
const fetchOptions = () => {
  getBussinessDomainslabel().then((response) => {
    if(response && response.records.length > 0){
      setOptions(response.records)
      setDomain(response.records[0].value)
      // getCodeCommit(response.records[0].value)
    }
  }).catch((data) => {
    return [];
  })
}
useEffect(() => {
  fetchOptions()
}, []);
//流水线
const getPipelineCountInfo = () => {
  const param={
    domain:domain
  }
  getPipelineCount(param).then((res) => {
    if(res){
      const dataList =[];
      dataList.push({
        value:res.failCount,
        name:`执行失败`
      })
      dataList.push({
        value:res.successCount,
        name:`执行成功`
      })
      // setTotal(res.count)
      // setData(res.apps)
      setLineInfo({
        total:res.totalCount,
        data:dataList
      })
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
useEffect(() => {
  if (lineInfo?.total != null && lineInfo?.data != null){
      //流水线
      initLine(lineInfo.total,lineInfo.data)
  }

}, [lineInfo]);


//获取代码提交
const initLine = (total=0,data) => {
 
  const chart = echarts.init(lineRef.current);
  const option = {
    title: {
    text: `${total}次`,
    left: 'center',
    top: 'center',
    textStyle: {
      color: '#2A2A2A',
      fontSize: 20,
      fontWeight: 'bold'
    }
  },
  color: ['#43B5F4', '#89CFF5'],
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',  
    bottom: '15%',
    left: 'right',
  
  },
  series: [
    {
     
      type: 'pie',
      radius: ['60%', '80%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
  
      labelLine: {
        show: false
      },
      data:data
    }
  ]
};
  chart.setOption(option);
  return () => {
    chart.dispose();
  };
}
// //获取代码提交
// const getCodeCommit = (info) => {
//   const dataParam = {
//     startTime:endTime?`${endTime} 00:00:00`:'',
//     endTime:startTime?`${startTime} 23:59:59`:'',
//     username:info?info.username:selUser[0]?.username,
//     id:info?info.id:selUser[0]?.id
//   }
//   if(!dataParam.username){
//     dataParam.username = '';
//   }
//   getUserSysCommit(dataParam).then((res) => {
//     if(res){
//       setCommitInfo(res)
//     }
//   }).catch((data) => {
//     message.error(data?.msg)
//   })
// }

  //改变日期
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

  const handleDateChange = (value) => {
    setDate(value);
    if(value === '全部'){
      setStartTime('')
      setEndTime('')
      return;
    }
    // 获取当前日期
    const startDate = new Date();
    let endDate;
    if(value == 0.5){
      // 创建结束日期（复制当前日期）
      endDate = new Date(startDate);
      // 往前推6个月
      endDate.setMonth(endDate.getMonth() - 6);
    }else if(value == 1){
      endDate = new Date();
      endDate.setFullYear(startDate.getFullYear() - 1);

    }else if(value == 2){
      endDate = new Date();
      endDate.setFullYear(startDate.getFullYear() - 2);
    }
    setStartTime(formatDate(endDate))
    setEndTime(formatDate(startDate))
  };


  
  


useEffect(() => {
  if(!domain){
    return;
  }
  //流水线
  getPipelineCountInfo()
}, [domain]);

  useEffect(() => {
    getStatisticDomain()
  }, [appSystemType])
 
  useEffect(() => {
    ///////////////////////开始////////////////////
    //应用系统统计
    getStatisticApp()
    getStatisticLevel()
    //应用程序统计
    getStatisticsInfo()
    //用户访问统计
    getVisitCountInfo()
    //文档浏览统计
    getDocCountInfo()
    //api统计
    getApiCountInfo()
    // 开源组件使用情况统计
    getOpenSourceCountInfo()
    //pass服务统计
    getPassCountInfo()
    //共享组件
    getShareCountInfo()
    //应用程序脚手架
    getScaffoldRankData()
    // //流水线
    // getPipelineCountInfo()
    //获取全部应用系统
    getAppList()
   
  
  }, [date,startTime,endTime]);
   


  // useEffect(() => {
  //   //获取代码提交记录
  //   if(selUser.length === 0){
  //     getCodeCommit(userInfo)
  //   }else{
  //     getCodeCommit()
  //   }
    
  // }, [date,selUser,startTime,endTime]);
  return (
    <div className="platformBoard">
      <div className="platformBoard-header">
        <div style={{display:'flex',alignItems:'center',justifyContent:'space-between'}}> 
          <div className='flex-app' style={{marginBottom:'25px'}}>
              <div className='header-icon'></div>
              <div>概览</div>
          </div>
          <div>
            <Space>
                <RangePicker format={dateFormat} 
                defaultValue={[dayjs(timeRange[0], 'YYYY/MM/DD HH:mm:ss'), dayjs(timeRange[1], 'YYYY/MM/DD HH:mm:ss')]}
                   onChange={(value, dateString) => {
                  setEndTime(dateString[1]);setStartTime(dateString[0])
                }} />
                <Select  style={{width:'200px'}} onChange={handleDateChange} value={date}>
                  <Option key={"全部"} value={"全部"}>全部</Option>
                  <Option key={"0.5"} value={"0.5"}>近半年</Option>
                  <Option key={"1"} value={"1"}>近1年</Option>
                  <Option key={"2"} value={"2"}>近2年</Option>
                </Select>
            </Space>
          </div>
        </div>
      </div>
      <div className="platformBoard-content ">
      <div style={{display:'flex',marginBottom:'10px',marginRight:'-10px'}}>
        <CommenTextCard info={appInfo} title={"应用系统统计"} type="application" handleChange={handleInfoChange}/>
        <CommenTextCard info={statisticsInfo} title={"应用程序统计"} type="app"  />
        
      </div>
       <div style={{display:'flex',marginBottom:'10px'}}>
            <CommenTextCard info={visitInfo} title={"用户访问统计"} type="visit"/>
            <CommenDataCard data={docinfo} title="文档浏览统计" type="doc"/>
            <CommenDataCard data={apiInfo} title="API统计" type="api"/>
       </div>
          <div style={{display:'flex',marginBottom:'10px'}}>
            <PaasBlockCard data={passInfo} title="PaaS服务统计" type="paas" widthValue={'48%'}/>
            <CommenTextCard info={scaffoldInfo} title={"应用程序脚手架模板"} type="scaffold" />
          </div>
          <div style={{height:'325px',display:'flex',alignItems:'center', marginBottom: '10px'}}>
                <PlatTableCard title={"应用系统维度代码提交统计"} />
                <Card  className="platformBoard-content-box" style={{width:'35%',height:'100%'}}>
                    <div className="commen-flex">
                      <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>流水线使用情况</span></p>
                      {/* <Select
                        value={applicationId}
                        onChange={(value) => {
                            setApplicationId(value);
                        }}
                        showSearch
                        onSearch={onSearch}
                        filterOption={filterOption}
                        style={{ width: '200px' }}
                      >
                      {appList.map((item) => (
                        <Option key={item.id} value={item.id}>
                            {item.cnName}
                        </Option>
                    ))}
                      </Select> */}
                      <Select
                        showSearch
                        value={domain}
                        placeholder={"输入名称搜索"}
                        filterOption={filterOption}
                        onSearch={onSearch}
                        onChange={(value) => {
                          setDomain(value)
                      }}
                        style={{ width: '200px' }}
                      >
                        {options.map((item) => (
                          <Option key={item.value} value={item.value}>
                            {item.value}
                          </Option>
                        ))}
                      </Select>
                  </div>
                  <div className="content-main">
                      <div style={{width:'100%'}}>
                      <div className="echarts-container" style={{height:'200px',width:'100%'}}>
                        <div ref={lineRef} style={{ width: '100%', height: '100%' }} /> 
                      </div>
                          
                      </div>             
                  </div>
              </Card>
        </div>
        <div style={{display:'flex',marginBottom:'10px'}}>
          <CommenDataCard data={openSourceInfo} title="开源组件使用情况统计" type="component"/>
          <PaasBlockCard data={shareInfo} title="共享组件统计" type="component"/>
        </div>
      </div>
    </div>
  );
};


export default PlatformBoardNew;




