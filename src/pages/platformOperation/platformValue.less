.platformValue{
    padding: 16px;
    .platformValue-content {
        .content-item {
            .box-title {
                border-bottom: 3px solid #008DDC;
                margin-bottom: 10px;

                .title-name {
                    color: #008DDC;
                    font-size: 18px;
                    line-height: 30px;
                }
            }

            .top-content {
                height: 180px;
                background-color: #ffffff;

                .top-text {
                    font-size: 18px;
                    height: 34px;
                    line-height: 34px;
                    border-radius: 5px;
                    background-color: #EFFAFF;
                    margin: 25px 0;

                    span {
                        display: inline-block;
                        text-align: center;
                    }
                    span:nth-child(1) {
                        width: 40%;
                        background-color: #1A9FE7;
                        color: #fff;
                        border-bottom-left-radius: 5px;
                        border-top-left-radius: 5px;
                    }
                    span:nth-child(2) {
                        width: 60%;
                        color: #1A9FE7;
                    }
                }

                .block {
                    width: calc(50% - 9px);
                    height: calc(50% - 15px);
                    text-align: center;
                    border-radius: 5px;
                    background: linear-gradient(to bottom, #cbebff, #ffffff);
                }

                .middle-right {
                    background-size: 83px 96px;
                    background-repeat: no-repeat;
                    background-position: center;
                    height: 96px;
                    width: 83px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin: auto;
                }
            }
            .bottom-content {
                height: 260px;
                background-color: #ffffff;
                .left-l {
                    border: 1px solid #1A9FE7;
                    border-radius: 5px;

                    .left-l-t {
                        height: 76px;
                        line-height: 76px;
                        color: #fff;
                        background-color: #1A9FE7;
                        font-size: 20px;
                    }
                    .left-l-b {
                        height: 124px;
                        color: #1A9FE7;
                        background-color: #fff;
                        font-size: 20px;
                        padding-top: 32px;
                    }
                }
                .left-r {
                    padding-top: 20px;
                    text-align: left;

                    span {
                        color: #91C9E8;
                        font-size: 24px;
                        font-weight: 600;
                        margin-left: 5px;
                    }
                }
                .right-r {
                    height: 66px;
                    line-height: 66px;
                    margin-top: 10px;
                    background-color: #ffffff;
                    border: 1px solid #C2EEFF;
                    box-sizing: border-box;
                    font-size: 18px;
                    border-radius: 10px;
                    
                    span {
                        display: inline-block;
                        text-align: left;
                        text-indent: 20px;
                        color: #1A9FE7;
                        font-weight: 600;
                    }
                    .right-r-text {
                        width: 70%;
                        background-color: #E6F8FF;
                        border-top-left-radius: 10px;
                        border-bottom-left-radius: 10px;
                    }
                    .right-r-num {
                        width: 30%;
                        text-indent: 35%;
                    }
                }
            }
            .bottom-title {
                height: 80px;
                line-height: 80px;
                text-align: center;
                background-color: #C3E2FC;
                color: #1A9FE7;
                font-weight: 500;
            }
        }
    }
   
}