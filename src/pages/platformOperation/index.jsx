import React, { useEffect, useRef, useState } from "react";
import { Input, Space , Affix, Menu , Checkbox,message,Empty } from "antd";
import "./index.less";
import "./platform.less";
import { history } from "umi";

// import MySubscriptions from './apiManageComponent/MySubscriptions'
// import SubscripLogC from './apiManageComponent/SubscripLog'
import {
  PieChartOutlined
} from '@ant-design/icons';
import { Link, Outlet } from 'umi';





const apiManage = ({searchValue}) => {

  const [current, setCurrent] = useState('1');
  
  useEffect(() => {  
  getList()       
  }, [searchValue]);

  const getList = () => {
   
  }

  const onClick = (e) => {
  
    setCurrent(e.key);
  };
  return (
    <div className="dealPadding products-api-manage">
      <div>
        <div className="sidebar sidebar-expand">
          <div className="sidebar-content">
            <Menu mode="vertical"  selectedKeys={[current]} style={{border:'0px'}} onClick={onClick}>
              <Menu.Item key="1">
                <Link to="/platformOperation/platformBoard"><Space><PieChartOutlined /><span>平台运营看板</span></Space></Link>
                <Link to="/platformOperation/middleplatformBoard"><Space><PieChartOutlined /><span>中间件运营看板</span></Space></Link>
              </Menu.Item>
             
             
            </Menu>
          
          </div>
        
        </div>
      </div>

      <div className="main-content">
      
        <>
        <Outlet />
        </>
       
      
       
     
        
      </div>
    </div>
  );
};


export default apiManage;
