import React, { useEffect, useRef, useState } from "react";
import {DatePicker ,Input,Button ,Table, Tag,message,Col, Row,Select,Space,Card,Dropdown  } from 'antd';
import './platformBoard.less';
import { getAppsList,getDomainsApp,getSummary,getDomainslabel} from '@/services';
import {
  ProfileOutlined
} from '@ant-design/icons';
const ApplicationBoard = () => {
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [appDomain, setAppDomain] = useState();
  const [appId, setAppId] = useState();
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [domainList, setDomainList] = useState([]);
  const [appList, setAppList] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [summary, setSummary] = useState({});
  const [summaryOrigin, setSummaryOrigin] = useState({});
  const [domainDatatitleList, setDomainDatatitleList] = useState([])
  const [domainslabelList, setDomainslabelList] = useState([])
  const [newData, setNewData] = useState([])
  
  
  useEffect(() => {
    getAppsListInfo()
  }, [appId,appDomain,currentPage,pageSize]);

  useEffect(() => {
    if(!appDomain){
      setAppList([]);
      setAppId('')
      return;
    }
    setAppId('')
    domainList.forEach((item)=>{
      if(item.appDomain == appDomain){
        setAppList(item.apps)
      }
    })
  }, [appDomain]);
  
  useEffect(() => {
    getDomainsAppData();
    getDomainsLabelList();
  }, []);

  useEffect(() => {
    filterData(domainslabelList,domainList)
  }, [domainList,domainslabelList]);

 const filterData = (dataList,titleArray) => {
  if(dataList.length === 0 || titleArray.length === 0){
    return;
  }
  const newArray = [];
  titleArray.forEach((item)=>{
    if(item.appDomain === 'nodomain'){
      newArray.push({
        label:"暂无应用域",
        value:item.appDomain
      })
    }else{
      dataList.forEach((list)=>{
        if(list.value === item.appDomain){
          newArray.push({
            label:list.label,
            value:item.appDomain
          })
        }
      })
    }
  })
 setDomainDatatitleList(newArray)
 }
 useEffect(() => {
  resetData(domainslabelList,data)
}, [data,domainslabelList]);
 const resetData = (titleArray,dataList) => {
  console.log("aaaaa")
  console.log(dataList)
  if(dataList.length === 0 || titleArray.length === 0){
    return;
  }
  dataList.forEach((item)=>{
    if(item.appDomain === 'nodomain'){
      item.appDomain='暂无作用域'
    }else{
      titleArray.forEach((list)=>{
        if(list.value === item.appDomain){
          item.appDomain = list.label
        }
      })
    }
  })
  setNewData([...dataList])
  console.log(dataList)
  // setData([...dataList])
 
 }
     //获取应用域label
     const getDomainsLabelList = () => {
      getDomainslabel().then((res) => {
        if(res && res.records.length > 0){
          setDomainslabelList(res.records)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
  const getDomainsAppData = () => {
    getDomainsApp().then((res) => {
        if(res){
          setDomainList(res)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
  }
    const columns = [
        {
          title: '应用系统',
          dataIndex: 'appName',
          key: 'appName'
        },
        {
          title: '应用域',
          dataIndex: 'appDomain',
          render: (_, { appDomain }) => (
            <>
            {
              appDomain?
              <Tag color={'blue'} key={appDomain}>
              {appDomain}
            </Tag>:''
            }
          
            </>
          ),
        },
        {
          title: '中间件服务名称',
          dataIndex: 'middlewareName',
          key: 'middlewareName',
        },
        {
          title: '中间件类型',
          dataIndex: 'middlewareType',
          key: 'middlewareType',
        },
        {
          title: 'CPU(Core)',
          dataIndex: 'cpu',
          key: 'cpu',
        },
        {
          title: '内存(GB)',
          dataIndex: 'memory',
          key: 'memory',
        },
        {
          title: '存储(GB)',
          dataIndex: 'storage',
          key: 'storage',
        },
        {
          title: '状态',
          key: 'statusText',
          dataIndex: 'statusText',
          render: (_, { statusText }) => (
            <>
            {
              statusText === '运行正常'?
              <Tag color="success">{statusText}</Tag>:(
                statusText === '数据删除中'? <Tag color="warning">{statusText}</Tag>:(
                  statusText === '更新中'? <Tag color="processing">{statusText}</Tag>:(
                    statusText === '创建失败'? <Tag color="error">{statusText}</Tag>:(
                      statusText === '驳回'? <Tag color="orange">{statusText}</Tag>:(
                        statusText === '审批中'? <Tag color="geekblue">{statusText}</Tag>:(
                          statusText === '重启中'? <Tag color="magenta">{statusText}</Tag>:
                          (
                            statusText === '运行异常'? <Tag color="red">{statusText}</Tag>:<Tag color="default">{statusText}</Tag>
                           )
                        )
                      )
                    )
                  )
                )
              )
            }
    
            </>
          ),
        },
   
      ];

  
      const generateUniqueIds = (dataArray) => {
        return dataArray.map(item => ({
          ...item,
          id: crypto.randomUUID() // 生成标准的UUID
        }));
      };
      const getAppsListInfo = () => {
        const param = {
          appDomain:appDomain,
          appId:appId,
          pageNo:currentPage,
          pageSize:pageSize
        }
        setLoading(true);
          getAppsList(param).then((res) => {
            if(res){
              const dataArray = res.records;
              const newArray = generateUniqueIds(dataArray);
              setData(newArray)
              setTotal(res.total)
              setLoading(false);
            }
          }).catch((data) => {
            message.error(data?.msg)
          })
      }
      // useEffect(() => {
      //   getDomainsLabelList(domaintitleList)
      // }, [data]);

      const getSummaryData = (appDomain,appId) => {
        getSummary({
          appDomain:appDomain,
          appId:appId
        }).then((res) => {
          if(res){
           setSummary(res)
           setSummaryOrigin(res)
          }
        }).catch((data) => {
          message.error(data?.msg)
        })
    }
    useEffect(() => {
      getSummaryData(appDomain,appId)
    }, [appDomain,appId]);
  
      const onPageChange = (page,pageSize) => {
        setCurrentPage(page);
        setPageSize(pageSize);
      };
      const onSearch = (value) => {
        console.log('search:', value);
      };
      const filterOption = (input, option) => {
        return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
      };
      const rowSelection = {
        selectedRowKeys,
        onChange: (selectedKeys, selectedRows) => {
          console.log(selectedRows)
          setSelectedRowKeys(selectedKeys);
          setSelectedRows(selectedRows);
          if(selectedRows.length === 0){
            setSummary({...summaryOrigin})
          }else{
            const total = calculateStats(selectedRows)
            setSummary({...total})
          }
        },
      };
      const calculateStats = (dataArray) => {
        // 1. 计算去重的appName数量
        const uniqueAppNames = [...new Set(dataArray.map(item => item.appName))];
        const appNum = uniqueAppNames.length;
        
        // 2. 计算去重的middlewareName数量
        const uniqueMiddlewareNames = [...new Set(dataArray.map(item => item.middlewareName))];
        const middlewareNum = uniqueMiddlewareNames.length;
        
        // 3. 累加所有cpu值
        const cpu = dataArray.reduce((sum, item) => sum + (item.cpu || 0), 0);
        
        // 4. 累加所有memory值
        const memory = dataArray.reduce((sum, item) => sum + (item.memory || 0), 0);
        // 4. 累加所有storage值
        const storage = dataArray.reduce((sum, item) => sum + (item.storage || 0), 0);
        return {
          appNum,
          middlewareNum,
          cpu,
          memory,
          storage
        };
      };
  return (
    <div className="platformBoard overview middleWarePlatformBoard">
      <div className="platformBoard-content ">
         <div style={{display:'flex',marginBottom:'10px'}}>
            <Card className="platformBoard-content-box" style={{width:'100%'}}>
              <div>
               <div  >
                    <h2 style={{fontSize:'18px',marginBottom:'5px'}}>
                      <Space>
                          <ProfileOutlined />应用分析
                      </Space>
                    </h2>
                    <p style={{marginBottom:'20px',fontSize:'14px'}}>各应用系统使用的中间件详情</p>
                </div>
                <div  className="commen-flex">
                  <Space>
                    <div>
                      <span>应用域：</span>
                      <Select value={appDomain} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setAppDomain(value)}} allowClear>
                        {domainDatatitleList.map(item => (
                          <Option key={`${item.value}`} value={item.value}>{`${item.label}`}</Option>
                        ))}
                      </Select>
                    </div>
                    <div>
                        <span>应用系统：</span>
                        <Select value={appId} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setAppId(value)}} allowClear
                        showSearch  onSearch={onSearch} filterOption={filterOption}
                        >
                        {appList.map(method => (
                          <Option key={`${method.appId}`} value={method.appId}>{`${method.appName}`}</Option>
                        ))}
                      </Select>
                    </div>
                  </Space>
                </div>
                <div className="summary-total">
                  <div className="summary">
                    <h3>{summary.appNum}</h3>
                    <span>应用系统</span>
                  </div>
                  <div className="summary">
                    <h3>{summary.middlewareNum}</h3>
                    <span>中间件实例</span>
                  </div>
                  <div className="summary">
                    <h3>{summary.cpu}Core</h3>
                    <span>总CPU占用</span>
                  </div>
                  <div className="summary">
                    <h3>{summary.memory}GB</h3>
                    <span>总内存占用</span>
                  </div>
                  <div className="summary">
                  <h3>{summary.storage}GB</h3>
                  <span>总存储占用</span>
                </div>
              </div>
               </div>
                <div>
                    <div style={{width:'100%'}}>
                        <Table columns={columns} dataSource={newData}
                        pagination={{
                          current:currentPage,
                          showTotal:()=>{return `共 ${total} 项数据`},
                          showSizeChanger:true,
                          onChange:(page,pageSize)=>{onPageChange(page,pageSize)},
                          total:total,
                          pageSize:pageSize
                        }}
                        loading={loading}
                        rowSelection={rowSelection}
                        rowKey="id"
                        />
                        
                    </div>             
                </div>
            </Card>
      </div>
      </div>
    </div>
  );
};


export default ApplicationBoard;




