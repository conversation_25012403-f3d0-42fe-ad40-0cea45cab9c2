import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-liquidfill'

const OpenSourceUsageGauge = ({data, dataPoor,color,title,type,barWidth='40px'}) => {
  const barchartRef = useRef(null);

  useEffect(() => {
    if (!barchartRef.current) return;
    const chart = echarts.init(barchartRef.current);
    const option = {
      tooltip: {  
        trigger: 'axis',
        // formatter: function(params) {
        //   // params是一个数组，包含当前轴上的所有系列数据
        //   let result = params[0].name + '<br/>';
        //   let showNum = 0;
        //   params.forEach(function(item) {
        //     showNum += parseInt(item.value); // 添加单位
        //   });
        //   result += showNum
        //   return result;
        // }
      },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
          type: 'category',
          data:title,
          axisLabel:{
            interval: 0,
            rotate: 45,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: data,
            type: 'bar',
            stack: 'total',
            name: '符合基线数',
            barWidth: barWidth,
            itemStyle: {
              color: Array.isArray(color)?
              function(params) {
                return color[params.dataIndex];
              }:color
                },
           
          },
          {
            data: dataPoor,
            type: 'bar',
            barWidth: barWidth,
            stack: 'total',
            name: '不符合基线数',
            itemStyle: {
              color: Array.isArray(color)?
              function(params) {
                return color[params.dataIndex];
              }:color
                },
           
          },
        ]
      };

    chart.setOption(option);

    // 响应式处理
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, [data]);



  return (
    <div style={{height:'100%',width:'100%'}}>
      <div ref={barchartRef} style={{ width: '100%', height: '100%' }}></div>
    </div>
  );
};

export default OpenSourceUsageGauge;