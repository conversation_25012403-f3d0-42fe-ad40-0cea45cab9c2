import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';

const QualityMetricsDashboard = () => {
  const chartRef = useRef(null);

  useEffect(() => {
    // Initialize ECharts instance
    const chart = echarts.init(chartRef.current);

    // Bar chart configuration
    const option = {
        legend: {
            data: [
                {name: '严重', icon: 'circle'},
                {name: '一般', icon: 'circle'},
                {name: '提示', icon: 'circle'},
                // ...
            ],
            show: true
        },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['[TEST]app-system-ui', '[DEV]kepler-auth', '[DEV]kepler-gateway'],
        axisLabel: {
          interval: 0,
          rotate: 0,
          fontSize: 12
        },
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 30,
        interval: 5,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [{
        data: [25, 15, 8],
        type: 'bar',
        barWidth: '20px',
        itemStyle: {
            color: '#F47170' // 所有柱子使用相同颜色
        },
   
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      }]
    };

    chart.setOption(option);

    // Handle window resize
    const handleResize = () => {
      chart.resize();
    };
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, []);

  return (
    <div style={{ width: '100%', maxWidth: '800px', margin: '0 auto', fontFamily: 'Arial, sans-serif' }}>
     
      {/* Chart Container */}
      <div 
        ref={chartRef} 
        style={{ 
          width: '100%', 
          height: '300px',
          backgroundColor: '#fff',
          borderRadius: '4px',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
        }}
      />
    </div>
  );
};

export default QualityMetricsDashboard;