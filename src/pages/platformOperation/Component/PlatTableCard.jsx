// export default platTableCard;
import React, { useEffect, useRef, useState } from "react";
import {Badge ,Input,Button,message,Col, Row,Select,Table,Card  } from 'antd';
import {
    DisconnectOutlined
  } from '@ant-design/icons';
  import  platTitle from '@/assets/images/platTitle.png';
  import SelectSearch from './SelectSearch';
  import { useModel } from 'umi';
import { size } from "lodash";
import Demo from './Demo';
import debounce from 'lodash/debounce';
import { BaseUrlEnum } from '@/enums/httpEnum';
import axios from 'axios';
import {getBussinessDomainslabel,getUserSysCommitInfo} from '@/services';
const platTableCard = ({title}) => {
   
      const [searchText, setSearchText] = useState('');
      // const [filteredUsers, setFilteredUsers] = useState([]);
      const { userInfo } = useModel('user');
      const [userName, setUserName] = useState(userInfo.id);
      const [data, setData] = useState([]);
      const [domain, setDomain] = useState('');
      const columns = [
        {
          title: '应用系统名称',
          dataIndex: 'projectName',
          key: 'projectName',
          render: text => <span>{text}</span>,
        },
        {
          title: '提交总次数',
          dataIndex: 'commitCount',
          key: 'commitCount',
          render: text => <span>{text}</span>,
        },
        {
          title: '首次提交时间',
          dataIndex: 'firstCommitTime',
          key: 'firstCommitTime',
          render: text => <span>{text}</span>,
        },
        {
          title: '最近提交时间',
          dataIndex: 'recentCommitTime',
          key: 'recentCommitTime',
          render: text => <span>{text}</span>,
        }
      ]
     
      ////开始
  const [options, setOptions] = useState([]);
  const fetchOptions = () => {
    getBussinessDomainslabel().then((response) => {
      if(response && response.records.length > 0){
        setOptions(response.records)
        setDomain(response.records[0].value)
        getCodeCommit(response.records[0].value)
      
      }
    }).catch((data) => {
      return [];
    })
  }
  useEffect(() => {
    fetchOptions()
  }, []);

  const filterOption = (input, option) => {
    return (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
  };
  const onSearch = (value) => {
    setDomain(value);
   
  };
  // useEffect(() => {
  //   if(!domain){
  //     return;
  //   }
  //   getCodeCommit()
  // }, [domain]);

 //获取代码提交
const getCodeCommit = (domain) => {
  const dataParam = {
    domain:domain
  }
  getUserSysCommitInfo(dataParam).then((res) => {
    if(res){
      setData(res)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
  return (
    <div className="platformBoard-content-box" style={{width:'65%',marginRight:'10px',height:'100%'}}>
    <Card style={{ width: '100%' ,marginRight:'10px',height:'100%'}}>
        <div className="commen-flex">
            <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>{title}</span></p>
            <Select
              showSearch
              value={domain}
              placeholder={"输入名称搜索"}
              filterOption={filterOption}
              onSearch={onSearch}
              onChange={(value) => {
                setDomain(value);
                getCodeCommit(value)
            }}
              style={{ width: '200px' }}
            >
              {options.map((item) => (
                <Option key={item.value} value={item.value}>
                  {item.value}
                </Option>
              ))}
            </Select>
        </div>
      
        <div>
           
            <div className="content-text">
              <Table columns={columns} dataSource={data} 
              pagination={{
                pageSize: 5,
              }} 
              size="small"
              />
            </div>             
        </div>
    </Card>
    </div>
  );
};

export default platTableCard;
