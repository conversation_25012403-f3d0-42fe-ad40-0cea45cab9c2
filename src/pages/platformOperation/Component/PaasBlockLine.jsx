// import React, { useRef, useEffect } from 'react';
// import * as echarts from 'echarts';

// const PaasBlockLine = ({title,dataList}) => {
//   const chartRef = useRef(null);
//   let chartInstance = null;

//   useEffect(() => {
//     // 初始化图表
//     if (chartRef.current) {
//       chartInstance = echarts.init(chartRef.current);
      
//       // 图表配置项
//       const option = {
//         title: {
//           text: '当月访问趋势',
//           left: 'center',
//           textStyle: {
//             color: '#464646',   // 改为白色，在深色背景上更清晰
//             fontSize: 12
//           },
//           top: 0,              // 标题贴近顶部
//           bottom: 10           // 标题与图表间距
//         },
//         tooltip: {
//           trigger: 'axis'
//         },
//         legend: {
//           data: title,
//           right: 0,           // 距离右侧距离
//           bottom: 10,          // 距离底部距离
//           orient: 'vertical',  // 垂直排列（可选）
//           textStyle: {
//             fontSize: 8       // 调整文字大小以适应空间
//           },
//             // 添加 icon 配置
//             icon: 'circle', // 设置为圆形
//             itemWidth: 6,   // 图标宽度
//             itemHeight: 6,  // 图标高度
//             itemGap: 8      // 图标与文字的间距
//         },
//         grid: {
//             left: '3%',
//             right: '18%',
//             bottom: '15%',       // 为底部 legend 留出空间
//             top: '15%',          // 减小顶部间距，让标题和图表更近
//             containLabel: true
//         },
//         xAxis: {
//           type: 'category',
//           boundaryGap: false,
//           data: ['01', '03', '05', '07', '09', '11', '13', '15', '17', '19', '21', '23', '25', '27', '29', '31']
//         },
//         yAxis: {
//           type: 'value',
//           min: 0,
//           max: 40
//         },
//         series: [
//           {
//             name: '节假日服务',
//             type: 'line',
//             data: [25, 22, 28, 35, 20, 38, 20, 5, 30, 7, 35, 8, 22, 20, 25],
//             smooth: true,
//             lineStyle: { color: '#FFC107' },
//             itemStyle: { color: '#FFC107' }
//           },
//           {
//             name: '邮件服务',
//             type: 'line',
//             data: [35, 40, 15, 5, 25, 30, 7, 35, 10, 35, 12, 25, 38, 15, 20],
//             smooth: true,
//             lineStyle: { color: '#4CAF50' },
//             itemStyle: { color: '#4CAF50' }
//           },
//           {
//             name: '国际化服务',
//             type: 'line',
//             data: [10, 22, 35, 10, 30, 15, 5, 8, 5, 3, 35, 3, 35, 30],
//             smooth: true,
//             lineStyle: { color: '#1E88E5' },
//             itemStyle: { color: '#1E88E5' }
//           }
//         ]
//       };

//       chartInstance.setOption(option);
//     }

//     // 组件卸载时销毁图表
//     return () => {
//       if (chartInstance) {
//         chartInstance.dispose();
//         chartInstance = null;
//       }
//     };
//   }, []);

//   return (
//     <div 
//       ref={chartRef} 
//       style={{ width: '100%', height: '100%' }} 
//     />
//   );
// };

// export default PaasBlockLine;
import React, { useRef, useEffect } from 'react';
import * as echarts from 'echarts';

const PaasBlockLine = ({title,dataList,color}) => {
  const chartRef = useRef(null);
  let chartInstance = null;

  useEffect(() => {
    // 初始化图表
    const transformData = (data) => {
      return Object.entries(data).map(([name, value]) => ({
        name,
        value
      }));
    };
    const xAxisData = [];
    if (chartRef.current) {
      chartInstance = echarts.init(chartRef.current);
        // 动态生成 series 数据
        const generateSeries = () => {
          if (!dataList) return {};
          // 使用
          const newArray = transformData(dataList);
          if (newArray[0].value?.length > 0) {
            newArray[0].value?.forEach((item, index) => {
              xAxisData.push(index+1)
            })
          }
          return newArray.map((data, index) => ({
            name: data.name,
            type: 'line',
            data: data.value,
            smooth: true,
            lineStyle: { 
              color:color[index] 
            },
            itemStyle: { 
              color:color[index] 
            }
          }));
        };
      // 图表配置项
      const option = {
        title: {
          text: '当月访问趋势',
          left: 'center',
          textStyle: {
            color: '#464646',   // 改为白色，在深色背景上更清晰
            fontSize: 12
          },
          top: 0,              // 标题贴近顶部
          bottom: 10           // 标题与图表间距
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: title,
          right: '0%',           // 调整右侧距离
          bottom: '0%',   
          orient: 'vertical',  // 垂直排列（可选）
          textStyle: {
            fontSize: 10      // 调整文字大小以适应空间
          },
            // 添加 icon 配置
            icon: 'circle', // 设置为圆形
            itemWidth: 6,   // 图标宽度
            itemHeight: 6,  // 图标高度
            itemGap: 8      // 图标与文字的间距
        },
        grid: {
            left: '0%',
            right: '25%',
            bottom: '5%',       // 为底部 legend 留出空间
            top: '15%',          // 减小顶部间距，让标题和图表更近
            containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: xAxisData,
          axisLabel:{
            rotate: 30,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          min: 0
        },
        series: generateSeries()
      };

      chartInstance.setOption(option);
    }

    // 组件卸载时销毁图表
    return () => {
      if (chartInstance) {
        chartInstance.dispose();
        chartInstance = null;
      }
    };
  }, []);

  return (
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: '100%' }} 
    />
  );
};

export default PaasBlockLine;
