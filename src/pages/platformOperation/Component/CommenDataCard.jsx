import React, { useEffect, useRef, useState } from "react";
import {Badge ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Flex, Progress   } from 'antd';
import {
    DisconnectOutlined
  } from '@ant-design/icons';
  import  platTitle from '@/assets/images/platTitle.png';
  import MiddleUsageGauge from '../bar';
  import PieCommon from './PieCommon';
  import CommenProgress from './CommenProgress';
  import CommenBlock from './CommenBlock';
import { info } from "autoprefixer";
import OpenSourceUsageGauge from "../barComponent";
const CommenDataCard = ({data,title,type}) => {
  const dataList=[{name:'文档总数',num:375},{name:'本周新增',num:23},{name:'文档浏览总数',num:2133},{name:'今日浏览',num:43},{name:'今日搜索',num:73}]
  return (
    <div style={{width:type === 'component'?'58%':'35%',marginRight:'10px'}} className="commenTextCard">
    <Card style={{ width: '100%' ,marginRight:'10px',height:'300px'}}>
      <div style={{width:'100%',height:'100%',display:'flex'}}>
        <div className="content-main-commencard" style={{width:'50%'}}>
            <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>{title}</span></p>
           <div>
            {
            type === 'doc'? <div>
            <CommenBlock data={{'name':'文档总数','num':data.totalDocCount}} />
            <CommenBlock data={{'name':'近30天新增','num':data.recent30DayNewDocCount}} />
            <CommenBlock data={{'name':'文档浏览总数','num':data.totalViewCount}} />
            <CommenBlock data={{'name':'近7天浏览','num':data.recent7DayViewCount}} />
            <CommenBlock data={{'name':'近7天搜索','num':data.recent7DaySearchCount}} />
            </div> :(
                type === 'api'? <div>
                <CommenBlock data={{'name':'API数量','num':data.info?.totalApiCount}} />
                <CommenBlock data={{'name':'近30天发布','num':data.info?.recent30DayPublishCount}} />
                <CommenBlock data={{'name':'近7天试用次数','num':data.info?.recent7DayTestCount}} />
                <CommenBlock data={{'name':'近7天订阅','num':data.info?.recent7DaySubscribeCount}} />
                <CommenBlock data={{'name':'近7天搜索','num':data.info?.recent7DaySearchCount}} />
                </div>: (
                  type === 'component' ? <div>
                      <CommenBlock customStyle={{width: 'calc(50% - 10px)'}} data={{'name':'开源组件使用数','num':data.info?.totalCount}} />
                      <CommenBlock customStyle={{width: 'calc(50% - 10px)'}} data={{'name':'符合基线数','num':data.info?.baselineCount}} />
                      <CommenBlock customStyle={{width: 'calc(50% - 10px)'}} data={{'name':'基线符合率','num':data.info?.baselineRate}} />
                    </div> : ''
                )
              )
            }
             
           </div>           
        </div>
        <div className="content-main-commencard-right" style={{width:'50%'}}>
              {
                type === 'api' && data.dataList?.length > 0 ? 
                <PieCommon data={data.dataList}
                color={ ['#D5F2EF', '#91CC75','#FAC858','#FAC858','#F2E8B7']}
                title="API统计"
                showLegend={true}
                showLabel={true}
                />
                :(
                  type === 'doc'?
                 <div>
                  <p className="platformBoard-content-title" style={{marginBottom:'20px'}}>文档浏览前10</p>
                  {
                    data.top10DocList?.length > 0 && data.top10DocList.map((item)=>(
                      <p className="content-text-contain" style={{color:'#3D3D3D'}}>
                      <span>{item.docName}</span>
                      <span>{item.viewCount}</span>
                      </p>
                      ))
                  }
                 </div>
                  : (
                    type === 'component' ?
                      <OpenSourceUsageGauge data={data.dataList} dataPoor={data.dataPoor}  title={data.dataTitle} type={''} barWidth="20px"/>
                      : ''
                  )
                )
              }
        </div>
      </div>
    </Card>
    </div>
  );
};

export default CommenDataCard;
