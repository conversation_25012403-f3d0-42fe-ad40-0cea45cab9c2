
import React, { useEffect, useRef, useState } from "react";
import {Badge ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Flex, Progress   } from 'antd';
  import  platTitle from '@/assets/images/platTitle.png';
  import MiddleUsageGauge from '../bar';
  import Pie<PERSON>ommon from './PieCommon';
  import CommenProgress from './CommenProgress'
const CommenTextCard = ({info,title,type, handleChange}) => {
  const handleChangeSelect = (val) => {
    handleChange(val)
  }
  const colorArr = ['#2FB7F5', '#1BBE6B', '#FF6067', '#FF9900']
  return (
    <div style={{width: type === 'scaffold'?'50%':(type === 'visit'? '30%':(type === 'application'? '60%':'40%')),marginRight:'10px'}} className="commenTextCard">
    <Card style={{ width: '100%' ,marginRight:'10px',height: type === 'visit'? '300px':'250px'}}>
      <div style={{width:'100%',height:'100%',display:'flex'}}>
        <div className="content-main-commencard" style={{width: type === 'application'? '40%' : ''}}>
            <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>{title}</span></p>
           <div style={{display:'flex', 'flex': 1, justifyContent: 'center', alignItems: 'center'}}>
               {
              type === 'application' || type === 'app'?  <div className="content-total">
                <p className="content-total-text">
                    {
                      info.total?info.total:0
                    }
                </p>
                <p style={{fontSize:'12px'}}>
                    <span>本月新增</span><p className="num-add">{info.increase}</p>
                </p>
              </div>:(
                type==='visit'?<div className="content-total">
                <div>
                  <span>访问量</span>
                  <span className="content-total-text">  {
                    info.total?info.total:0
                  }</span>
                </div>
          </div>:(
            type === 'scaffold'? <div className="PaasBlockCardTop" style={{width:'100%',textAlign:'center',color:'#323233'}}>
            <p style={{fontSize:'12px'}}>应用程序脚手架模板数</p>
            <h3 style={{fontSize:'30px'}}>{info.total?info.total:0}</h3>
          </div>:''
          )
              )
               }
             
              {
                type === 'app' && info.dataList?.length > 0 ?
               <div style={{width:'55%',display:'flex',margin:'0 15px'}}>
                  <div className="left">
                    {
                      info.dataList.map((item)=>(
                        <p className="content-text-contain">
                        <span>{item.name}</span>
                        <span style={{marginLeft: '40px'}}>{item.value}</span>
                        </p>
                        ))
                    }
                  </div>
               </div>:(
                    type === 'application'?(
                      <div style={{width:'55%',display:'flex',margin:'0 15px'}}>
                        <div className="left">
                          {
                            info.data?.map((item, index)=>(
                              <p className="content-text-contain">
                              
                                {
                                  // item.stageStatusName === '建设中'?<Space><Badge color="#2FB7F5"/> 建设中</Space>:
                                  // item.stageStatusName === '待上线'?<Space><Badge color="#1BBE6B"/> 待上线</Space>:
                                  // item.stageStatusName === '以上线'?<Space><Badge color="#FF6067"/>  以上线</Space>:
                                  // item.stageStatusName === '待变更'?<Space><Badge color="#FF9900"/>待变更</Space>:
                                  // item.stageStatusName === '已下线'?<Space><Badge color="#FF9900"/>已下线</Space>:
                                  // <Space><Badge status="default" />未知状态</Space>
                                  <Space><Badge color={colorArr[index%4]} />{item.stageStatusName}</Space>
                                  
                                }
                              <span style={{marginLeft: '40px'}}>{item.num}</span>
                              </p>
                              ))
                          }
                        </div>
                      </div>
                    ):''
                  )
              }
             
              
           
           </div>           
        </div>
        <div className="content-main-commencard-right" style={{width: type === 'application'? '60%' : ''}}>
              {
                (type === 'application') && info?.dataList?.length && (
                  <div style={{height: '32px'}}>
                    <Select
                      defaultValue={'1'}
                      onChange={(value) => handleChangeSelect(value)}
                      style={{ float: 'right', width: '180px' }}
                    >
                      <Select.Option value="1">应用域</Select.Option>
                      <Select.Option value="2">服务等级</Select.Option>
                      <Select.Option value="3">建设方式</Select.Option>
                    </Select>
                  </div>
                )
              }
              {
                (type === 'application' || type === 'scaffold') && info?.dataList?.length ? <MiddleUsageGauge data={info.dataList} color={['#FAC858','#67B8DE','#B4E0F2','#3399CD','#83CDA7']} title={info.dataTitle} type={''} barWidth="10px"/>:(
                  type === 'app'?
                  <PieCommon data={info.dataList}
                  color={ ['#D5F2EF', '#91CC75','#FAC858','#FAC858','#F2E8B7']}
                  title="应用程序统计"
                  />:<div>
                  {
                    info && info.dataList && <CommenProgress data={info.dataList}/>
                  }
                  
                  </div>
                )
              }
        </div>
      </div>
    </Card>
    </div>
  );
};

export default CommenTextCard;
