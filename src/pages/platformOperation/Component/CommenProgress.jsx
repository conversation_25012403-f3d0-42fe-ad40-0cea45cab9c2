import React, { useEffect, useRef } from 'react';
import {Badge ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Flex, Progress   } from 'antd';

const CommenProgress = ({data}) => {
  return (
    <div>
        {
            data.map((item)=>
            <div>
                <div style={{fontSize:'10px',color:'#646566'}}>{item.name}<span style={{float: 'right'}}>{item.value}</span></div>
                <Progress percent={item.percent} size="small"/>
            </div>
            )
        }
    </div>
  );
};

export default CommenProgress;