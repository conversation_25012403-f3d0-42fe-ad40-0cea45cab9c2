import React, { useState } from 'react';
import { Select } from 'antd';

const { Option } = Select;

const UserSearchSelect = ({ userData }) => {
  const [filteredUsers, setFilteredUsers] = useState(userData);

  // 搜索处理函数
  const handleSearch = (value) => {
    if (!value) {
      setFilteredUsers(userData);
      return;
    }
    
    const filtered = userData.filter(user => 
      user.userRealname && user.userRealname.toLowerCase().includes(value.toLowerCase())
    );
    setFilteredUsers(filtered);
  };

  return (
    <Select
      showSearch
      style={{ width: 200 }}
      placeholder="搜索用户姓名"
      optionFilterProp="children"
      onSearch={handleSearch}
      filterOption={false} // 禁用默认的过滤，使用自定义的搜索
    >
      {filteredUsers.map(user => (
        <Option key={user.id} value={user.id}>
          {user.userRealname}
        </Option>
      ))}
    </Select>
  );
};
export default UserSearchSelect;
