import React, { useState, useRef, useEffect } from 'react';
import { Select, Spin, message } from 'antd';
import debounce from 'lodash/debounce';
import axios from 'axios';
import { BaseUrlEnum } from '@/enums/httpEnum'
const { Option } = Select;
import { useModel } from 'umi';
const RemoteSearchSelect = ({ 
  fetchOptions, 
  debounceTimeout = 800, 
  initialValue,
  ...props 
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);

  const fetchRef = useRef(0);
  const { userInfo } = useModel('user');
  const [value, setValue] = useState(initialValue);

//   // 加载初始值对应的label
  useEffect(() => {
    if (initialValue && !options.find(o => o.value === initialValue)) {
      fetchOptions(userInfo.userRealname).then((newOptions) => {
        setOptions(prev => [...prev, ...newOptions]);
      }).catch(() => {
        message.error('加载初始数据失败');
      });
    }
  }, [initialValue]);

  const debounceFetcher = React.useMemo(() => {
    const loadOptions = (value) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);
      
      fetchOptions(value)
        .then((newOptions) => {
          if (fetchId !== fetchRef.current) {
            return;
          }
          setOptions(newOptions);
          setFetching(false);
        })
        .catch(() => {
          message.error('加载选项失败');
          setFetching(false);
        });
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout]);

  const handleChange = (newValue) => {
    setValue(newValue);
    props.onChange && props.onChange(newValue);
  };

  return (
    <Select
      showSearch
      value={value}
      placeholder={props.placeholder || "请搜索"}
      filterOption={false}
      onSearch={debounceFetcher}
      onChange={handleChange}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      style={props.style || { width: '200px' }}
      allowClear
      {...props}
      
    >
      {options.map((item) => (
        <Option key={item.value} value={item.value}>
          {item.label}
        </Option>
      ))}
    </Select>
  );
};

// 使用示例
const Demo = ({initialValue}) => {
  const fetchUserList = async (keyword) => {
    try {
     // 搜索多个结果
     if (initialValue) {
        // 加载单个初始值
        const response = await axios.get(`${BaseUrlEnum.KEPLER}/upms/u/users`,{
            params: { name: keyword,current:1,size:10,isQueryLocked:1 }
          }
        );
        console.log("aa")
        console.log(response)
        return [{ value: response.data.data.records[0].id, label: response.data.data.records[0].userRealname }];
      }else{
        const response = await axios.get(`${BaseUrlEnum.KEPLER}/upms/u/users`, {
            params: { name: keyword,current:1,size:10,isQueryLocked:1 }
          });
          console.log(response)
          return response.data.data.records.map(user => ({
            value: user.id,
            label: user.userRealname
          }));
        } 
      }catch (error) {
      console.error('Fetch users error:', error);
      return [];
    }
  };

  return (
    <div>
      <h3>用户搜索选择器</h3>
      <RemoteSearchSelect
        fetchOptions={fetchUserList}
        placeholder="搜索用户"
        initialValue={initialValue}
        onChange={(value) => console.log('selected:', value)}
      />
    </div>
  );
};

export default Demo;