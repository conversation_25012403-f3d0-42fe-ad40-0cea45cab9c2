import React, { useRef, useEffect } from 'react';
import * as echarts from 'echarts';  // 引入原生ECharts

const ApplicationChart = ({data=[],title=[]}) => {
  // 创建ref关联图表容器DOM
  const chartRef = useRef(null);
  // 存储图表实例，用于卸载时销毁
  let chartInstance = null;

  useEffect(() => {
    // 初始化图表（确保DOM已挂载）
    if (chartRef.current) {
      // 创建图表实例
      chartInstance = echarts.init(chartRef.current);
      // 图表配置项（与之前逻辑一致，横向柱状图）
      const option = {
        title: { text: '申请数据统计', left: 'center' },
        tooltip: { trigger: 'axis', axisPointer: { type: 'shadow' } },
        grid: { left: '3%', right: '4%', bottom: '3%', containLabel: true },
        xAxis: {
          axisLabel: { show: false },  // 隐藏数值标签
          axisLine: { show: false },   // 可选：隐藏轴线
          axisTick: { show: false }    // 可选：隐藏刻度线
        },
        yAxis: { 
          type: 'category', 
          data: title// 类别轴（左侧技术栈名称）
        },
        series: [{
          name: '申请数量',
          type: 'bar',  // 柱状图类型
          data: data,  // 对应数值
          itemStyle: { color: '#67B8DE' },  // 蓝色柱状图样式
          barWidth: '10px',
          label: {
            show: true,           // 开启标签
            position: 'insideLeft',  // 在柱子内部右侧显示
            formatter: '{c}',
            textStyle: {
              color: '#fff',   // 改为白色，在深色背景上更清晰
              fontSize: 10
            }
          
          }
        }]
      };

      // 设置配置项并渲染图表
      chartInstance.setOption(option);
    }

    // 组件卸载时销毁图表实例（避免内存泄漏）
    return () => {
      if (chartInstance) {
        chartInstance.dispose();  // 销毁实例
        chartInstance = null;
      }
    };
  }, []);  // 空依赖数组：仅在组件挂载/卸载时执行

  return (
    // 图表容器：需指定宽高，ref关联DOM
    <div 
      ref={chartRef} 
      style={{ width: '100%', height: '100%' }}  // 必须设置宽高
    />
  );
};

export default ApplicationChart;
