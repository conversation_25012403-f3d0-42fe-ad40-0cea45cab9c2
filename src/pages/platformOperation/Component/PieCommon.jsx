import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-liquidfill'

const PieCommon = ({data,color,title="",showLegend=false,showLabel=false}) => {
  const barchartRef = useRef(null);
  useEffect(() => {
    if (!barchartRef.current) return;
    const chart = echarts.init(barchartRef.current);
    const option = {
      color:color,
      tooltip: {
        trigger: 'item',
        show:true
      },
      legend: {
       
        orient: 'vertical',  
        right: '3px',  // 距离右侧3px
        bottom: '5%',  // 距离底部5%
        show:false
      },
      series: [
          {
            name: title,
            type: 'pie',
            label: {
              show: showLabel, // 隐藏标签文字
            },
            labelLine: {
              show: true, // 隐藏标签引导线
            },
            radius: '60%',
            data:data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
    };
      chart.setOption(option);
      return () => {
        chart.dispose();
      };
  }, [data]);



  return (
    <div ref={barchartRef} style={{ width: '100%', height: '100%' }}></div>
  );
};

export default PieCommon;