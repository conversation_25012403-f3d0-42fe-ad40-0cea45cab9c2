import React, { useEffect, useRef, useState } from "react";
import {Badge ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Flex, Progress   } from 'antd';
import {
    DisconnectOutlined
  } from '@ant-design/icons';
  import  platTitle from '@/assets/images/platTitle.png';
  import MiddleUsageGauge from '../bar';
  import PieCommon from './PieCommon';
  import CommenProgress from './CommenProgress';
  import PaasBlockResource from './paasBlockResource';
  import ApplicationChart from './ApplicationChart';
  import PaasBlockLine from './PaasBlockLine'
  
const PaasBlockCard = ({data,title,type,widthValue}) => {
  const dataList=[{name:'文档总数',num:375},{name:'本周新增',num:23},{name:'文档浏览总数',num:2133},{name:'今日浏览',num:43},{name:'今日搜索',num:73}]
  return (
    <div style={{width:type === 'component'?'42%':'50%',hegith:'100%',marginRight:'10px'}} className="PaasBlockCard">
    <Card style={{ width: '100%' ,marginRight:'10px',height:type === 'component'?'300px':'250px'}}>
      <div style={{width:'100%',height:'100%',display:'flex'}}>
        <div className="content-main-commencard" style={{width:`${widthValue}`, display: 'flex', flexDirection: 'column'}}>
            <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>{title}</span></p>
           <div style={{display:'flex', 'flex': 1, justifyContent: 'center', alignItems: 'center'}}>
              {
                type === 'paas'?<div>
                  <div className="PaasBlockCardTop">
                    <p className="PaasBlockCardTopTitle" style={{height: '32px', lineHeight: '48px'}}>Paas服务实例数</p>
                    <h3 style={{height: '76px', lineHeight: '76px'}}>{data?.data?.instanceCount}</h3>
                  </div>
                  <div className="PaasBlockCardBottom">
                    <PaasBlockResource num={data?.data?.usedCpu} total={data?.data?.totalCpu} type={'CPU'} />
                    <PaasBlockResource num={data?.data?.usedMemory} total={data?.data?.totalMemory}  type={'内存'} />
                    <PaasBlockResource num={data?.data?.usedStorage} total={data?.data?.totalStorage}  type={'存储'}  />
                  </div>
                </div>:(
                  type === 'component'?
                  <div>
                  <div className="PaasBlockCardTop">
                    <p className="PaasBlockCardTopTitle">共享组件上架数</p>
                    <h3>{data.count}</h3>
                  </div>
                </div>:''
                )
              }
             
           </div>           
        </div>
     
              {
                type === 'paas' ? 
                data.appData?.length > 0 &&  <div className="content-main-commencard-right" style={{width:'52%',hegith:'100%'}}> <ApplicationChart data={data.appData} title={data.appTitle}/> </div>
                :(
                  type === 'component' && data.title?.length>0 ?<div className="content-main-commencard-right" style={{width:'75%',hegith:'100%'}}> 
                      <PaasBlockLine title={data.title} dataList={data.dataList} color={['#FFC107','#4CAF50','#1E88E5','#D5F2EF','#F2E8B7','#1BBE6B']} />
                  </div>
                  :''
                )
              }
       
      </div>
    </Card>
    </div>
  );
};

export default PaasBlockCard;
