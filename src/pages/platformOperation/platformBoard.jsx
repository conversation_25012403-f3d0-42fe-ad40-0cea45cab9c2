import React, { useEffect, useRef, useState } from "react";
import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Dropdown, Menu  } from 'antd';
const { RangePicker } = DatePicker;
import { scaffoldTemplate,techStackRank,docCount,apiCount ,statisticApp,statisticMiddle,getUserList,getUserSysCommit} from '@/services';
import * as echarts from 'echarts';
import './platformBoard.less';
import CommenTextCard from './Component/CommenTextCard';
import PlatTableCard from './Component/PlatTableCard'
import  platTitle from '@/assets/images/platTitle.png';
import Code from './Component/LineCard';
import  codeImg from '@/assets/images/code.png';
const dateFormat = 'YYYY-MM-DD';
import { useModel } from 'umi';

const PlatformBoard = () => {
  const [total, setTotal] = useState();
  const [data, setData] = useState([]);
  const [techData, setTechData] = useState({});
  const [tech, setTech] = useState("前后端");
  const [createTime, setCreateTime] = useState([]);
  const [date, setDate] = useState('全部');
  const [startTime, setStartTime] = useState('');
  const [endTime, setEndTime] = useState('');
  const [info, setInfo] = useState({});
  const [docinfo, setDocInfo] = useState({});
  const [apiInfo, setApiInfo] = useState({});
  const [appInfo, setAppInfo] = useState({});
  const [appPercent, setAppPercent] = useState([
    {"businessDomain":"Finance","num":8},
    {"businessDomain":"HR","num":5}
  ]);
  const [isPass, setIspass] = useState('1');
  const [userList, setUserList] = useState([]);
  const [selUser, setSelUser] = useState([]);
  const [commitInfo, setCommitInfo] = useState([]);
  const { userInfo } = useModel('user');

  const chartRef = useRef(null)
  const middleRef = useRef(null)
  const lineRef = useRef(null)
  

  const getBaselineListData = () => {
    const dataParam = {
      startTime:endTime?`${endTime} 00:00:00`:'',
      endTime:startTime?`${startTime} 23:59:59`:''
    }
    scaffoldTemplate(dataParam).then((res) => {
      if(res){
        // setTotal(res.count)
        // setData(res.apps)
        setInfo({
          total:res.count,
          data:res.apps
        })
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  const getDocCount = () => {
    const dataParam = {
      startTime:endTime?`${endTime} 00:00:00`:'',
      endTime:startTime?`${startTime} 23:59:59`:''
    }
    docCount(dataParam).then((res) => {
      if(res){
        // setTotal(res.count)
        // setData(res.apps)
        setDocInfo({
          total:res,
          data:[]
        })
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
const getApiCount = () => {
  const dataParam = {
    startTime:endTime?`${endTime} 00:00:00`:'',
    endTime:startTime?`${startTime} 23:59:59`:''
  }
  apiCount(dataParam).then((res) => {
    if(res){
      // setTotal(res.count)
      // setData(res.apps)
      setApiInfo({
        total:res,
        data:[]
      })
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
  

  //技术栈使用情况
  const getTechStackRankData = () => {
    const dataParam = {
      startTime:endTime?`${endTime} 00:00:00`:'',
      endTime:startTime?`${startTime} 23:59:59`:''
    }
    techStackRank(dataParam).then((res) => {
      if(res){
        const list = res;
       setTechData(list)
       const array = {
        applicationTotalCount:list.applicationTotalCount,
        data:[
          {
          value: list.frontendApplicationCount, name: '前端'
          },
          {
            value: list.backendApplicationCount, name: '后端'
          }]
       }
       initTechCanvas(array)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
//应用系统统计
  const getStatisticApp = () => {
    const dataParam = {
      startTime:endTime?`${endTime} 00:00:00`:'',
      endTime:startTime?`${startTime} 23:59:59`:''
    }
    statisticApp(dataParam).then((res) => {
      if(res){
        if(res.apps?.length){
          res.apps.forEach((item)=>{
            if(item.stageStatus === 'Constructing'){
              item.stageStatusName = '建设中'
            }else if(item.stageStatus === 'Planning'){
              item.stageStatusName = '规划中'
            }else if(item.stageStatus === 'OnlineApplying'){
              item.stageStatusName = '发布审核中'
            }else if(item.stageStatus === 'OfflineApplying'){
              item.stageStatusName = '下线审核中'
            }else{
              item.stageStatusName = '未知状态'
            }
          })
        }
        setAppInfo({
          total:res.count,
          data:res.apps
        })
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  const handleChange = (value) => {
    setTech(value)
    if(value == '技术栈'){
      const newArray = techData?.techStackUsageList?.map(item => ({
        name: item.technicalStackTags,
        value: item.count
      }));
      const array = {
        applicationTotalCount:techData.applicationTotalCount,
        data:[...newArray]
       }
       console.log(array)
       initTechCanvas(array)
    }else{
      const array = {
        applicationTotalCount:techData.applicationTotalCount,
        data:[
          {
          value: techData.frontendApplicationCount, name: '前端'
          },
          {
            value: techData.backendApplicationCount, name: '后端'
          }
        ]
       }
       initTechCanvas(array)
    }
  };
  //改变日期
  function formatDate(date) {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
}

  const handleDateChange = (value) => {
    setDate(value);
    if(value === '全部'){
      setStartTime('')
      setEndTime('')
      return;
    }
    // 获取当前日期
    const startDate = new Date();
    let endDate;
    if(value == 0.5){
      // 创建结束日期（复制当前日期）
      endDate = new Date(startDate);
      // 往前推6个月
      endDate.setMonth(endDate.getMonth() - 6);
    }else if(value == 1){
      endDate = new Date();
      endDate.setFullYear(startDate.getFullYear() - 1);

    }else if(value == 2){
      endDate = new Date();
      endDate.setFullYear(startDate.getFullYear() - 2);
    }
    setStartTime(formatDate(startDate))
    setEndTime(formatDate(endDate))
  };


  //中间件系统占比
  const getStatisticMiddle = (pass) => {
  
  const dataParam = {
    startTime:endTime?`${endTime} 00:00:00`:'',
    endTime:startTime?`${startTime} 23:59:59`:'',
    paas:pass === '2'?true:false
  }

  statisticMiddle(dataParam).then((res) => {
    if(res){
     let data = res || [];
     if(pass == 2){
    //   data=  {
    //     "智能制造": [
    //         {
    //             "businessDomain": "ZHZZ",
    //             "name": "智能制造",
    //             "type": "redis",
    //             "num": 12
    //         },
    //         {
    //             "businessDomain": "ZHZZ",
    //             "name": "智能制造",
    //             "type": "mysql",
    //             "num": 2
    //         },
    //         {
    //             "businessDomain": "ZHZZ",
    //             "name": "智能制造",
    //             "type": "elasticsearch",
    //             "num": 1
    //         }
    //     ],
    //     "数字底座": [
    //         {
    //             "businessDomain": "SZDZ",
    //             "name": "数字底座",
    //             "type": "elasticsearch",
    //             "num": 1
    //         },
    //         {
    //             "businessDomain": "SZDZ",
    //             "name": "数字底座",
    //             "type": "redis",
    //             "num": 1
    //         }
    //     ],
    //     "基础建设": [
    //         {
    //             "businessDomain": "JCJS",
    //             "name": "基础建设",
    //             "type": "mysql",
    //             "num": 3
    //         },
    //         {
    //             "businessDomain": "JCJS",
    //             "name": "基础建设",
    //             "type": "redis",
    //             "num": 4
    //         }
    //     ],
    //     "供应链集成": [
    //         {
    //             "businessDomain": "GYLJC",
    //             "name": "供应链集成",
    //             "type": "redis",
    //             "num": 4
    //         },
    //         {
    //             "businessDomain": "GYLJC",
    //             "name": "供应链集成",
    //             "type": "elasticsearch",
    //             "num": 2
    //         }
    //     ]
    // }
          //组装柱状图所需的数据格式
          const result = Object.entries(data).map(([key, items]) => {
            const newItem = { name: key };
            items.forEach(item => {
              newItem[item.type] = item.num;
            });
            return newItem;
          });
          //组装柱状图横坐标
          const uniqueTypes = [...new Set(Object.values(data).flat().map(item => item.type))];
          uniqueTypes.unshift('name')
          const series = [
            { type: 'bar',barWidth: 20 }, { type: 'bar',barWidth: 20 }, { type: 'bar',barWidth: 20 }
          ]
          initMilddleCanvas(uniqueTypes,result,series)
     }else{
      // const data = [{
      //           "businessDomain": "ZHZZ2",
      //           "name": "智能制造2",
      //           "type": "redis",
      //           "num": 12
      //       },
      //       {
      //           "businessDomain": "ZHZZ",
      //           "name": "智能制造3",
      //           "type": "mysql",
      //           "num": 2
      //       },
      //       {
      //           "businessDomain": "ZHZZ3",
      //           "name": "智能制造",
      //           "type": "elasticsearch",
      //           "num": 1
      //       }
      //   ]
       //组装柱状图横坐标
        const uniqueNames = [
          "name",
          "容器中间件"
        ];
        data.forEach(item => {
          item['容器中间件'] = item.num; // 添加新属性
        });
        const resource = data;
        const series = [
          { type: 'bar',barWidth: 20 }
        ]
        initMilddleCanvas(uniqueNames,resource,series)
     }
    
     }
  }).catch((data) => {
    message.error(data?.msg)
  })
  }
  const initMilddleCanvas = (x,data,series) => {
    
    const chartDom = middleRef.current;
    const myChart = echarts.init(chartDom);
    const option = {
      color: ['#2FB7F5', '#1BBE6B', '#A4A8FF'],
      legend: {
        top: 'top', // 或 top: 0
        left: 'right', // 水平居中
      },
      grid: {
        left: '0%',
        right: '0%',
        top: '50px',
        bottom: '0%'
    },
      tooltip: {},
      dataset: {
        dimensions: x,
        source:data
      },
      xAxis: { type: 'category',
   
     },
      yAxis: {
        type: 'value',
        min: 0,
        max: 30 // 根据你的数据调整
    },
      series: series
    };
    myChart.setOption(option);
    return () => {
        myChart.dispose();
    }
  }
  const initTechCanvas = (data) => {
    const chartDom = chartRef.current;
    const myChart = echarts.init(chartDom);
    const option = {
      grid: {
        top: 0,
        right: 0,
        bottom: 0,
        left: 0,
        containLabel: true
    },
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        right: 10, // 距离右侧的距离
        top: 'center',     
        // 或者使用 bottom 和 left 等属性调整位置
    },
      series: [
        
        {
          name: '应用程序统计信息',
          type: 'pie',
          label: {
            show: false // 不显示标签
        },
          radius: ['90%', '110%'],  // 增大半径
          center: ['35%', '80%'],   // 将圆心下移
          startAngle: 180,
          endAngle: 0,
          // data: [
          //   { value: list.frontendApplicationCount, name: '前端' },
          //   { value: list.backendApplicationCount, name: '后端' }
          // ],
          data: data.data,
          color: ['#43B5F4', '#89CFF5']
       
        }
      ],
      graphic: [{
        type: 'text',
    
        top: '70%',
        left:'25%',
        style: {
          text: `应用程序总数: ${data.applicationTotalCount}`,
          textAlign: 'center',
          fill: '#333',
          fontSize: 14,
         
        }
      }],
    };
    myChart.setOption(option);
    return () => {
        myChart.dispose();
    }
  }
 //获取所有用户信息
//  const getUserListInfo = () => {
//   const dataParam = {
//     isQueryLocked:1,
//     current:1,
//     size:999999999999
//   }
//   getUserList(dataParam).then((res) => {
//     if(res){
//       setUserList(res.records || [])
    
//     }
//   }).catch((data) => {
//     message.error(data?.msg)
//   })
// }
//获取代码提交
const getCodeCommit = (info) => {
  const dataParam = {
    startTime:endTime?`${endTime} 00:00:00`:'',
    endTime:startTime?`${startTime} 23:59:59`:'',
    username:info?info.username:selUser[0]?.username,
    id:info?info.id:selUser[0]?.id
  }
  if(!dataParam.username){
    dataParam.username = '';
  }
  getUserSysCommit(dataParam).then((res) => {
    if(res){
      setCommitInfo(res)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}

//流水线
useEffect(() => {
  const chart = echarts.init(lineRef.current);
  const option = {
    title: {
    text: '300次',
    left: 'center',
    top: 'center',
    textStyle: {
      color: '#2A2A2A',
      fontSize: 20,
      fontWeight: 'bold'
    }
  },
  color: ['#43B5F4', '#89CFF5'],
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',  
    bottom: '5%',
    left: 'right',
    // formatter: function(name) {
    //   // 自定义 legend 文字显示
    //   return '类别：' + name;
    //   // 或者根据数据返回更复杂的内容
    // }
  },
  series: [
    {
     
      type: 'pie',
      radius: ['50%', '70%'],
      avoidLabelOverlap: false,
      label: {
        show: false,
        position: 'center'
      },
  
      labelLine: {
        show: false
      },
      data: [
        { value: 168, name: `执行成功 168次` },
        { value: 132, name: `执行失败 132次` }
      ]
    }
  ]
};
  chart.setOption(option);
  return () => {
    chart.dispose();
  };
}, []);
 
  useEffect(() => {
    //应用程序脚手架
    getBaselineListData()
    //应用程序统计信息
    getTechStackRankData()
    //技术文档统计信息
    getDocCount()
    //api发布总数
    getApiCount()
    //应用系统统计
    getStatisticApp()
   //获取所有用户
  //  getUserListInfo()
  
  }, [date,startTime,endTime]);
   
  useEffect(() => {
    //应用系统占比
    getStatisticMiddle(isPass)
  }, [date,isPass,startTime,endTime]);

  useEffect(() => {
    //获取代码提交记录
    if(selUser.length === 0){
      getCodeCommit(userInfo)
    }else{
      getCodeCommit()
    }
    
  }, [date,selUser,startTime,endTime]);
  return (
    <div className="platformBoard">
      <div className="platformBoard-header">
        <div style={{display:'flex',alignItems:'center',justifyContent:'space-between'}}> 
          <div className='flex-app' style={{marginBottom:'25px'}}>
              <div className='header-icon'></div>
              <div>概览</div>
          </div>
          <div>
            <Space>
                <RangePicker format={dateFormat}    onChange={(value, dateString) => {
                  setEndTime(dateString[0]);setStartTime(dateString[1])
                }} />
                <Select  style={{width:'200px'}} onChange={handleDateChange} value={date}>
                  <Option key={"全部"} value={"全部"}>全部</Option>
                  <Option key={"0.5"} value={"0.5"}>近半年</Option>
                  <Option key={"1"} value={"1"}>近1年</Option>
                  <Option key={"2"} value={"2"}>近2年</Option>
                </Select>
            </Space>
          </div>
        </div>
      </div>
      <div className="platformBoard-content ">
      <div style={{display:'flex',marginBottom:'10px'}}>
        <CommenTextCard info={appInfo} title={"应用系统统计"} type="application" />
        <CommenTextCard info={info} title={"应用程序脚手架模板使用情况"} type="app" />
        <CommenTextCard info={docinfo} title={"技术文档统计信息"} type="doc"/>
        <CommenTextCard info={apiInfo} title={"API发布总数"} type="api" />
      </div>
       <div style={{display:'flex',marginBottom:'10px'}}>
          <Card className="platformBoard-content-box" style={{width:'35%'}}>
            <div>
              <img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />
              <Select value={isPass} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setIspass(value)}} >
                <Option key={"1"} value={"1"}>容器中间件在各业务域的应用系统占比</Option>
                <Option key={"2"} value={"2"}>各业务域使用PaaS服务数量</Option> 
              </Select>
            </div>
            <div className="content-main">
                <div style={{width:'100%'}}>
                <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                  <div ref={middleRef} style={{ width: '100%', height: '100%' }} />
                </div>
                    
                </div>             
            </div>
          </Card>
          <Card className="platformBoard-content-box" style={{width:'40%'}}>
            <div  className="commen-flex">
             <p> <img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />代码质量概况（待统计）</p>
             <div>
                <Select className="border-bottom-only"  style={{width:'120px',marginRight:'10px'}} placeholder="请选择仓库" >
                </Select>
                <Select  className="border-bottom-only"  style={{width:'120px'}} placeholder="请选择分支" >
                </Select>
             </div>
            </div>
            <div className="content-main">
                <div style={{width:'100%'}}>
                <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                  <Code />
                </div>
                    
                </div>             
            </div>
          </Card>
          <Card className="platformBoard-content-box"  style={{width:'25%'}}>
            <div>
              <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>流水线执行情况（待统计）</span></p>
            </div>
            <div className="content-main">
                <div style={{width:'100%'}}>
                <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                  <div ref={lineRef} style={{ width: '100%', height: '300px' }} /> 
                </div>
                    
                </div>             
            </div>
          </Card>
              
       </div>
       <div style={{height:'350px',display:'flex',alignItems:'center'}}>
                <Card className="platformBoard-content-box" style={{width:'50%',height:'100%'}}>
                    <div className="commen-flex">
                      <p className="platformBoard-content-title"><img src={platTitle} style={{ height: "20px",marginRight:'10px'}} />  <span>应用程序统计信息</span></p>
                        <Select  style={{width:'200px'}} onChange={handleChange} value={tech} >
                          <Option key={"前后端"} value={"前后端"}>前后端</Option>
                          <Option key={"技术栈"} value={"技术栈"}>技术栈</Option>
                        </Select>
                      </div>
                    
                      <div className="content-main">
                    
                        <div style={{width:'100%'}}>
                        <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                          <div ref={chartRef} style={{ width: '100%', height: '100%' }} />
                        </div>
                            
                          </div>             
                      </div>
                </Card>
                <PlatTableCard info={apiInfo} title={"开发人员参与应系统代码提交分布"} data={userList} setSelUser={setSelUser} commitInfo={commitInfo}/>
       </div>
      </div>
    </div>
  );
};


export default PlatformBoard;




