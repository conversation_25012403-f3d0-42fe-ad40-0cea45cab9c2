import React, { useEffect, useRef, useState } from "react";
import {Input,Button ,Table,message,Col, Row,Select,Card,Badge , Space,Segmented   } from 'antd';

import { getMiddlewareInfos } from '@/services';
import './platformBoard.less';
import './middle.less';
import  platTitle from '@/assets/images/platTitle.png';
import elasticsearchIcon from '../../assets/images/board/elasticsearch.svg';
import kafkaIcon from '../../assets/images/board/kafka.svg';
import mysqlIcon from '../../assets/images/board/mysql.svg';
import rabbitmqIcon from '../../assets/images/board/rabbitmq.svg';
import redisIcon from '../../assets/images/board/redis.svg';
import rocketmqIcon from '../../assets/images/board/rocketmq.svg';
import sentinelIcon from '../../assets/images/board/sentinel.png';
import zookeeperIcon from '../../assets/images/board/zookeeper.png';
import defaultIcon from '../../assets/images/board/default.svg'
import xxlIcon from '../../assets/images/board/xxl-job.png';
import nacosIcon from '../../assets/images/board/nacos.png';
//////////////////////////////////开始////////////////////////////
import MiddleWarePlatformBoard from './middleWarePlatformBoard'
import Overview from './overview'
import ApplicationBoard from './applicationBoard'
const MiddleplatformBoard = () => {
   /////////////////////////////////////开始////////////////////////////////////
   const [size, setSize] = useState('总览')
   const [middleList, setMiddleList] = useState(
    []
  );
   useEffect(() => {
    getMiddlewareInfosList()
  }, []);
     //获取头部
      const getMiddlewareInfosList = () => {
        getMiddlewareInfos().then((res) => {
          if(res){
          setMiddleList(res)
          }
        }).catch((data) => {
          message.error(data?.msg)
        })
      }
      const getImageSrc = (imageType) => {
        const imageTypeLower = imageType.toLowerCase()
        switch(imageTypeLower) {
          case 'kafka': return kafkaIcon;
          case 'mysql': return mysqlIcon;
          case 'rabbitmq': return rabbitmqIcon;
          case 'redis': return redisIcon;
          case 'rocketmq': return rocketmqIcon;
          case 'sentinel-dashboard': return sentinelIcon;
          case 'zookeeper': return zookeeperIcon;
          case 'elasticsearch': return elasticsearchIcon;
          case 'xxljob': return xxlIcon;
          case 'nacos': return nacosIcon;
          default: return defaultIcon;
        }
      };
   const headerData=[
    {
        image:platTitle,
        title:'ZooKeeper',
        count:1
    },
    {
        image:platTitle,
        title:'Elasticsearch',
        count:3
    },
    {
        image:platTitle,
        title:'RockerMQ',
        count:8
    },
    {
        image:platTitle,
        title:'Kafka',
        count:9
    },
    {
        image:platTitle,
        title:'Rabbitmq',
        count:2
    },
    {
        image:platTitle,
        title:'Redis',
        count:10
    }
]

  return (
    <div className="platformBoard middleBoard">
      <div className="middle-header">
        <p>已创建服务信息</p>
        <div style={{display:'flex',alignItems:'center',height:'120px'}}> 
           
            {
              middleList.map((item)=>
                <div className="header-item">
                    <img  src={getImageSrc(item.middlewareType)}  style={{ height: "20px",width:'20px',margin:'0 auto',objectFit:'contain'}} />
                    <div className="header-item-title">{item.middlewareType}</div>
                    <div>
                        <Space>
                            <div>
                              <Space><Badge status="success" /><span>{item.running?item.running:0}个</span></Space>
                            </div>
                            <div>
                              <Space>
                                <Badge status="error" /><span>{item.error?item.error:0}个</span>
                              </Space>
                            </div>
                            <div>
                              <Space>
                                <Badge color="blue" /><span>{item.other?item.other:0}个</span>
                              </Space>
                            </div>
                        </Space>
                    </div>
                </div>
                )
            }
         
        </div>
      </div>
      <div style={{marginBottom:'5px'}}>
        <Segmented
        options={['总览', '应用维度分析', '中间件维度分析']}
        value={size}
        onChange={(value) => setSize(value)}
        block 
        style={{ backgroundColor: '#D8D8D8' }}
        />
      </div>
     
            {
              size === '中间件维度分析'?
              <div className="platformBoard-content ">
                <MiddleWarePlatformBoard middleList={middleList}  />
              </div>
              :(
                size === '总览'? <div className="platformBoard-content-overview ">
                <Overview middleList={middleList} />
              </div>:(
                size === '应用维度分析'? <div className="platformBoard-content-overview ">
                <ApplicationBoard  />
              </div>:''
              )
              )
            }
 
    </div>
  );
};


export default MiddleplatformBoard;




