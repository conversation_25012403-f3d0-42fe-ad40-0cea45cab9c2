.platformBoard{
    padding: 16px;
    .platformBoard-content-title{
        font-size: 15px;
        color: #10173F;
        display: flex;
        align-items: center;
    }
    .content-main{
        display: flex;
        justify-content: space-between;
        margin-top: 20px;
        align-items: center;
        height: calc(100% - 30px);
        .content-total{
            font-size: 36px;
        }
        .content-text{
            color: #646566;
            width: 60%;
            .content-text-contain{
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-bottom: 5px;
            }
            .left{
              width:35%;
              margin-right:20px
            }
            .right{
              width:65%;
              height:100%
            }
        }
    }
    .commen-flex{
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .platformBoard-content-box{
        width: calc(49% + 10px);
        margin-right: 10px;
        height: 400px;
    }
    .commen-flex-p{
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      
  }
  .micro-card-body{
    height: 100% !important;
    padding: 12px;
  }
  .commenTextCard{
    .num-add{
      color: #047C1C;
      font-size: 24px;
      padding-left: 5px;
    }
   
    .content-main-commencard{
      width: 50%;
      margin-right: 10px;
      display: flex;
      flex-direction: column;
      .platformBoard-content-title{
        margin-bottom: 20px;
      }
    }
    .content-main-commencard-right{
      width: 50%;
      height: 100%;
    }
    .content-total{
      margin-top: -8px;
      .content-total-text{
        font-size: 30px;
        color: #323233;
      }
    }
    .content-text-contain{
      display: flex;
      align-items: center;
      font-size: 12px;
      color: #646566;
      justify-content: space-between;
      margin-bottom: 5px;
      padding-left: 30px;
    }
    .left{
      // width: 100%;
    }
   }
   .PaasBlockCard{
    .PaasBlockCardTop{
      text-align: center;
      margin-top: 10px;
      h3{
        color: #323233;
        font-size: 30px;
      }
      .PaasBlockCardTopTitle{
        color: #323233;
        font-size: 12px;
      }
    }
   }
   
}
:global {
    .ant-statistic-title {
      color: #666;
      font-size: 14px;
      margin-bottom: 8px;
    }

    .ant-statistic-content {
      color: #1f1f1f;
      font-size: 24px;
      font-weight: bold;
    }
  }
.border-bottom-only.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) .ant-select-selector {
    border: none !important;
    border-bottom: 1px solid #d9d9d9 !important;
    border-radius: 0 !important;
    background-color: transparent !important;
  }
  
  .border-bottom-only.ant-select:hover .ant-select-selector {
    border-color: #40a9ff !important;
    box-shadow: none !important;
  }
  
  .border-bottom-only.ant-select-focused .ant-select-selector {
    border-color: #40a9ff !important;
    box-shadow: none !important;
  }
  .commen-block{
    height: 50%;
    text-align: center;
    background: linear-gradient(to bottom, #D5EFFF, #fff);
    background-color: #40a9ff;
    display: inline-block;
    margin-right: 10px;
    margin-bottom: 10px;
    padding:5px;
    width: calc(100% / 2 - 10px);
    vertical-align: top;
    .text-num{
      color: #008DDC;
      font-size: 24px;
      font-weight: bold;
    }
  }
  
  .paas-commen-block{
    height: 50%;
    text-align: center;
    
    display: inline-block;
    margin-right: 6px;
    margin-bottom: 10px;
    padding:5px;
    width: 31%;
   
    .text-num{
      color: #008DDC;
     
      font-weight: bold;
    }
    .text-total{
      color: #09A210;
      margin: 5px 0px;
    }
  
  }
  .PaasBlockCardBottom{
    display: flex;
    font-size: 12px;
    .cpu{
      background: linear-gradient(to top, #E6F5FF, #fff);
    }
    .memory{
      background: linear-gradient(to top, #FFF2D6, #fff);
    }
    .disk{
      background: linear-gradient(to top, #EFFEEB, #fff);
      margin-right: 0px;
    }
  }