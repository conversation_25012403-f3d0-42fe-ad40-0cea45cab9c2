import React, { useEffect, useRef, useState } from "react";
import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Card,Dropdown, Menu  } from 'antd';
import { getAppRank,getMiddlewareResources,getMiddlewareCluster,getDomainsRank,getResourcesTrend,getDomainslabel} from '@/services';
import * as echarts from 'echarts';
import './platformBoard.less';
import <PERSON><PERSON><PERSON> from './shuipao'

import MiddleUsageGauge from './bar';


const Overview = ({middleList}) => {
  
  ///////////////开始/////////////////
  const lineRef = useRef(null)
  const usechartRef = useRef(null);
  const [clusterList, setClusterList] = useState([]);
  const [clusterId, setClusterId] = useState();
  const [resourceType, setResourceType] = useState('cpu');
  const [domainResourceType, setDomainResourceType] = useState('cpu');
 
  const [titleList, setTitleList] = useState([]);
  const [cpuTop, setCpuTop] = useState([]);
  const [memoryTop, setMemoryTop] = useState([])

  const [domaintitleList, setDomaintitleList] = useState([]);
  const [domainCpuTop, setDomainCpuTop] = useState([]);
  const [domainMemoryTop, setDomainMemoryTop] = useState([])
  const [domainDatatitleList, setDomainDatatitleList] = useState([])
  
  
  const [clusterResource, setClusterResource] = useState(
   {}
  );

  
  useEffect(() => {
    getMiddlewareClusterList()
     getResourcesTrendInfo()
  }, []);

  useEffect(() => {
    getMiddlewareResourcesInfo(clusterId)
  }, [clusterId]);
  useEffect(() => {
    getAppRankResource(resourceType)
  }, [resourceType]);

  useEffect(() => {
    getDomainsRankResource(domainResourceType)
  }, [domainResourceType]);

    //获取集群
const getMiddlewareClusterList = () => {
  getMiddlewareCluster().then((res) => {
    if(res){
      setClusterList(res)
      setClusterId(res[0]?.clusterId)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
    //获取集群资源使用情况
    const getMiddlewareResourcesInfo = (id) => {
      if(!id){
        return;
      }
      getMiddlewareResources({
        clusterId:id
      }).then((res) => {
        if(res){
          setClusterResource(res)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
    //获取应用资源TOP10
    const getAppRankResource = (type) => {
      if(!type){
        return;
      }
      getAppRank({
        type:type
      }).then((res) => {
        if(res){
          const titleList = [];
          const cpuTop = [];
          const memoryTop = [];
          if(res.length > 0){
            res.forEach((item)=>{
              titleList.push(item.appName);
              cpuTop.push(item.cpu)
              memoryTop.push(item.memory)
            })
          }
          setTitleList(titleList)
          setCpuTop(cpuTop)
          setMemoryTop(memoryTop)
         
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
    //获取应用域资源占用情况
      const getDomainsRankResource = (type) => {
        if(!type){
          return;
        }
        getDomainsRank({
          type:type
        }).then((res) => {
          if(res){
            const domaintitleList = [];
            const domianCpuTop = [];
            const domainMemoryTop = [];
            if(res.length > 0){
              res.forEach((item)=>{
                domaintitleList.push(item.appDomain);
                domianCpuTop.push(item.cpu)
                domainMemoryTop.push(item.memory)
              })
            }
            setDomaintitleList(domaintitleList)
            setDomainCpuTop(domianCpuTop)
            setDomainMemoryTop(domainMemoryTop)
          
          }
        }).catch((data) => {
          message.error(data?.msg)
        })
      }
      useEffect(() => {
        getDomainsLabelList(domaintitleList)
      }, [domaintitleList]);

         //获取应用域label
         const getDomainsLabelList = (titleArray) => {
          if(!titleArray || titleArray.length === 0){
            return;
          }
          getDomainslabel().then((res) => {
            if(res && res.records.length > 0){
              const newArray = [];
              const dataList = res.records;
              titleArray.forEach((item)=>{
                if(item === 'nodomain'){
                  newArray.push("暂无应用域")
                }else{
                  dataList.forEach((list)=>{
                    if(list.value === item){
                      newArray.push(list.label)
                    }
                  })
                }
              })
             setDomainDatatitleList(newArray)
            }
          }).catch((data) => {
            message.error(data?.msg)
          })
        }
      //域资源使用趋势图
      const getResourcesTrendInfo = () => {
        getResourcesTrend().then((res) => {
          if(res){
            if(res.length){
              const trandTitle = [];
              const trandCpu = [];
              const trandMemory = [];
              res.forEach((item)=>{
                trandTitle.push(item.monthString)
                trandCpu.push(item.cpu)
                trandMemory.push(item.memory)
              })
              initMonthData(trandTitle,trandCpu,trandMemory)
              
              // setTrandTitle(trandTitle)
              // setTrandCpu(trandCpu)
              // setTrandMemory(trandMemory)
            }
          }
        }).catch((data) => {
          message.error(data?.msg)
        })
      }
  //中间件类型分布
 const initMiddleType = (data) => {
  const chart = echarts.init(lineRef.current);
  const option = {
  color: ['#0a91b1', '#079669','#dc2625','#ea580b','#7c3aed','#0d9488'],
  tooltip: {
    trigger: 'item',
    show:true
  },
  legend: {
    orient: 'vertical',  
    bottom: '5%',
    left: 'right',
    show:false
  },
  series: [
      {
        name: '中间件类型分布',
        type: 'pie',
        radius: '50%',
        data:data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
};
  chart.setOption(option);
  return () => {
    chart.dispose();
  };
 }
 const initMonthData = (title,cpuData,memoryData) => {
  if (!usechartRef.current) return;
  const chart = echarts.init(usechartRef.current); 
 const option = {
  grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    },
     // 设置系列指示器颜色
     formatter: function (params) {
      // 自定义formatter来设置小圆点颜色
      let result = params[0].name + '<br>';
      params.forEach(function (item) {
        // 根据系列名称设置不同颜色的小圆点
        let color = item.seriesName === 'cpu' ? '#0d9488' : '#0a91b1';
        let type = item.seriesName === 'cpu' ? 'Core' : 'GB';
        result += '<span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:' + color + '"></span>';
        result += item.seriesName + ' ' + item.value + type +'<br>';
      });
      return result;
    }
  },
  legend: {
    data: ['cpu', 'memory'],
    show:false
  },
  toolbox: {
      feature: {
        saveAsImage: {
          show: false // 设置为 false 隐藏下载图标
        }
      }
    },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      data: title
    }
  ],
  yAxis: [
    {
      type: 'value'
    }
  ],
  series: [
    {
      name: 'cpu',
      type: 'line',
      stack: 'Total',
      areaStyle: {
        color:'#0d9488'
      },
      emphasis: {
        focus: 'series',
        
      },
      data: cpuData
    },
    {
      name: 'memory',
      type: 'line',
      stack: 'Total',
      areaStyle: {
          color:'#0a91b1'
      },
      emphasis: {
        focus: 'series'
      },
      data: memoryData
   }
    
  ]
};
  chart.setOption(option);

  // 响应式处理
  const handleResize = () => chart.resize();
  window.addEventListener('resize', handleResize);

  return () => {
    window.removeEventListener('resize', handleResize);
    chart.dispose();
  };
 }
useEffect(() => {
  if(middleList &&  middleList.length>0){
    const filterArray=[];
    middleList.forEach((element) => {
      filterArray.push({
        value:element.instanceSum,
        name:element.middlewareType
      })
    });
    initMiddleType(filterArray)
  }
  }, [middleList]);
   

  return (
    <div className="platformBoard overview">
      <div className="platformBoard-content ">
       <div style={{display:'flex',marginBottom:'10px'}}>
          <Card className="platformBoard-content-box" style={{width:'50%'}}>
            <div  className="commen-flex">
              <p>集群资源概览</p>
              <Select value={clusterId} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setClusterId(value)}}>
                {clusterList.map(method => (
                  <Option key={`${method.clusterId}`} value={method.clusterId}>{`${method.clusterName}`}</Option>
                ))}
              </Select>
            </div>
            <div className="content-main">
                <div style={{width:'100%'}}>
                <div className="echarts-container" style={{height:'250px',width:'100%',display:'flex'}}>
                  <Shuipao percentage={clusterResource.cpuRequestPercent?clusterResource.cpuRequestPercent:0} data={clusterResource} type='cpu'/>
                  <Shuipao  percentage={clusterResource.memoryRequestPercent?clusterResource.memoryRequestPercent:0}  data={clusterResource} type='memory'/>
                </div>
                    
                </div>             
            </div>
          </Card>
          <Card className="platformBoard-content-box" style={{width:'50%'}}>
          <div  className="commen-flex">
            <p>中间件类型分布</p>
          </div>
          <div className="content-main">
              <div style={{width:'100%'}}>
                <div ref={lineRef} style={{ width: '100%', height: '300px' }} /> 
              </div>             
          </div>
        </Card>
       </div>
       <div style={{display:'flex',marginBottom:'10px'}}>
            <Card className="platformBoard-content-box" style={{width:'50%'}}>
                <div  className="commen-flex">
                  <p>应用系统资源占用TOP10</p>
                  <Select value={resourceType} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setResourceType(value)}} >
                    <Option key={`cpu`} value={'cpu'}>cpu</Option>
                    <Option key={`memory`} value={'memory'}>memory</Option>
                  </Select>
                </div>
                <div className="content-main">
                    <div style={{width:'100%'}}>
                        <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                            <MiddleUsageGauge data={resourceType === 'cpu'?cpuTop:memoryTop} color={"#5daa4b"} title={titleList} type={resourceType === 'cpu'?'Core':'GB'} />
                        </div>
                        
                    </div>             
                </div>
            </Card>
            <Card className="platformBoard-content-box" style={{width:'50%'}}>
                <div  className="commen-flex">
                  <p>应用域资源占用情况</p>
                  <Select value={domainResourceType} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setDomainResourceType(value)}} >
                    <Option key={`cpu`} value={'cpu'}>cpu</Option>
                    <Option key={`memory`} value={'memory'}>memory</Option>
                  </Select>
                </div>
                <div className="content-main">
                    <div style={{width:'100%'}}>
                        <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                        {
                          domainDatatitleList.length &&  <MiddleUsageGauge data={domainResourceType === 'cpu'?domainCpuTop:domainMemoryTop} color={"#0a91b1"} title={domainDatatitleList} type={domainResourceType === 'cpu'?'Core':'GB'}/>
                        }
                           
                        </div>
                        
                    </div>             
                </div>
            </Card>
         </div>
         <div style={{display:'flex',marginBottom:'10px'}}>
            <Card className="platformBoard-content-box" style={{width:'100%'}}>
                <div  className="commen-flex">
                    <p>资源使用趋势</p>
                </div>
                <div className="content-main">
                    <div style={{width:'100%'}}>
                        <div className="echarts-container" style={{height:'250px',width:'100%'}}>
                            <div ref={usechartRef} style={{ width: '100%', height: '100%' }} id="liquidChart"></div>
                        </div>
                        
                    </div>             
                </div>
            </Card>
      </div>
      </div>
    </div>
  );
};


export default Overview;




