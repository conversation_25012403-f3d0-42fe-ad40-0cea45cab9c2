import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-liquidfill'

const MiddleUsageGauge = ({data,color,title,type,barWidth='40px'}) => {
  const barchartRef = useRef(null);

  useEffect(() => {
    if (!barchartRef.current) return;
    const chart = echarts.init(barchartRef.current);
    const option = {
      tooltip: {  
        trigger: 'axis',
        formatter: function(params) {
          // params是一个数组，包含当前轴上的所有系列数据
          let result = params[0].name + '<br/>'; 
          params.forEach(function(item) {
            result += item.value + `${type}`; // 添加单位
          });
          return result;
        }
      },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            top: '10%',
            containLabel: true
        },
        xAxis: {
          type: 'category',
          data:title,
          axisLabel:{
            rotate: 45,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            data: data,
            type: 'bar',
            barWidth: barWidth,
            itemStyle: {
              color: Array.isArray(color)?
              function(params) {
                return color[params.dataIndex % 5];
              }:color
                },
           
          }
        ]
      };

    chart.setOption(option);

    // 响应式处理
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, [data]);



  return (
    <div style={{height:'calc(100% - 32px)',width:'100%'}}>
      <div ref={barchartRef} style={{ width: '100%', height: '100%' }}></div>
    </div>
  );
};

export default MiddleUsageGauge;