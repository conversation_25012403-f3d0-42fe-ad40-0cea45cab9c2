import React, { useEffect, useRef, useState } from "react";
import './platformValue.less';
import { Col, Row, Tag } from "antd";

import valueIcon from '../../assets/images/valueIcon.svg';
import backIcon from '../../assets/images/backIcon.svg';
import { getValueDashboardApi, getValueDashboardPaas, getValueDashboardScaffold, getValueDashboardSecurity, getValueDashboardService, getValueDashboardSystem, getValueDashboardTool } from "@/services";

const PlatformValue = () => {
    const [toolInfo, setToolInfo] = useState({
        codeRepositoryCount: 0,
        pipelineCount: 0,
        paasServiceCount: 0,
        objectStorageCount: 0
    });
    const [scaffoldInfo, setScaffoldInfo] = useState({
        totalCount: 0,
        backendCount: 0,
        frontendCount: 0
    });
    const [systemInfo, setSystemInfo] = useState({
        platformSystemCount: 0,
        platformAppCount: 0,
        syncCount: 0
    });
    const [serviceInfo, setServiceInfo] = useState({
        techComponentCount: 0,
        accessCount: 0,
        paasServiceCount: 0,
        instanceCount: 0
    });
    const [apiInfo, setApiInfo] = useState({
        apiTotalCount: 0,
        apiReuseCount: 0,
        apiReuseRate: ''
    })

    const [paasInfo, setPaasInfo] = useState({
        applicationCount: 0,
        middlewareInstanceCount: 0,
        timeSaving: 0,
        resourceSavingRate: 0,
        highAvailabilityCount: 0
    })
    const [securityInfo, setSecurityInfo] = useState({
        securityComponentCount: 0,
        dependencyScanCount: 0,
        codeReviewCommentCount: 0
    })

    useEffect(() => {
        // 可管可视
        fetchView()
        // 可加速
        fetchSpeed()
        // 可降低
        fetchLower()
    }, []);

    const fetchView = () => {
        getValueDashboardTool().then(res => {
            setToolInfo(res)
        })
        getValueDashboardSystem().then(res => {
            setSystemInfo(res)
        })
        getValueDashboardScaffold().then(res => {
            setScaffoldInfo(res)
        })
    }
    const fetchSpeed = () => {
        getValueDashboardService().then(res => {
            setServiceInfo(res)
        })
        getValueDashboardApi().then(res => {
            setApiInfo(res)
        })
    }

    const fetchLower = () => {
        getValueDashboardPaas().then(res => {
            setPaasInfo(res)
        })
        getValueDashboardSecurity().then(res => {
            setSecurityInfo(res)
        })
    }
    

    return (
        <div className="platformValue">
            <div className="platformValue-header">
                <div style={{display:'flex',alignItems:'center',justifyContent:'space-between'}}> 
                    <div className='flex-app' style={{marginBottom:'25px'}}>
                        <div className='header-icon'></div>
                        <div>价值看板</div>
                    </div>
                </div>
            </div>
            <div className="platformValue-content">
                <div className="content-item">
                    <div className="box-title">
                        <div className="title-name">可管可视</div>
                    </div>
                    <Row gutter={12}>
                        <Col span={8}>
                            <div className="top-content" style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                <div style={{ width: '40%', padding:'0 20px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '30px', height: '60px' }}>{scaffoldInfo.totalCount}</div>
                                    <div>共使用脚手架模板创建应用程序</div>
                                </div>
                                <div style={{ width: '60%', padding:'0 20px', textAlign: 'center' }}>
                                    <div className="top-text">
                                        <span>后端</span>
                                        <span>{scaffoldInfo.backendCount}</span>
                                    </div>
                                    <div className="top-text">
                                        <span>前端</span>
                                        <span>{scaffoldInfo.frontendCount}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="bottom-title">基于模板创建应用，用户体验感一致、规范化</div>
                        </Col>
                        <Col span={8}>
                            <div className="top-content" style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                <div style={{ width: '30%', padding:'0 15px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '30px', height: '60px' }}>{systemInfo.platformSystemCount}</div>
                                    <div>平台应用系统数</div>
                                </div>
                                <div style={{ width: '30%', padding:'0 15px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '30px', height: '60px' }}>{systemInfo.platformAppCount}</div>
                                    <div>平台应用程序数</div>
                                </div>
                                <div style={{ width: '40%', padding:'0 15px', textAlign: 'center' }}>
                                    <div style={{marginBottom: '10px'}}>已同步下游系统：</div>
                                    <div style={{maxHeight: '100px', overflow: 'auto'}}>
                                        <Tag color="#1A9FE7" style={{marginBottom: '10px'}}>GitLab</Tag>
                                        <Tag color="#1A9FE7" style={{marginBottom: '10px'}}>CMBD</Tag>
                                        <Tag color="#1A9FE7" style={{marginBottom: '10px'}}>Devops</Tag>
                                        <Tag color="#1A9FE7" style={{marginBottom: '10px'}}>SCA</Tag>
                                    </div>
                                    <div>同步次数：<span style={{color: '#1A9FE7'}}>{systemInfo.syncCount}</span></div>
                                </div>
                            </div>
                            <div className="bottom-title">应用系统、应用程序 数据资产透明化、可分享</div>
                        </Col>
                        <Col span={8}>
                            <div className="top-content" style={{ display: 'flex', flexWrap: 'wrap', gap: '15px', padding: '15px 30px' }}>
                                <div className="block">
                                    <div style={{ fontSize: '24px' }}>{toolInfo.codeRepositoryCount}</div>
                                    <div>自动创建代码仓库</div>
                                </div>
                                <div className="block">
                                    <div style={{ fontSize: '24px' }}>{toolInfo.pipelineCount}</div>
                                    <div>自动创建流水线</div>
                                </div>
                                <div className="block">
                                    <div style={{ fontSize: '24px' }}>{toolInfo.paasServiceCount}</div>
                                    <div>自助申请PaaS</div>
                                </div>
                                <div className="block">
                                    <div style={{ fontSize: '24px' }}>{toolInfo.objectStorageCount}</div>
                                    <div>自助申请对象存储</div>
                                </div>
                            </div>
                            <div className="bottom-title">流程和工具规范化，有工具支撑的更自动化流程</div>
                        </Col>
                    </Row>
                </div>
                
                <div className="content-item" style={{marginTop: '10px'}}>
                    <div className="box-title">
                        <div className="title-name">可加速</div>
                    </div>
                    <Row gutter={12}>
                        <Col span={12}>
                            <div className="top-content" style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                <div style={{ width: '25%', padding:'0 20px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '14px', height: '50px' }}>共上架技术组件</div>
                                    <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                        <img src={valueIcon} style={{ height: "30px", width: '30px' }} />
                                        <span style={{fontSize: '30px'}}>{serviceInfo.techComponentCount}</span>
                                    </div>
                                </div>
                                <div style={{ width: '25%', padding:'0 20px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '14px', height: '50px' }}>组件与服务被访问次数</div>
                                    <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                        <img src={valueIcon} style={{ height: "30px", width: '30px' }} />
                                        <span style={{fontSize: '30px'}}>{serviceInfo.accessCount}</span>
                                    </div>
                                </div>
                                <div style={{ width: '25%', padding:'0 20px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '14px', height: '50px' }}>共上架PaaS服务</div>
                                    <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                        <img src={valueIcon} style={{ height: "30px", width: '30px' }} />
                                        <span style={{fontSize: '30px'}}>{serviceInfo.paasServiceCount}</span>
                                    </div>
                                </div>
                                <div style={{ width: '25%', padding:'0 20px', textAlign: 'center' }}>
                                    <div style={{ fontSize: '14px', height: '50px' }}>已创建实例</div>
                                    <div style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                        <img src={valueIcon} style={{ height: "30px", width: '30px' }} />
                                        <span style={{fontSize: '30px'}}>{serviceInfo.instanceCount}</span>
                                    </div>
                                </div>
                            </div>
                            <div className="bottom-title">公共组件和PaaS组件服务化，加速应用建设效率</div>
                        </Col>
                        <Col span={12}>
                            <div className="top-content" style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                <div style={{ width: '30%', padding:'0 20px', textAlign: 'center' }}>
                                    <div>
                                        <div className="middle-right" style={{
                                            backgroundImage: `url(${backIcon})`,
                                        }}>
                                            <span style={{ color: 'white', fontSize: '24px' }}>{apiInfo.apiTotalCount}</span>
                                        </div>
                                        <span>API总数</span>
                                    </div>
                                </div>
                                <div style={{ width: '30%', padding:'0 20px', textAlign: 'center' }}>
                                    <div>
                                        <div className="middle-right" style={{
                                            backgroundImage: `url(${backIcon})`,
                                        }}>
                                            <span style={{ color: 'white', fontSize: '24px' }}>{apiInfo.apiReuseCount}</span>
                                        </div>
                                        <span>API复用数</span>
                                    </div>
                                </div>
                                <div style={{ width: '30%', padding:'0 20px', textAlign: 'center' }}>
                                    <div>
                                        <div className="middle-right" style={{
                                            backgroundImage: `url(${backIcon})`,
                                        }}>
                                            <span style={{ color: 'white', fontSize: '24px' }}>{apiInfo.apiReuseRate}</span>
                                        </div>
                                        <span>API复用率</span>
                                    </div>
                                </div>
                            </div>
                            <div className="bottom-title">增加API复用性，减少重复</div>
                        </Col>
                    </Row>
                </div>

                <div className="content-item" style={{marginTop: '10px'}}>
                    <div className="box-title">
                        <div className="title-name">可降低</div>
                    </div>
                    <Row gutter={12}>
                        <Col span={12}>
                            <div className="bottom-content" style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                <div style={{ width: '50%', padding:'0 40px', textAlign: 'center' }}>
                                    <div className="left-l">
                                        <div className="left-l-t">
                                            <span style={{fontWeight: 500}}>{paasInfo.applicationCount}</span>
                                            <span>个应用系统共申请</span>
                                        </div>
                                        <div className="left-l-b">
                                            <span style={{fontWeight: 500, fontSize: '36px'}}>{paasInfo.middlewareInstanceCount}</span>
                                            <span>个中间件实例</span>
                                        </div>
                                    </div>
                                </div>
                                <div style={{ width: '50%', padding:'0 40px', textAlign: 'center' }}>
                                    <div className="left-r">服务开通时间约节约<span>{paasInfo.timeSaving}人天</span></div>
                                    <div className="left-r">PaaS服务资源约节约<span>{paasInfo.resourceSavingRate}</span></div>
                                    <div className="left-r">PaaS服务双活高可用数<span>{paasInfo.highAvailabilityCount}</span></div>
                                </div>
                            </div>
                            <div className="bottom-title">专业公共技术组件PaaS，提升运行质量，降低成本</div>
                        </Col>
                        <Col span={12}>
                            <div className="bottom-content" style={{ display: 'flex', justifyContent: 'space-around', alignItems: 'center' }}>
                                <div style={{ width: '100%', padding:'0 50px', textAlign: 'center' }}>
                                    <div className="right-r">
                                        <span className="right-r-text">设立基线的安全组件</span>
                                        <span className="right-r-num">{securityInfo.securityComponentCount}个</span>
                                    </div>
                                    <div className="right-r">
                                        <span className="right-r-text">应用程序共执行依赖扫描</span>
                                        <span className="right-r-num">{securityInfo.dependencyScanCount}次</span>
                                    </div>
                                    <div className="right-r">
                                        <span className="right-r-text">产生代码评审意见</span>
                                        <span className="right-r-num">{securityInfo.codeReviewCommentCount}条</span>
                                    </div>
                                </div>
                            </div>
                            <div className="bottom-title">第三方和开源准入管理，降低运行和IT管理风险</div>
                        </Col>
                    </Row>
                </div>
            </div>
        </div>
    )
}

export default PlatformValue;