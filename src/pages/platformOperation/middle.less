.middleBoard{
    .middle-header{
        background-color: #fff;
        margin-bottom: 5px;
        border: 1px dotted #ccc;
        padding: 8px;
        border-radius: 3px;
    }
    .header-item{
        width: 15%;
        border-right: 1px solid #ccc;
        text-align: center;
        .header-item-title{
            margin-top: 5px;
            margin-bottom: 8px;
        }
    }
    .segmented-with-gap .ant-segmented-item {
        margin: 0 1.5px !important;
      }
    .platformBoard-content-box{
        background-color: #F7F8FB;
        height: auto;
    }
    // .platformBoard-content {
    //     background-color: #fff;
    //     padding: 8px 0px;
    //     padding-right: 0px;
    //     margin-right: -8px;
    // }
    .middle-content-title{
        justify-content: space-between;
    }
    .middle-content-left{
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 25px;
    }
    .middle-content-left-num{
        color: #119FA8;
        font-size: 20px;
        font-weight: bold;

    }  
    .middle-card-box{
        display: inline-block;
    } 
 .middle-card-box-middle{
    width: calc(100%/4 - 10px);
    margin-bottom: 10px;
 }
 .platformBoard-content-overview{
    background-color: none;
    .platformBoard{
        padding: 0px !important;
    }
    .platformBoard-content{
        background-color: transparent !important;
        margin-right: -8px;
        padding: 8px 0px;
        padding-right: 0px;
    }
   .platformBoard-content-box{
        background-color: #fff !important;
    }
 }
}
.overview{
    .sub-num{
        color: #2874e8;
        font-style: normal;
        font-weight: 700;
        font-size: 18px;
        text-shadow: 0 4px 4px rgba(34,110,230,.15);
        margin-bottom: 5px;
    }
    .container {
        position: relative;
        width: 50%;
        height: 100%;
        
    }
    // #liquidChart {
    //     width: 100%;
    //     height: 100%;
    // }
    .label {
        position: absolute;
        color: #fff;
        font-weight: bold;
        font-size: 14px;
     
        z-index: 10; /* 确保文字在水球之上 */
    }
    .label-cpu {
        bottom: 67px;
        left: 20px;
    }
    .label-core {
        top: 68px;
        right: 20px;
        color: #c8c8c8 !important;
    }
}
.middleWarePlatformBoard{
    .summary-total{
        display: flex;
        align-items: center;
        margin-top: 20px;
        .summary{
            width: 20%;
            text-align: center;
            h3{
                color: #119FA8;
                font-size: 20px;
                font-weight: bold;
            }
        }
    }
}