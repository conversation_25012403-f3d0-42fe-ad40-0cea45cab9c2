import React, { useEffect, useRef, useState } from "react";
import {DatePicker ,Input,Button ,Table,message,Tag , Row,Select,Card,Badge , Space,Segmented   } from 'antd';
const { RangePicker } = DatePicker;
import { getMiddleList,getSummary,getAppsList ,getDomainslabel ,statisticApp,statisticMiddle,getUserList,getUserSysCommit} from '@/services';
import * as echarts from 'echarts';
import './platformBoard.less';
import './middle.less';
import CommenTextCard from './Component/CommenTextCard';
import PlatTableCard from './Component/PlatTableCard'
import  platTitle from '@/assets/images/platTitle.png';
import Code from './Component/LineCard';
import  codeImg from '@/assets/images/code.png';
const dateFormat = 'YYYY-MM-DD';
import { useModel } from 'umi';
import {
  FunnelPlotOutlined
} from '@ant-design/icons';
//////////////////////////////////开始////////////////////////////
import MiddleTextCard from './Component/MiddleTextCard';

const middleWarePlatformBoard = ({middleList}) => {
  useEffect(() => {
    getList()
  }, []);
  const [middleData, setMiddleData] = useState([]);
  const [middle, setMiddle] = useState();
  const [summary, setSummary] = useState({});
  const [summaryOrigin, setSummaryOrigin] = useState({});
  const [data, setData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const onPageChange = (page,pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedRows, setSelectedRows] = useState([]);
  const [domainslabelList, setDomainslabelList] = useState([])
  const [newData, setNewData] = useState([])
  
  
  const columns = [
    {
      title: '应用系统',
      dataIndex: 'appName',
      key: 'appName'
    },
    {
      title: '应用域',
      dataIndex: 'appDomain',
      render: (_, { appDomain }) => (
        <>
        {
          appDomain?
          <Tag color={'blue'} key={appDomain}>
          {appDomain}
        </Tag>:''
        }
        </>
      ),
    },
    {
      title: '中间件服务名称',
      dataIndex: 'middlewareName',
      key: 'middlewareName',
    },
    {
      title: '中间件类型',
      dataIndex: 'middlewareType',
      key: 'middlewareType',
    },
    {
      title: 'CPU(Core)',
      dataIndex: 'cpu',
      key: 'cpu',
    },
    {
      title: '内存(GB)',
      dataIndex: 'memory',
      key: 'memory',
    },
    {
      title: '存储(GB)',
      dataIndex: 'storage',
      key: 'storage',
    },
    {
      title: '状态',
      key: 'statusText',
      dataIndex: 'statusText',
      render: (_, { statusText }) => (
        <>
        {
          statusText === '运行正常'?
          <Tag color="success">{statusText}</Tag>:(
            statusText === '数据删除中'? <Tag color="warning">{statusText}</Tag>:(
              statusText === '更新中'? <Tag color="processing">{statusText}</Tag>:(
                statusText === '创建失败'? <Tag color="error">{statusText}</Tag>:(
                  statusText === '驳回'? <Tag color="orange">{statusText}</Tag>:(
                    statusText === '审批中'? <Tag color="geekblue">{statusText}</Tag>:(
                      statusText === '重启中'? <Tag color="magenta">{statusText}</Tag>:
                     (
                      statusText === '运行异常'? <Tag color="red">{statusText}</Tag>:<Tag color="default">{statusText}</Tag>
                     )
                    )
                  )
                )
              )
            )
          )
        }

        </>
      ),
    },

  ];
  useEffect(() => {
    getSummaryData(middle)
  }, [middle]);

  const getList = () => {
      getMiddleList().then((res) => {
        if(res){
          setMiddleData(res)
          // if(res.length > 0){
          //   setMiddle(res[0])
          // }
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
  }
  const getSummaryData = (middle) => {
    getSummary({
      middlewareType:middle
    }).then((res) => {
      if(res){
       setSummary(res)
       setSummaryOrigin(res)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
}
const generateUniqueIds = (dataArray) => {
  return dataArray.map(item => ({
    ...item,
    id: crypto.randomUUID() // 生成标准的UUID
  }));
};
useEffect(() => {
  getDomainsLabelList();
}, []);
useEffect(() => {
  resetData(domainslabelList,data)
}, [data,domainslabelList]);
const resetData = (titleArray,dataList) => {
  console.log("aaaaa")
  console.log(dataList)
  if(dataList.length === 0 || titleArray.length === 0){
    return;
  }
  dataList.forEach((item)=>{
    if(item.appDomain === 'nodomain'){
      item.appDomain='暂无作用域'
    }else{
      titleArray.forEach((list)=>{
        if(list.value === item.appDomain){
          item.appDomain = list.label
        }
      })
    }
  })
  setNewData([...dataList])
  console.log(dataList)

 
 }
//获取应用域label
const getDomainsLabelList = () => {
 getDomainslabel().then((res) => {
   if(res && res.records.length > 0){
     setDomainslabelList(res.records)
   }
 }).catch((data) => {
   message.error(data?.msg)
 })
}
    const getAppsListInfo = () => {
        const param = {
          middlewareType:middle,
          pageNo:currentPage,
          pageSize:pageSize
        }
        setLoading(true);
          getAppsList(param).then((res) => {
            if(res){
              const dataArray = res.records;
             const newArray = generateUniqueIds(dataArray);
             setData(newArray)
              setTotal(res.total)
              setLoading(false);
             
            }
          }).catch((data) => {
            message.error(data?.msg)
          })
      }
      useEffect(() => {
        getAppsListInfo()
      }, [middle,currentPage,pageSize])

      // const onSelectChange = (newSelectedRowKeys) => {
      //   console.log('selectedRowKeys changed: ', newSelectedRowKeys);
      //   setSelectedRowKeys(newSelectedRowKeys);
      //   const total = calculateStats()
      // };
    
      const rowSelection = {
        selectedRowKeys,
        onChange: (selectedKeys, selectedRows) => {
          console.log(selectedRows)
          setSelectedRowKeys(selectedKeys);
          setSelectedRows(selectedRows);
          if(selectedRows.length === 0){
            setSummary({...summaryOrigin})
          }else{
            const total = calculateStats(selectedRows)
            setSummary({...total})
          }
         
        },
      };
      const calculateStats = (dataArray) => {
        // 1. 计算去重的appName数量
        const uniqueAppNames = [...new Set(dataArray.map(item => item.appName))];
        const appNum = uniqueAppNames.length;
        
        // 2. 计算去重的middlewareName数量
        const uniqueMiddlewareNames = [...new Set(dataArray.map(item => item.middlewareName))];
        const middlewareNum = uniqueMiddlewareNames.length;
        
        // 3. 累加所有cpu值
        const cpu = dataArray.reduce((sum, item) => sum + (item.cpu || 0), 0);
        
        // 4. 累加所有memory值
        const memory = dataArray.reduce((sum, item) => sum + (item.memory || 0), 0);
        // 4. 累加所有storage值
        const storage = dataArray.reduce((sum, item) => sum + (item.storage || 0), 0);
        return {
          appNum,
          middlewareNum,
          cpu,
          memory,
          storage
        };
      };
    
   /////////////////////////////////////开始////////////////////////////////////
   

  return (
    <div className="middleWarePlatformBoard">
      <div>
        {
          middleList.map((item)=>
            <MiddleTextCard info={item} title={"应用系统统计"} type="application"  />
          )
        } 
        </div>
        <div style={{display:'flex',marginBottom:'10px'}}>
            <Card className="platformBoard-content-box" style={{width:'100%',backgroundColor:'#fff'}}>
                <div>
                  <h2 style={{fontSize:'18px',marginBottom:'5px'}}>
                    <Space>
                      <FunnelPlotOutlined />中间件使用详情
                    </Space>
                  </h2>
                  <p  style={{marginBottom:'20px',fontSize:'14px'}}>查看使用特定中间件的应用系统</p>
                  <div style={{marginTop:'15px'}}>
                    <span>中间件类型：</span>
                    <Select value={middle} className="border-bottom-only"  style={{width:'200px'}} onChange={(value)=>{setMiddle(value)}} allowClear>
                    {middleData.map(method => (
                      <Option key={`${method}`} value={method}>{`${method}`}</Option>
                    ))}
                  </Select>
                  </div>
                  <div className="summary-total">
                      <div className="summary">
                        <h3>{summary.appNum}</h3>
                        <span>应用系统</span>
                      </div>
                      <div className="summary">
                        <h3>{summary.middlewareNum}</h3>
                        <span>中间件实例</span>
                      </div>
                      <div className="summary">
                        <h3>{summary.cpu}Core</h3>
                        <span>总CPU占用</span>
                      </div>
                      <div className="summary">
                        <h3>{summary.memory}GB</h3>
                        <span>总内存占用</span>
                      </div>
                      <div className="summary">
                        <h3>{summary.storage}GB</h3>
                        <span>总存储占用</span>
                      </div>
                  </div>
                </div>
                <div>
                  <Table columns={columns} dataSource={newData}
                  rowKey="id"
                  pagination={{
                    current:currentPage,
                    showTotal:()=>{return `共 ${total} 项数据`},
                    showSizeChanger:true,
                    onChange:(page,pageSize)=>{onPageChange(page,pageSize)},
                    total:total,
                    pageSize:pageSize
                  }}
                  loading={loading}
                  rowSelection={rowSelection}
                  />   
                </div>
            </Card>
      </div>
    </div>
  );
};


export default middleWarePlatformBoard;




