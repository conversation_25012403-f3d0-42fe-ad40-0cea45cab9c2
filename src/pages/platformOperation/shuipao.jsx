import React, { useEffect, useRef } from 'react';
import * as echarts from 'echarts';
import 'echarts-liquidfill'

const MemoryUsageGauge = ({ percentage = 0.3,data,type }) => {
  const chartRef = useRef(null);

  useEffect(() => {
    if (!chartRef.current) return;

    const chart = echarts.init(chartRef.current);
    
    // 根据百分比确定颜色
    const waveColor = percentage < 0.5 ? '#2874e8' : '#ff0000';
    
    const option = {
      series: [{
        type: 'liquidFill',
        radius: '80%',
        center: ['50%', '50%'],
        data: [percentage, percentage - 0.1, percentage - 0.2],
        amplitude: 6,
        color: [waveColor],
        backgroundStyle: {
          color: 'rgba(240, 240, 240, 0.2)',
          borderColor: '#000',
          borderWidth: 0,
          show:false
        },
     
        outline: {
          show: true,
          borderDistance: 2, // 边框与波浪图的距离
          borderRadius:3,
          itemStyle: {
            color: 'none', // 无填充色
            borderColor: '#ccc', // 灰色边框
            borderWidth: 1,
            borderType: 'dashed', // 虚线边框
            shadowBlur: 0 ,// 无阴影
            borderRadius:3
          }
        },
        shape: 'rect',
        padding: 0,
        // 设置自适应
        autoFit: true,
        label: {
          show: true,
          color: waveColor,
          insideColor: '#fff',
          fontSize: 24,
          fontWeight: 'bold'
        },
        itemStyle: {
          opacity: 0.8,
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.1)'
        }
      }],
    
    };

    chart.setOption(option);

    // 响应式处理
    const handleResize = () => chart.resize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
      chart.dispose();
    };
  }, [percentage]);



  return (
    <div style={{height:'100%',width:'50%',display:'flex',alignItems:'center'}}>
        <div class="container">
            <div ref={chartRef} style={{ width: '100%', height: '100%' }} id="liquidChart"></div>
            <div class="label label-cpu">{
              type === 'cpu'?'CPU':'内存'
            }</div>
            <div class="label label-core">
            {
              type === 'cpu'?'核':'Gi'
            }
            </div>
        </div>
       
       <div style={{marginLeft:'15px'}}>
        <p>
            <h3>项目总量</h3>
            <div className='sub-num' >{type === 'cpu'?data.totalCpu:data.totalMemory}</div>
        </p>
        <p>
            <h3>已使用</h3>
            <div className='sub-num'>{type === 'cpu'?data.usedCpu:data.usedMemory}</div>
        </p>
        <p>
            <h3>已分配</h3>
            <div className='sub-num'>{type === 'cpu'?data.requestCpu:data.requestMemory}</div>
        </p>
       </div>
    </div>
  );
};

export default MemoryUsageGauge;