.system-manage {
    
    .main-content {
        flex: 1;
        padding-left: 8px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;

        .list-content {
            margin-top: 10px;
            width: 100%;
            overflow-x: hidden;
            flex: 1;
            background-color: #ffffff;
            padding: 20px;

            .title-left {
                font-size: 14px;
                font-weight: 500;
                line-height: 32px;

                position: relative;

                padding-left: 16px;

                white-space: nowrap;
                letter-spacing: 1px;
                // background: pink;
                &.title-left-border {
                    padding-bottom: 4px;
                    border-bottom: 1px solid #eeeeee;
                    &::before {
                        margin-top: -2px;
                    }
                }
                &::before {
                    position: absolute;
                    top: 50%;
                    left: 0;

                    width: 6px;
                    height: 1.1em;
                    border-radius: 4px;

                    content: '';
                    transform: translateY(-50%);

                    background: #008cd6;
                }
            }
        }
    }
}