import React, { useEffect, useRef, useState } from 'react';
import { Card, Row, Col, Typography, Statistic, Button, Space, message, Select, Input, Radio, Table, DatePicker, Drawer, Form, Tag } from 'antd';
import "./index.less";
import { history } from 'umi';
import BreadcrumbNav from '@/components/BreadcrumbNav';
import dayjs from 'dayjs';
import { addSystemNotice, getAllUserList, getManageNoticeList } from '@/services';
import { BaseUrlEnum } from '@/enums/httpEnum';
import axios from 'axios';
import { ExclamationCircleOutlined, NotificationOutlined } from '@ant-design/icons';

const Manage = () => {

    const [searchParams, setSearchParams] = useState({
        noticeType: '',
        content: '',
        startTime: '',
        endTime: '',
        size: 10,
        current: 1,
    })
    const [tableData, setTableData] = useState([]);
    const [loading, setLoading] = useState(false);
    const [btnLoading, setBtnLoading] = useState(false);
    const [totalNum, setTotalNum] = useState(0);
    const [openFlag, setOpenFlag] = useState(false);
    const [form] = Form.useForm();
    const [formData, setFormData] = useState({
        noticeType: 1,
        content: '',
        sendTime: 'now',
        allUser: true,
        userIds: [],
        releaseTime: '',
        
    });
    const [userList, setUserList] = useState([]);
    const [searchTimeout, setSearchTimeout] = useState(null);
    useEffect(() => {
        fetchAllUserList('')
    },[])
    const fetchAllUserList = (paramVal) => {
        const query = {
            page: 1,
            size: 100,
            name: paramVal
        }
        getAllUserList(query).then(res => {
            setUserList(res.records)
        })
    }
    const handleSelectSearch = (val) => {
        fetchAllUserList(val)
    }
     // 防抖函数
  const debouncedSearch = (val) => {
    if (searchTimeout) {
        clearTimeout(searchTimeout);
    }
    const newTimeout = setTimeout(() => {
            handleSelectSearch(val);
        }, 100);
        setSearchTimeout(newTimeout);
    };

    // 清理定时器
    useEffect(() => {
        return () => {
        if (searchTimeout) {
            clearTimeout(searchTimeout);
        }
        };
    }, [searchTimeout]);

    useEffect(() => {
        fetchAllNoticeList()
    }, [searchParams])

    const fetchAllNoticeList = () => {
        setLoading(true)
        // todo 获取通知列表
        getManageNoticeList(searchParams).then(res => {
            setLoading(false)
            setTableData(res.records)
            setTotalNum(res.total)
        }).finally(() => {
            setLoading(false)
        })
    }
    const handleAddNotice = () => {
        const tempData = {
            noticeType: 1,
            content: '',
            sendTime: 'now',
            allUser: true,
            userIds: [],
            releaseTime: '',
        };
        setFormData(tempData);
        form.setFieldsValue(tempData);
        setOpenFlag(true)
    }
    const handleDateChange = (timer) => {
        if (timer && timer.length === 2) {
            // 检查 timer 是否为数组且长度为 2
            const startTime = dayjs(timer[0]).isValid()
                ? dayjs(timer[0]).format('YYYY-MM-DD HH:mm:ss')
                : '';
            const endTime = dayjs(timer[1]).isValid()
                ? dayjs(timer[1]).format('YYYY-MM-DD HH:mm:ss')
                : '';
            setSearchParams((prevParams) => ({
                ...prevParams,
                startTime,
                endTime,
            }));
        } else {
            // 如果 timer 不符合要求，清空 startTime 和 endTime
            setSearchParams((prevParams) => ({
                ...prevParams,
                startTime: '',
                endTime: '',
            }));
        }
    }
    const linkList = [
        {
            title: '平台配置'
        },
        {
            title: '系统通知管理'
        }
    ]

    const tableColumns = [
        {
            title: '状态',
            dataIndex: 'releaseStatus',
            key: 'releaseStatus',
            width: 80,
            render: (text) => {
                let tagColor = '';
                let tagText = '';
                if (text === 0) {
                    tagColor = '#476fdb';
                    tagText = '未推送';
                } else if (text === 1) {
                    tagColor = '#476fdb';
                    tagText = '已推送';
                }
                return <Tag color={tagColor}>{tagText}</Tag>;
            },
        },
        {
            title: '通知内容',
            dataIndex: 'content',
            key: 'content',
            ellipsis: true,
            width: 300,
        },
        {
            title: '通知类型',
            dataIndex: 'noticeType',
            width: 80,
            render: (text) => {
                let tagColor = '';
                let tagText = '';
                let iconComponent= <NotificationOutlined />;
                if (text === 1) {
                    tagColor = 'blue';
                    tagText = '系统通知';
                } else if (text === 2) {
                    tagColor = 'green'
                    tagText = '提醒'
                    iconComponent = <ExclamationCircleOutlined />;
                }
                // return <Tag color={tagColor}><NotificationOutlined style={{color: tagColor, marginRight: '5px'}} />{tagText}</Tag>;
                return (
                    <Tag color={tagColor}>
                        {React.cloneElement(iconComponent, { style: { color: tagColor, marginRight: '5px' } })}
                        {tagText}
                    </Tag>
                );
            },
        },
        {
            title: '推送人',
            dataIndex: 'senderName',
            key: 'senderName',
            ellipsis: true,
            width: 80,
        },
        {
            title: '推送时间',
            dataIndex: 'releaseTime',
            key: 'releaseTime',
            ellipsis: true,
            width: 80,
        }
    ];
    const onClose = () => {
        setOpenFlag(false)
        form.resetFields();
    }
    const handleSelectChange = (val: any, field: any) => {
        setFormData((prevData) => ({
            ...prevData,
            [field]: val,
        }));
    }
    const handleInputChange = (e: any, field: any) => {
        setFormData((prevData) => ({
            ...prevData,
            [field]: e.target.value,
        }));
    };
    const handleCancel = () => {
        onClose();
    }
    const handleSubmit = () => {
        setBtnLoading(true);
        form.validateFields()
            .then((values) => {
                // 处理提交逻辑
                const queryParams = {
                    ...values,
                    releaseTime: formData.releaseTime,
                    userIds: formData.userIds.join(',')
                };
                addSystemNotice(queryParams).then(res => {
                    setBtnLoading(false);
                    if (res) {
                        fetchAllNoticeList();
                        onClose();
                        message.success('新增成功');
                    }
                })
            })
        .catch((errorInfo) => {
            console.log('表单校验失败:', errorInfo);
            setBtnLoading(false);
        });
    }
    return (
        <div className='system-manage' style={{height:'100%',overflow:'auto', display: 'flex'}}>
            <div className='main-content'>
                <div style={{ display: 'flex', alignItems: 'center', background: '#fff' }}>
                    <BreadcrumbNav list={linkList} />
                </div>
                <div className='list-content'>
                    <div className="title-left">系统通知管理</div>
                    <Row style={{marginTop: '10px', marginBottom: 16}} justify="space-between">
                        <Col span={4}>
                            <Button type='primary' onClick={handleAddNotice}>新增通知</Button>
                        </Col>
                        <Col span={20}>
                            <Row justify="end" align="middle">
                                <Col>
                                    <Select
                                        value={searchParams.noticeType}
                                        onChange={(val) => {
                                            setSearchParams((prevParams: any) => ({
                                                ...prevParams,
                                                noticeType: val || '',
                                            }));
                                        }}
                                        style={{ width: 120, marginRight: 16 }}
                                    >
                                        <Select.Option value=''>全部类型</Select.Option>
                                        <Select.Option value={1}>系统通知</Select.Option>
                                        <Select.Option value={2}>提醒</Select.Option>
                                    </Select>
                                </Col>
                                <Col>
                                    <Input.Search
                                        placeholder="输入关键词搜索通知内容"
                                        allowClear
                                        onSearch={(val) => {
                                            setSearchParams((prevParams: any) => ({
                                                ...prevParams,
                                                content: val || '',
                                            }));
                                        }}
                                        onKeyDown={(e) => {
                                            if (e.key === 'Enter') {
                                                setSearchParams((prevParams: any) => ({
                                                    ...prevParams,
                                                    content: e.target.value || '',
                                                }));
                                            }
                                        }}
                                        style={{ width: 200, marginRight: 8 }}
                                    />
                                </Col>
                                <Col>
                                    <DatePicker.RangePicker
                                        showTime
                                        showNow
                                        onChange={handleDateChange}
                                    />
                                </Col>
                            </Row>
                        </Col>
                    </Row>
                    <div>
                        <Table
                            columns={tableColumns}
                            dataSource={tableData}
                            loading={loading}
                            pagination={{
                                showTotal:()=>{return `共 ${totalNum} 项数据`},
                                showSizeChanger: true,
                                current: searchParams.current,
                                pageSize: searchParams.size,
                                total: totalNum,
                                onChange: (pageNo, sizeNo) => {
                                    setSearchParams((prevParams: any) => ({
                                        ...prevParams,
                                        current: pageNo,
                                        size: sizeNo,
                                    }));
                                },
                            }}
                        />
                        <Drawer
                            title={'新建系统通知'}
                            placement="right"
                            width={520}
                            onClose={onClose}
                            closable={false}
                            open={openFlag}
                        >
                            <Form
                                form={form}
                                labelCol={{ span: 24 }}
                                initialValues={formData}
                                style={{
                                    display: 'flex',
                                    flexDirection: 'column',
                                    height: '100%',
                                }}
                            >
                                <div style={{ flex: 1 }}>
                                    <Form.Item
                                        label="类型"
                                        name="noticeType"
                                        rules={[
                                            { required: true, message: '请选择类型' },
                                        ]}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Select
                                            onChange={(e) => {
                                                handleSelectChange(e, 'type')
                                            }}
                                        >
                                            <Select.Option value={1}>系统通知</Select.Option>
                                        </Select>
                                    </Form.Item>
                                    <Form.Item
                                        label="通知内容"
                                        name="content"
                                        rules={[{ required: true, message: '请输入通知内容' }]}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Input.TextArea
                                            maxLength={200}
                                            showCount={true}
                                            onChange={(e) =>
                                                handleInputChange(e, 'content')
                                            }
                                        />
                                    </Form.Item>
                                    <Form.Item
                                        label="推送对象"
                                        name="allUser"
                                        rules={[
                                        { required: true, message: '请选择推送对象' },
                                        ]}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Radio.Group
                                            onChange={(e) => {
                                                handleInputChange(e, 'allUser')
                                            }} 
                                            value={formData.allUser}
                                        >
                                            <Radio value={true}>全部用户</Radio>
                                            <Radio value={false}>指定人员</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                    {!formData.allUser && (
                                        <Form.Item
                                            label="指定人员"
                                            name="userIds"
                                            rules={[
                                                { required: true, message: '请选择指定人员' },
                                            ]}
                                         wrapperCol={{ span: 24 }}
                                        >
                                            <Select
                                                mode='multiple'
                                                options={userList}
                                                fieldNames={{
                                                    value: 'id',
                                                    label: 'userRealname',
                                                }}
                                                onSearch={(val) =>{
                                                    debouncedSearch(val)
                                                }}
                                                showSearch
                                                filterOption={false}
                                                onChange={(val) => {
                                                    setFormData((prevData) => ({
                                                        ...prevData,
                                                        userIds: val,
                                                    }))
                                                }}
                                            />
                                        </Form.Item>
                                    )}
                                    <Form.Item
                                        label="发送时间"
                                        name="sendTime"
                                        rules={[
                                            { required: true, message: '请选择发送时间' },
                                        ]}
                                        wrapperCol={{ span: 24 }}
                                    >
                                        <Radio.Group
                                            onChange={(e) => {
                                                handleInputChange(e, 'sendTime')
                                            }}
                                            value={formData.sendTime}
                                        >
                                            <Radio value="now">立即发送</Radio>
                                            <Radio value="releaseTime">指定时间</Radio>
                                        </Radio.Group>
                                    </Form.Item>
                                    {formData.sendTime === 'releaseTime' && (
                                        <Form.Item
                                            label="指定时间"
                                            name="releaseTime"
                                            rules={[
                                                { required: true, message: '请选择指定时间' },
                                            ]}
                                            wrapperCol={{ span: 24 }}
                                        >
                                            <DatePicker
                                                showTime
                                                format="YYYY-MM-DD HH:mm:ss"
                                                onChange={(d, s) => {
                                                    setFormData((prevData) => ({
                                                        ...prevData,
                                                        releaseTime: s,
                                                    }))
                                                }}
                                            />
                                        </Form.Item>
                                    )}
                                </div>
                                <Form.Item
                                    wrapperCol={{ offset: 8, span: 16 }}
                                    style={{ textAlign: 'right' }}
                                >
                                    <Button
                                        onClick={handleCancel}
                                        style={{ marginRight: 8 }}
                                    >
                                        取消
                                    </Button>
                                    <Button
                                        type="primary"
                                        onClick={handleSubmit}
                                        loading={btnLoading}
                                    >
                                        发送通知
                                    </Button>
                                </Form.Item>
                            </Form>
                        </Drawer>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default Manage;