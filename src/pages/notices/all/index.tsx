import React, { useEffect, useRef, useState } from 'react';
import { Card, Row, Col, Typography, Statistic, Button, Space, message, Select, Input, Radio, Table, Tag, Modal } from 'antd';
import "./index.less";
import { history } from 'umi';
import { CheckOutlined, ExclamationCircleOutlined, NotificationOutlined } from '@ant-design/icons';
import { getCurrentUserNotice, setAllNoticeRead } from '@/services';

const Notices = () => {

    const [noticeList, setNoticeList] = useState([])
    const [searchParams, setSearchParams] = useState({
        content: '',
        isRead: '',
        noticeType: '',
        size: 10,
        current: 1,
    });
    const [loading, setLoading] = useState(false)
    const [totalNum, setTotalNum] = useState(0);
    const [isModalVisible, setIsModalVisible] = useState(false);
    const [currentItem, setCurrentItem] = useState(null);

    useEffect(() => {
        fetchAllList()
    }, [searchParams])

    const fetchAllList = () => {
        setLoading(true)
        getCurrentUserNotice(searchParams).then(res => {
            setLoading(false)
            setNoticeList(res.records)
            setTotalNum(res.total)
        }).finally(() => {
            setLoading(false)
        })
    }

    const handleCancel = () => {
        history.push(`/homePage`);
    };

    const handleSetRead = () => {
        const query = {
            noticeId: '',
            noticeType: searchParams.noticeType,
            allRead: true,
        }
        setAllNoticeRead(query).then((res) => {
            if (res) {
                message.success('设置成功')
                fetchAllList()
            } else {
                message.error('设置失败')
            }
        })
    }

    const setSingleRead = (item) => {
        if (!item.isRead) {
            const query = {
                noticeId: item.noticeId,
                noticeRelationId: item.noticeRelationId,
                allRead: false,
                allUser: item.allUser,
            }
            setAllNoticeRead(query).then((res) => {
                if (res) {
                    fetchAllList()
                }
            })
        }
    }

    const handleViewClick = (item) => {
        setSingleRead(item)
        // 查看调用单条数据已读方法
        if (item.noticeType === 1) {
            setCurrentItem(item);
            setIsModalVisible(true);
        } else {
            let route = `${item.routePath}/${item.routeParams.instanceId}`;
            if (item.routeParams.instanceId && item.routeParams.taskId) {
                route += `/${item.routeParams.taskId}`;
            }
            history.push(route);
        }
        
    };

    const handleModalOk = () => {
        setIsModalVisible(false);
    };

    const handleModalCancel = () => {
        setIsModalVisible(false);
    };

    const tableColumns = [
        {
            title: '读取状态',
            dataIndex: 'isRead',
            width: 80,
            render: (text) => (
                text ? (
                    <span style={{ color: 'green' }}><CheckOutlined /></span>
                ) : (
                    <span style={{ display: 'inline-block', width: 10, height: 10, backgroundColor: '#2b7fff', borderRadius: '50%' }}></span>
                )
            ),
        },
        {
            title: '通知内容',
            dataIndex: 'content',
            ellipsis: true,
            width: 300,
        },
        {
            title: '通知类型',
            dataIndex: 'noticeType',
            width: 80,
            render: (text) => {
                let tagColor = '';
                let tagText = '';
                let iconComponent= <NotificationOutlined />;
                if (text === 1) {
                    tagColor = 'blue';
                    tagText = '系统通知';
                } else if (text === 2) {
                    tagColor = 'green'
                    tagText = '提醒'
                    iconComponent = <ExclamationCircleOutlined />;
                }
                // return <Tag color={tagColor}><NotificationOutlined style={{color: tagColor, marginRight: '5px'}} />{tagText}</Tag>;
                return (
                    <Tag color={tagColor}>
                        {React.cloneElement(iconComponent, { style: { color: tagColor, marginRight: '5px' } })}
                        {tagText}
                    </Tag>
                );
            },
        },
        {
            title: '通知时间',
            dataIndex: 'releaseDesc',
            width: 80,
        },
        {
            title: '操作',
            render: (text, record) => (
                <span
                    onClick={() => handleViewClick(record)}
                    style={{ color: '#2b7fff', cursor: 'pointer' }}
                >
                    查看
                </span>
            ),
            width: 80,
        },
    ];

    return (
        <div className='notice-content'>
            <Row justify="space-between" align="middle" style={{ padding: '25px 30px' }}>
                <Col>
                    <Typography.Title style={{textIndent: '20px'}} level={4}>全部通知</Typography.Title>
                </Col>
                <Col>
                    <Space>
                        <Button onClick={handleCancel}>取消</Button>
                        <Button type="primary" onClick={handleSetRead}>
                            全部设为已读
                        </Button>
                    </Space>
                </Col>
            </Row>
            <div className='main-content'>
                <Row justify="space-between" align="middle" style={{ marginBottom: 16 }}>
                    <Col>
                        <Radio.Group
                            buttonStyle="solid"
                            onChange={(e) => {
                                setSearchParams((prevParams: any) => ({
                                    ...prevParams,
                                    isRead: e.target.value,
                                }));
                            }}
                            value={searchParams.isRead}
                        >
                            <Radio.Button value=''>全部</Radio.Button>
                            <Radio.Button value={0}>未读</Radio.Button>
                            <Radio.Button value={1}>已读</Radio.Button>
                        </Radio.Group>
                    </Col>
                    {/* 右侧区域，放置下拉框和搜索框 */}
                    <Col>
                        <Select
                            value={searchParams.noticeType}
                            onChange={(val) => {
                                setSearchParams((prevParams: any) => ({
                                    ...prevParams,
                                    noticeType: val || '',
                                }));
                            }}
                            style={{ width: 120, marginRight: 16 }}
                        >
                            <Select.Option value=''>全部类型</Select.Option>
                            <Select.Option value={1}>系统通知</Select.Option>
                            <Select.Option value={2}>提醒</Select.Option>
                        </Select>
                        <Input.Search
                            placeholder="输入内容搜索"
                            onSearch={(val) => {
                                setSearchParams((prevParams: any) => ({
                                    ...prevParams,
                                    content: val || '',
                                }));
                            }}
                            onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                    setSearchParams((prevParams: any) => ({
                                        ...prevParams,
                                        content: e.target.value || '',
                                    }));
                                }
                            }}
                            style={{ width: 300 }}
                        />
                    </Col>
                </Row>
                <div style={{ height: '100%' }}>
                    <Table
                        dataSource={noticeList}
                        columns={tableColumns} 
                        size="middle"
                        loading={loading}
                        scroll={{ x: '100%' }}
                        pagination={{
                            showTotal:()=>{return `共 ${totalNum} 项数据`},
                            showSizeChanger: true,
                            current: searchParams.current,
                            pageSize: searchParams.size,
                            total: totalNum,
                            onChange: (pageNo, sizeNo) => {
                                setSearchParams((prevParams: any) => ({
                                    ...prevParams,
                                    current: pageNo,
                                    size: sizeNo,
                                }));
                            },
                        }}
                    />
                </div>
            </div>
            <Modal
                title="系统通知"
                visible={isModalVisible}
                footer={null}
                onCancel={handleModalCancel}
            >
                {currentItem && (
                    <div>
                        <div style={{ display: 'flex', marginBottom: 10 }}>
                        <span style={{ width: 80 }}>通知内容：</span>
                        <span style={{ flex: 1 }}>{currentItem.content}</span>
                    </div>
                    <div style={{ display: 'flex' }}>
                        <span style={{ width: 80 }}>发布时间：</span>
                        <span style={{ flex: 1 }}>{currentItem.releaseTime}</span>
                    </div>
                </div>
                )}
            </Modal>
        </div>
    )
}

export default Notices;