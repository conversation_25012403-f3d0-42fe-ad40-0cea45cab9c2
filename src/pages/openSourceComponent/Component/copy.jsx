import React, { useState, useEffect } from 'react';
import {
    Table,
    Input,
    Button,
    Space,
    Form,
    message,
    Pagination,
    Modal,
    Select,
} from 'antd';
import {
    EditOutlined,
    SaveOutlined,
    CloseOutlined,
    PlusOutlined,
    ExclamationCircleOutlined,
} from '@ant-design/icons';
import { getApp, getReviewAppList, updateReview } from '@/services/review';
const { Search } = Input;
const { confirm } = Modal;
import ContentWrapper from '@/pages/workplace/components/ContentWrapper';
import GToolBar from '@/components/PureUI/GToolbar';
import { useAppSystemBreadnav, useRouter } from '@/utils/bustool';
import { useHistory, useParams, useLocation } from 'umi';
import axios from 'axios';
const { Option } = Select;

const addReview = ({taskIds}) => {
    const [dataSource, setDataSource] = useState([]);
    const [dataSourceOrigin, setDataSourceOrigin] = useState([]);

    const [name, setName] = useState();
    const [application, setApplication] = useState([]);
    const [isEdit, setIsEdit] = useState(false);
    const [editingKey, setEditingKey] = useState('');
    const [form] = Form.useForm();
    const [stats, setStats] = useState({
        highLevel: 0,
        mediumLevel: 0,
        lowLevel: 0,
        noLevel: 0,
    });
    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });
   
    console.log(taskIds);
    const [applicationId, setApplicationId] = useState(taskIds || '');
    // let { taskId: taskIds } = useParams<{
    //     taskId: any;
    // }>();

    const [loading, setLoading] = useState(false);
    //获取所有应用系统
    const getApplication = () => {
        getApp().then((res) => {
            if (!taskIds && res.length > 0) {
                setApplicationId(res[0].id);
            }
            setApplication(res);
        });
    };
    useEffect(() => {
        getApplication();
    }, []);

    useEffect(() => {
        if (!applicationId) {
            return;
        }
        getData(applicationId);
    }, [applicationId]);
    useEffect(() => {
        const newStats = {
            highLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '高风险',
            ).length,
            mediumLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '中风险',
            ).length,
            lowLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '低风险',
            ).length,
            noLevel: dataSourceOrigin.filter(
                (item) => item.riskLevel === '无风险',
            ).length,
        };
        setStats(newStats);
    }, [dataSourceOrigin]);
    const getData = (id) => {
        setLoading(true);
        getReviewAppList(id).then((res) => {
            setDataSource(res);
            setDataSourceOrigin(res);
            setLoading(false);
            //setData([{ name: '111', useVersion: '222' }]);
        });
    };
    const saveData = () => {
        const filterArray = [];
        dataSource.forEach((item) => {
            if (item.isEdit) {
                filterArray.push(item);
            }
        });
        updateReview(JSON.stringify(filterArray)).then((res) => {
            if (res) {
                message.success('更新成功');
                getData(applicationId);
                setIsEdit(false)
            }
        });
    };
    // 表格列配置
    const columns = [
        {
            title: '应用程序',
            dataIndex: 'applicationName',
        },
        {
            title: '应用程序英文名称',
            dataIndex: 'applicationEnName',
        },

        {
            title: '审阅人',
            dataIndex: 'reviewer',
        },
        {
            title: '审阅时间',
            dataIndex: 'reviewTime',
            render: (text) => {
                return (
                    <div
                        className="ellipsis"
                        style={{ width: '150px' }}
                        title={text}
                    >
                        {text}
                    </div>
                );
            },
        },
        {
            title: '审阅意见',
            dataIndex: 'reviewOpinion',
            render: (text, record, index) => {
                return (
                    isEdit?  <Input
                    value={text}
                    maxLength={100}
                    style={{ width: '200px' }}
                    showCount
                    onChange={(e) => {
                        const newData = [...dataSource];
                        newData.forEach((item, key) => {
                            if (item.id === record.id) {
                                newData[key].reviewOpinion = e.target.value;
                                newData[key].isEdit = true;
                            }
                        });
                        setDataSource(newData);
                    }}
                />:{text}

                );
            },
        },
        {
            title: '风险等级',
            dataIndex: 'riskLevel',
            render: (text, record, index) => {
                console.log(text);
                return (
                   isEdit? <Select
                   defaultValue={text}
                   style={{ width: 150 }}
                   onChange={(value) => {
                       const newData = [...dataSource];
                       newData[index].riskLevel = value;
                       newData[index].isEdit = true;
                       setDataSource(newData);
                   }}
                   options={[
                       { value: '高风险', label: '高风险' },
                       { value: '中风险', label: '中风险' },
                       { value: '低风险', label: '低风险' },
                       { value: '无风险', label: '无风险' },
                   ]}
               />:{text}
                );
            },
        },
    ];

    const onSearch = (value) => {
        setName(value);
    };
    const handleTableChange = (page) => {
        setPagination((prev) => ({
            ...prev,
            current: page.current,
            pageSize: page.pageSize,
        }));
        // getData(page.current, page.pageSize);
        setEditingKey('');
    };

    useEffect(() => {
        // getData(1, 10);
        setPagination({
            ...pagination,
            current: 1,
        });
    }, [name]);
    const { breadcrumbs } = useAppSystemBreadnav({
        appSystemBreadItems: ['新增审阅'],
    });

    return (
        <ContentWrapper permsView={true} breadcrumb={breadcrumbs}>
            <GToolBar splitTitle title="新增审阅" />
            <div>
                <div style={{ marginBottom: '15px' }}>
                    <Select
                        style={{ width: '250px' }}
                        showSearch
                        value={applicationId}
                        onChange={(value) => {
                            setApplicationId(value);
                        }}
                    >
                        {application.map((item) => (
                            <Option key={item.id} value={item.id}>
                                {item.cnName}
                            </Option>
                        ))}
                    </Select>
                </div>
                <div
                    style={{
                        marginBottom: '10px',
                        display: 'flex',
                        justifyContent: 'space-between',
                    }}
                >
                    <div>
                        <p style={{ fontSize: '18px', color: '#3D3D3D' }}>
                            总结：共{dataSource.length}个应用程序，其中
                            {stats.highLevel}个高风险，{stats.mediumLevel}
                            个中风险，{stats.lowLevel}个低风险，{stats.noLevel}
                            个无风险
                        </p>
                    </div>
                    <div>
                        <Space>
                            <Button type="primary" onClick={()=>{setIsEdit(true)}}>
                            编辑
                            </Button>
                            <Button
                                type="primary"
                                onClick={() => {
                                    saveData();
                                }}
                            >
                                保存
                            </Button>
                        </Space>
                    </div>
                </div>

                <Table
                    size="small"
                    bordered={false}
                    scroll={{
                        x: 1000, // 设置最小表格宽度，触发横向滚动
                    }}
                    columns={columns}
                    dataSource={dataSource}
                    rowKey={(record) => record.id}
                    rowClassName="editable-row"
                    pagination={{
                        ...pagination,
                        showSizeChanger: true,
                        showQuickJumper: true,
                        showTotal: (total, range) => `共 ${total} 条`,
                        pageSizeOptions: ['5', '10', '15', '20'],
                        defaultCurrent: 1,
                    }}
                    onChange={handleTableChange}
                    loading={loading}
                />
            </div>
        </ContentWrapper>
    );
};

export default addReview;
