import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';

import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import './list.less';

import {DatePicker ,Input,Button ,Table,message,Col, Row,Select,Space,Modal } from 'antd';

import { createBaseline,getCategories,getBaselineList,updateBaseline,delBaseline } from '@/services';
import { ExclamationCircleOutlined } from '@ant-design/icons';
import AddComponent from './addComponent'
import storage from '@/utils/storage';
import { localStorageEnum } from '@/enums/storageEnum';

const { confirm } = Modal;

const OpenSourceComponentList = () => {

  const [productName, setProductName] = useState('');
  const [apiPath, setApiPath] = useState('');
  const [pubOrgName, setPubOrgName] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [timeRange, setTimeRange] = useState();
  const [time, setTime] = useState([]);
  useEffect(() => {
   
  }, [])
  const USERINFO = localStorageEnum.USERINFO; // 保存用户信息
  const userInfo = storage.getLocal(USERINFO)
console.log(userInfo)
 
  const USERAUTHMAP = localStorageEnum.USERAUTHMAP; // 保存用户信息
  const userAuthMap = storage.getLocal(USERAUTHMAP)
  ///////开始//////////////
  const linkList = [
    {
      title: '开源组件管理'
    }
  ]
const [data, setData] = useState([]);
const [categoriesList, setCategoriesList] = useState([]);
// const [categoriesName, setCategoriesName] = useState('');
const [total, setTotal] = useState(0);
const [name, setName] = useState('');
const [category, setCategory] = useState('');
const [isAddOpen, setIsAddOpen] = useState(false);
const [editData, setEditData] = useState();



const onPageChange = (page,pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };
  //获取分类
const getCategoriesList = () => {
  getCategories().then((res) => {
    if(res){
      setCategoriesList(res || [])
    }else{
      message.error(res?.message || res?.msg)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
  //获取列表
  const getBaselineListData = () => {
    const dataParam = {
      pageSize:pageSize,
      pageNo:currentPage,
      name:name,
      category:category
    }
    getBaselineList(dataParam).then((res) => {
      if(res){
        setData(res.records || [])
        setTotal(res.total)
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
   //创建基线
   const handleCreateBaseline = (param) => {
    // param.version = param.version.join(',')
    createBaseline(param).then((res) => {
      if(res){
        message.success("创建成功")
        setIsAddOpen(false)
        getBaselineListData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  //编辑基线
  const editComponent = (param) => {
   setEditData(param)
   setIsAddOpen(true)
  }
    //更新基线
  const handleUpdate = (param) => {
    // param.version = param.version.join(',')
    updateBaseline(param).then((res) => {
        if(res){
          message.success("更新成功")
          setIsAddOpen(false)
          getBaselineListData()
        }else{
          message.error(res?.message || res?.msg)
        }
      }).catch((data) => {
        message.error(data?.msg)
      })
    }
  const handleChange = (value) => {
    setCurrentPage(1)
    setCategory(value)
  };
  const addBaseline = (value) => {
    setEditData()
    setIsAddOpen(true)
  };
  const showDelConfirm = (item) => {
    confirm({
      title: '删除?',
      icon: <ExclamationCircleOutlined />,
      content: `确定删除组件 ${item.name}？`,
      onOk() {
        handleDelete(item.id)
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  };
  const handleDelete = (id) => {
    delBaseline({id:id}).then((res) => {
      if(res){
        message.success("删除成功")
        getBaselineListData()
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  
  useEffect(() => {
    getCategoriesList()
  
  }, []);
  useEffect(() => {
    getBaselineListData()
  }, [currentPage,pageSize,name,category]);

  const columns = [
    {
      title: '组件名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <span>{text}</span>,
    },
    {
      title: '分类',
      key: 'category',
      dataIndex: 'category'
    },
    {
      title: '基线版本',
      dataIndex: 'version',
      key: 'version',
    },
    {
      title: '最低版本',
      dataIndex: 'minVersion',
      key: 'minVersion',
    },
    {
      title: '更新人',
      dataIndex: 'manager',
      key: 'manager',
    },
    {
      title: '更新时间',
      key: 'updateTime',
      dataIndex: 'updateTime'
    },
    {
      title: '许可证描述',
      key: 'permit',
      dataIndex: 'permit'
    },
    {
        title: '描述',
        key: 'description',
        dataIndex: 'description',
        className:"text-overflow",
        render: (text) => <span title={text}>{text}</span>,
      },
   
    {
      title: '操作',
      key: '操作',
      render: (api, record) => (
        
        <Space>
        <Button
          type="text"
          size="small"
          onClick={()=>{editComponent(record)}}
          style={{ color: '#008cd6' }}
         
        >
        编辑
        </Button>
        <Button
        type="text"
        size="small"
        danger
        onClick={()=>{showDelConfirm(record)}}
       
      >
      删除
      </Button>
         
        </Space>
     
      ),
    },
  ];



  return (
    <div className="openSourceComponent ">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div  className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header">
          <div className="middleware-base-info">
          <Row>
            <div className='flex-app' style={{marginBottom:'25px'}}>
                <div className='header-icon'></div>
                <div>组件基线列表</div>
            </div>
          </Row>
            <Row>
                <Col span={24} style={{display:'flex',justifyContent:'space-between'}}>
                    {
                      userAuthMap.openSourceComponent.openSourceComponentList.PUT?<Button style={{margin:'0px 5px'}} type='primary' onClick={()=>{addBaseline()}}>新增</Button>:<div></div>
                    }
                    
               
                    <div className='mySubscriptions-search'>
                    <Space>
                        <Input placeholder="名称" style={{width:'250px'}} value={name} onChange={(e)=>{setName(e.target.value,setCurrentPage(1))}} />
                        <Select onChange={handleChange} style={{width:'200px'}} placeholder="分类" allowClear>
                        {categoriesList.map(item => (
                          <Option key={`${item}`} value={item}>{`${item}`}</Option>
                        ))}
                      </Select>
                    </Space>
                       
                    </div>
                </Col>
                
            </Row>
          
          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div>
            <Table columns={columns} dataSource={data} 
                pagination={{
                  current:currentPage,
                  showTotal:()=>{return `共 ${total} 项数据`},
                  showSizeChanger:true,
                  onChange:(page,pageSize)=>{onPageChange(page,pageSize)},
                  total:total,
                  pageSize:pageSize
                }}
            />
          </div>
        </div>
      </div>
     {
      isAddOpen && <AddComponent setIsAddOpen={setIsAddOpen} isAddOpen={isAddOpen} categoriesList={categoriesList} handleCreateBaseline={handleCreateBaseline} editData={editData} handleUpdate={handleUpdate}/>
     }
     
    </div>
  );
};



export default OpenSourceComponentList;