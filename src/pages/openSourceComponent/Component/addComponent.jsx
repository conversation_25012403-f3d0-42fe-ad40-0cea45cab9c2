import React, { useEffect, useState } from 'react';


import {Modal ,Input,Button ,Space ,message,Col, Row,Select,Form } from 'antd';
import '../index.less';
const options = [];



const addComponent = ({isAddOpen,setIsAddOpen,categoriesList,handleCreateBaseline,editData,handleUpdate}) => {
const [form] = Form.useForm();

  const onFinish = (values) => {
    if(editData){
        handleUpdate({...values,id:editData.id})
    }else{
        handleCreateBaseline(values)
    }
    
  };

  const onReset = () => {
    setIsAddOpen(false);
  };
  const handleChange = (value) => {
    
  };
  useEffect(() => {
   if(editData){
    form.setFieldsValue({
        name:editData.name,
        category:editData.category,
        version:editData.version,
        description:editData.description,
        minVersion:editData.minVersion,
        permit:editData.permit
      })
   }
  
  }, [editData]);
  return (
    <div>
        <Modal
            title={editData?'编辑基线':'新增基线'}
            open={isAddOpen}
            onCancel={()=>{ setIsAddOpen(false)}}
            onOk={()=>{setIsAddOpen(false)}}
            footer={null}
            
        >
        <Form
        
            form={form}
            name="control-hooks"
            onFinish={onFinish}
            style={{width:'100%'}}
            layout='vertical'
            className="addBaseline"
            
        >
            <Form.Item name="name" label="组件名称" rules={[{ required: true }]} >
                <Input placeholder='请填写组件名称' maxLength={100} disabled={editData} />
            </Form.Item>
            <Form.Item name="category" label="分类" rules={[{ required: true }]} >
                <Select
                    placeholder="请选择"
                    allowClear
                    disabled={editData}
                >
                    {categoriesList.map(item => (
                        <Option key={`${item}`} value={item}>{`${item}`}</Option>
                    ))}
                </Select>
            </Form.Item>
            <Form.Item name="version" label="基线版本" rules={[{ required: true }]}>
                <Input placeholder='请填写基线版本' />
            </Form.Item>
            <Form.Item name="minVersion" label="最低版本" rules={[{ required: true }]} >
                <Input placeholder='请填写最低版本' />
            </Form.Item>
            <Form.Item name="permit" label="许可证描述" >
                <Input.TextArea allowClear maxLength={50} />
            </Form.Item>
            <Form.Item name="description" label="描述" >
                <Input.TextArea allowClear maxLength={200}  />
            </Form.Item>
            <Form.Item style={{textAlign:'right',marginTop:'35px'}}>
                <Space>
                    <Button htmlType="button" onClick={onReset}>
                    取消
                    </Button>
                    <Button type="primary" htmlType="submit">
                    确定
                    </Button>
                   
                </Space>
            </Form.Item>
        </Form>
        </Modal>
    </div>
  );
};



export default addComponent;