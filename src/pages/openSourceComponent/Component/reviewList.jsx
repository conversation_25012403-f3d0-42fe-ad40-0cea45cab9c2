import React, { useState, useEffect } from 'react';
import {
    Table,
    Input,
    Button,
    Space,
    Form,
    message,
    Pagination,
    Modal,
    Badge,
    Row ,
    Col
} from 'antd';
import { useParams, history } from 'umi';
import {

    PlusOutlined,

} from '@ant-design/icons';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
const { Search } = Input;
import axios from 'axios';
import './list.less';
import { getReviewList } from '@/services';
import { BaseUrlEnum } from '@/enums/httpEnum'

const { confirm } = Modal;

const ReviewList = () => {
    const [dataSource, setDataSource] = useState([{ name: '测试数据' }]);
    const [name, setName] = useState('');
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        getData(name);
    }, []);
    const getData = (value) => {
        const param = {
            applicationSystemName: value ? value : '',
        };
        setLoading(true);
        getReviewList(param).then((res) => {
            setDataSource(res);
            setLoading(false);
        });
    };
    const onSearch = (value) => {
        setName(value);
        getData(value);
    };
    const downloadResponse = async () => {
        try {
            const timestamp = new Date().getTime();
            const response = await axios.get(
                `${BaseUrlEnum.BASELINEAPI}/open-source/compt/baseline/review/export`,
                {
                    responseType: 'blob', // 关键配置
                    params: {
                        applicationSystemName: name,
                        _t: timestamp,
                    },
                },
            );
            const contentDisposition = response.headers.get('Content-Disposition');
            let fileName = 'download.xlsx'; // 默认文件名
            
            if (contentDisposition) {
              // 处理UTF-8编码的文件名
              const fileNameMatch = contentDisposition.match(/filename\*?=UTF-8''(.+?)(;|$)/i);
              
              if (fileNameMatch && fileNameMatch[1]) {
                // 解码URL编码的文件名
                fileName = decodeURIComponent(fileNameMatch[1]);
              } else {
                // 处理没有UTF-8标识的文件名
                const fallbackMatch = contentDisposition.match(/filename="?(.+?)"?(;|$)/i);
                if (fallbackMatch && fallbackMatch[1]) {
                  fileName = fallbackMatch[1];
                }
              }
            }
            // 创建Blob对象
            const blob = new Blob([response.data], {
                type:
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            });

            // 创建下载链接
            const downloadUrl = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = downloadUrl;

          

            link.download = fileName;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            // 释放URL对象
            window.URL.revokeObjectURL(downloadUrl);
        } catch (error) {
            console.error('下载失败:', error);
        }
    };

    const goAdd = () => {
        history.push({
            pathname: `/openSourceComponent/addReview/add`,
        });
    };
  ///////开始//////////////
  const linkList = [
    {
      title: '开源组件审阅'
    }
  ]
   // 表格列配置
   const columns = [
    {
        title: '应用系统名称',
        dataIndex: 'applicationSystemName'
    },
    {
        title: '应用系统英文名称',
        dataIndex: 'applicationSystemEnglishName',
    },

    {
        title: '审阅状态',
        dataIndex: 'reviewStatus',
        render: (text) => {
            return text == '未审阅' ? (
                <span style={{ color: '#bfbfbf' }}>
                    {' '}
                    <Space>
                        <Badge color="#bfbfbf" />
                        <span>{text}</span>{' '}
                    </Space>
                </span>
            ) : text == '已审阅' ? (
                <span style={{ color: '#047C1C' }}>
                    <Space>
                        <Badge color="#047C1C" />
                        <span>{text}</span>{' '}
                    </Space>
                </span>
            ) : text === '部分审阅' ? (
                <span style={{ color: '#008DDC' }}>
                    <Space>
                        <Badge color="#008DDC" />
                        <span>{text}</span>{' '}
                    </Space>
                </span>
            ) : (
                <span>{text}</span>
            );
        },
    },
    {
        title: '审阅时间',
        dataIndex: 'reviewTime',
    },
    {
        title: '审阅人',
        dataIndex: 'reviewer',
    },
    {
        title: '风险等级',
        dataIndex: 'riskLevel',
    },

    {
        title: '操作',
        dataIndex: 'operation',
        render: (_, record) => {
            return (
                <Space>
                    <Button
                        type="text"
                        size="small"
                        style={{ color: '#008cd6' }}
                        onClick={() => {
                            // const id=record.applicationSystemId;
                            history.push(
                                `/openSourceComponent/addReview/${record.applicationSystemId}`,
                                { 
                                  
                                    appName: record.applicationSystemName
                                   
                                  }
                            );
                        }}
                    >
                        查看
                    </Button>
                </Space>
            );
        },
    },
];

  return (
    <div className="openSourceComponent ">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div  className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header"  style={{marginBottom:'0px'}}>
          <div className="middleware-base-info">
          <Row>
            <div className='flex-app' style={{marginBottom:'25px'}}>
                <div className='header-icon'></div>
                <div>开源组件审阅列表</div>
            </div>
          </Row>
            <Row>
                <Col span={24} style={{display:'flex',justifyContent:'space-between'}}>
                    <Button
                        type="primary"
                        icon={
                            <PlusOutlined
                                style={{
                                    verticalAlign: 'middle',
                                    fontSize: '14px',
                                    marginBottom: '4px',
                                }}
                            />
                        }
                        onClick={() => {
                            goAdd();
                        }}
                    >
                        新增审阅
                    </Button>
                    <div className='mySubscriptions-search'>
                    <Space>
                        <Search
                        placeholder="搜索应用系统"
                        onSearch={onSearch}
                        style={{ width: 250}}
                        allowClear
                        />
                        <Button
                            type="primary"
                            onClick={() => {
                                downloadResponse();
                            }}
                        >
                            导出
                        </Button>
                    </Space>
                       
                    </div>
                </Col>
                
            </Row>
          
          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section" style={{marginTop:'0px'}}>
            <Table
                size="small"
                bordered={false}
                scroll={{
                    x: 1000, // 设置最小表格宽度，触发横向滚动
                }}
                columns={columns}
                dataSource={dataSource}
                rowClassName="editable-row"
                pagination={{
                    showSizeChanger: true,
                    showQuickJumper: true,
                    showTotal: (total, range) => `共 ${total} 条`,
                    pageSizeOptions: ['5', '10', '15', '20'],
                    defaultCurrent: 1,
                }}
                loading={loading}
            />
        </div>
      </div>
  
     
    </div>
  );
};



export default ReviewList;