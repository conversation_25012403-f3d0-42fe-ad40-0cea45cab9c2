import React, { useState, useCallback, useEffect } from 'react';
import { Spin, Alert } from 'antd';
import { useSearchParams } from 'umi';
// 统一加载外链的Iframe组件
const CommonIframe = ({
  onLoad,
  onError,
  ...props 
}) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchParams] = useSearchParams()
  const iframeSrc = searchParams?.get('rowPath') ?? ''
  // 处理iframe加载完成
  const handleLoad = useCallback((e) => {
    setLoading(false);
    setError(null);
    if (onLoad) onLoad(e);
  }, [onLoad]);

  // 处理iframe加载错误
  const handleError = useCallback((e) => {
    setLoading(false);
    setError('无法加载内容，请检查链接或网络连接');
    if (onError) onError(e);
  }, [onError]);

  const [iframeHeight, setIframeHeight] = useState(
    document.getElementById("sub-app-box")?.clientHeight
  );

  useEffect(() => {
    let observer;
    const dom = document.getElementById("sub-app-box");

    if (!dom) return;
    try {
      observer = new ResizeObserver((entries) => {
        const { height } = entries[0].contentRect;
        setIframeHeight(height);
        // 执行响应逻辑
      });
      observer.observe(dom);
    } catch (error) {}
    return () => {
      observer?.disconnect();
    };
  }, []);
  return (
    <div className='common-iframe-container' style={{ position: 'relative', width: '100%', height: '100%' }}>
      {loading && (
        <div style={{
          position: 'absolute',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          backgroundColor: 'rgba(255, 255, 255, 0.7)',
          zIndex: 10
        }}>
          <Spin
            size="large"
          />
        </div>
      )}
      
      {error ? (
        <div style={{ height: '100%', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
          <Alert
            message="加载错误"
            description={error}
            type="error"
            showIcon
            style={{ width: '80%' }}
          />
        </div>
      ) : (
        <iframe
          src={iframeSrc}
          width='100%'
          height={iframeHeight || '100%'}
          onLoad={handleLoad}
          onError={handleError}
          {...props}
        />
      )}
    </div>
  );
};

export default CommonIframe;