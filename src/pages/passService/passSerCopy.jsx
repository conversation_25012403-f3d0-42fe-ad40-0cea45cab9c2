import React, { useState, useEffect } from 'react';
import { Input, Button, Space, Select, Tooltip, Affix } from 'antd';
import { SearchOutlined, CopyOutlined } from '@ant-design/icons';
import { history } from 'umi';
import './passSer.less';
import { getMiddlewareAppList } from '@/services';
import { passServiceList } from './mockdata';
import { API_MIDDLEWARE, CONFIG_MIDDLEWARE } from '@/services/middleware';
const PassSer = () => {
  const [selectedKey, setSelectedKey] = useState('all');
  const [appList, setAppList] = useState([]);
  const [filteredApiList, setFilteredApiList] = useState([]);
  const [navItems, setNavItems] = useState([
    {
      title: '全部',
      key: 'all',
      count: 0
    },
    {
      title: '数据库',
      key: 'database',
      count: 0
    },
    {
      title: '消息列队',
      key: 'message',
      count: 0
    },
    {
      title: '微服务治理',
      key: 'microservice',
      count: 0
    },
    {
      title: '其他',
      key: 'others',
      count: 0
    }
  ]);
  const [searchKeyword, setSearchKeyword] = useState('');

  const getNavItems = (passServiceList) => {
    // 计算总数
    const totalCount = passServiceList.reduce((sum, item) => sum + item.middlewareInfoDTOS.length, 0);
    // 生成导航项
    const navItems = [
      {
        title: '全部',
        key: 'all',
        count: totalCount
      },
      ...passServiceList.map(item => ({
        title: item.groupName,
        key: item.groupCode,
        count: item.middlewareInfoDTOS.length
      }))
    ];

    return navItems;
  };

  // 获取中间件应用列表
  useEffect(() => {
    getMiddlewareAppList({ clusterId: CONFIG_MIDDLEWARE.clusterId, filterUninstalled: false, keyword: '' }).then(res => {
      setNavItems(getNavItems(res));
      setAppList(res);
      setFilteredApiList(res.flatMap(item => item.middlewareInfoDTOS));
    });

    // // 先用 passServiceList 替代res，接口太慢
    // setNavItems(getNavItems(passServiceList));
    // setAppList(passServiceList);
    // setFilteredApiList(passServiceList.flatMap(item => item.middlewareInfoDTOS));
  }, []);

  // 自定义标题渲染
  const titleRender = (nodeData) => {
    const count = getNodeCount(nodeData);
    return (
      <div className="tree-node-content">
        <span className="node-title">{nodeData?.title}</span>
        {count && <span className="node-count">{count}</span>}
      </div>
    );
  };

  // 处理卡片点击事件，跳转到详情页面
  const handleCardClick = (api) => {
    history.push(`/passService/detail/${ api.name }/${ api.id }/${api.chartName}`, { api });
  };

  // 处理导航项点击
  const handleNavItemClick = (key) => {
    setSelectedKey(key);
    // 根据选中的key过滤apiList
    let apis = [];
    if (key === 'all') {
      apis = appList.flatMap(item => item.middlewareInfoDTOS);
    } else {
      const filtered = appList.filter(item => item.groupCode === key);
      apis = filtered.flatMap(item => item.middlewareInfoDTOS);
    }
    // 如果搜索框有值，进一步过滤
    if (searchKeyword && searchKeyword.trim()) {
      apis = apis.filter(api =>
        (api.chartName && api.chartName.toLowerCase().includes(searchKeyword.toLowerCase())) 
      );
    }
    setFilteredApiList(apis);
  };
  // 处理搜索输入
  const handleSearch = (e) => {
    const keyword = e.target.value;
    setSearchKeyword(keyword);
    // 如果当前选中的是 'all'
    if (selectedKey === 'all') {
      const filtered = appList.flatMap(item => 
        item.middlewareInfoDTOS.filter(api => 
          api.chartName.toLowerCase().includes(keyword.toLowerCase()) 
        )
      );
      setFilteredApiList(filtered);
    } else {
      // 如果选中了特定分类
      const filtered = appList
        .filter(item => item.groupCode === selectedKey)
        .flatMap(item => 
          item.middlewareInfoDTOS.filter(api =>
            api.chartName.toLowerCase().includes(keyword.toLowerCase()) 
          )
        );
      setFilteredApiList(filtered);
    }
  };

  return (
    <div className="pass-ser dealPadding">
      <div className="left-sidebar">
        <Affix offsetTop={22}>
        <div className="navigation">
          <div className="navigation-title">
            <span className="nav-indicator"></span>
            <h3>导航</h3>
          </div>
          <div className="nav-menu">
            {navItems.map((item) => (
              <div
                key={item.key}
                className={`nav-item ${selectedKey === item.key ? 'active' : ''}`}
                onClick={() => handleNavItemClick(item.key)}
              >
                <span className="nav-item-title">{item.title}</span>
                <span className="nav-item-count">({item.count})</span>
              </div>
            ))}
          </div>
        </div>
        </Affix>
      </div>

      <div className="main-content">
        <div className="search-bar">
          <div className="search-title">全部({appList.reduce((sum, item) => sum + item.middlewareInfoDTOS.length, 0)})</div>
          <div className="search-filters">
            <div className="search-inputs">
              <Input
                placeholder="请输入Pass服务名称"
                value={searchKeyword}
                onChange={handleSearch}
              />
            </div>
          </div>
        </div>

        <div className="api-list">
          {filteredApiList.map((api) => (
            <div
              key={api.id}
              className="api-card"
              onClick={() => handleCardClick(api)}
              style={{ cursor: 'pointer' }}
            >
              <div className="card-header">
                <div className="header-content">
                  <div className="api-logo">
                    <img src={ `${API_MIDDLEWARE}/images/middleware/${api.imagePath}`} alt={api.imagePath} />
                  </div>
                  <div>
                    <h4 className="api-name">{api.name}</h4>
                    <div className="api-tags">
                      <span className="database-tag">{navItems.filter(item => item.key === api.type)[0]?.title}</span>
                      <span className="version-tag">{api.chartVersion}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="card-info">
                <div className="info-item">
                  <span className="label">供应商：</span>
                  <span className="value">{api.vendor}</span>
                </div>
                <div className="info-item">
                  <span className="label">介绍：</span>
                  <Tooltip title={api.description} placement="bottom">
                    <span className="value">{api.description}</span>
                  </Tooltip>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PassSer; 