import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import './index.less';
import { CONFIG_MIDDLEWARE } from '@/services';
import { Table } from 'antd';

const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

const TestComponent = () => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({
    id: 22,
    name: "postgresql",
    aliasName: "PostgreSQL",
    description: "CloudDB PostgreSQL数据库",
    type: "db",
    version: "16.4,16.1,14.13,14.7,14.2,13.8,13.6,13.5,12.10,11.15",
    image: null,
    imagePath: "postgresql-2.5.1.svg",
    status: null,
    createTime: "2025-01-14 11:30:37",
    versionStatus: "now",
    chartName: "postgresql",
    chartVersion: "2.5.1",
    grafanaId: null,
    replicas: null,
    replicasStatus: null,
    middlewares: null,
    official: true,
    vendor: "harmonycloud",
    enable: null,
    withMiddleware: null
  });
  const [middlewareMeta, setMiddlewareMeta] = useState({
    "appName": "PostgreSQL",
    "categoryName": "db",
    "vendor": "谐云科技",
    "usageCount": 49,
    "owner": null,
    "lastUpdateTime": "2025-01-14 11:30:37",
    "description": "CloudDB PostgreSQL数据库",
    "questionYaml": null
});

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState(
    '<packaging>pom</packaging><organization><name>harmony cloud</name><url>https://www.harmonycloud.cn/</url></organization>');
  const [fileTree, setFileTree] = useState({});
  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
const data = [
  { title: '参数名', description: 'Description 1' },
  { title: '参数值', description: 'Description 2' },
  // 更多数据...
];
const data1 = [
  { title: '参数名', description: 'Description 1' },
  { title: '参数值', description: 'Description 2' },
  { title: '参数值', description: 'Description 2' },
  // 更多数据...
];
  return (
    <div className="middleware-detail">
      <div className="info-section" style={{marginTop:'0px'}}>
        <p className='title-step'>在线文档地址：<a href='http://fileul.trinasolar.com/swagger-ui.html' target='_black'>http://fileul.trinasolar.com/swagger-ui.html</a> </p>
        <div className="file-content" >
          <div className='file-pic'></div>
          <p className='title-step'>登录接口</p>
          <CodeMirror
          value={'https://fileul.trinasolar.com/user?clientId=[client_id]&clientSecret=[client_secret]'}
          theme="none"
          extensions={[javascript(), yaml()]}
          editable={false}
          readOnly={true}
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            highlightActiveLine: true,
            dropCursor: true,
            allowMultipleSelections: true,
            indentOnInput: true,
            
          }}
          style={{ overflow: 'auto' }}
        />
        <p className='title-step'>返回参数</p>
        <CodeMirror
        value={`"bizcode": 10000,\n"bizmsg": "SUCCESS",\n"data": {\n  "token": "821f3157e1a3456bfe1a000a1adf0862NB2EdeVL"\n},\n"sign": null,\n"timestamp": 0,\n"postToken": "d80fe4f87eed48f0b81d2127102a41bc"`}
        theme="none"
        extensions={[javascript(), yaml()]}
        editable={false}
        readOnly={true}
        basicSetup={{
          lineNumbers: true,
          foldGutter: true,
          highlightActiveLine: true,
          dropCursor: true,
          allowMultipleSelections: true,
          indentOnInput: true,
          
        }}
        style={{ overflow: 'auto' }}
      />
        
       
        </div>
       
      </div>
 
     
    </div>
  );
};

export default TestComponent; 