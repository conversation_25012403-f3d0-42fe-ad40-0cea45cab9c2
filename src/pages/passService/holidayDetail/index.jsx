
import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import ParamsSetDrawer from '../passParamsSet';
import TestComponent from '../testEnv';
import PrdComponent from '../prdEnv';
import ApiInfo from '../apiInfo';
import './index.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Tabs } from 'antd';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

const HolidayComponentDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};
  const handleBack = () => {
    history.back();
  };
  const [info, setInfo] = useState({
    type1:[{
        title:'查询指定日期是否为节假日(get请求)',
        path:'/kepler/share/api/holiday/check/2025-07-28',
        headerData:[{name:'Content-type',value:'application/json'}],
        paramData:[],
        result:'{}',
        responseData:[],
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
      },
      {
        title:'查询指定时间段的所有节假日(get请求)',
        path:'/kepler/share/api/holiday/range',
        headerData:[{name:'Content-type',value:'application/json'}],
        paramData:[{name:'startDate',type:'string',position:'query',sub:'none'},{name:'endDate',type:'string',position:'query',sub:'none'}],
        result:'{ "code": 0,"msg": null,"data": [{"id": "string","delFlag": "string","createTime": "string","updateTime": "string","createBy": null,"updateBy": null,"date": "string","year": "string","nameCn": "string","nameEn": null,"countryCode": "string","type": "string"}]}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
      {
        title:'指定年份的所有节假日(get请求)',
        path:'/kepler/share/api/holiday/year/2025',
        headerData:[{name:'Content-type',value:'application/json'}],
        paramData:[],
        result:'{"code": 0,"msg": null,"data": [{"id": "string","delFlag": "string","createTime": "string","updateTime": "string","createBy": null,"updateBy": null,"date": "string","year": "string","nameCn": "string","nameEn": null,"countryCode": "string","type": "string"}]}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
      {
        title:'指定日期的节假日名称(get请求)',
        path:'/kepler/share/api/holiday/name/2025-10-01',
        headerData:[{name:'Content-type',value:'application/json'}],
        paramData:[],
        result:'{"code": 0,"msg": null,"data": "string"}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
      {
        title:'批量查询多个日期的节假日(post请求)',
        path:'/kepler/share/api/holiday/batch',
        headerData:[{name:'Content-type',value:'application/json'}],
        paramData:[{name:'dates',type:'Object',position:'body',sub:'none'}],
        result:'{ "code": 0,"msg": null,"data": [true]}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
      {
        title:'时间段内放假的天数(get请求)',
        path:'/kepler/share/api/holiday/count',
        headerData:[{name:'Content-type',value:'application/json'}],
        paramData:[{name:'startDate',type:'string',position:'query'},{name:'endDate',type:'string',position:'query'}],
        result:'{ "code": 0,"msg": null,"data": 0}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
    ],
    type2:[
        {
            title:'判断指定日期是否为工作日(get请求)',
            path:'/kepler/share/api/workday/check/2025-10-01',
            headerData:[{name:'Content-type',value:'application/json'}],
            paramData:[],
            result:'{ "code": 0,"msg": null,"data": true}',
            statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
            {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
           
          },
          {
            title:'计算两个日期之间的工作日天数(get请求)',
            path:'/kepler/share/api/workday/count',
            headerData:[{name:'Content-type',value:'application/json'}],
            paramData:[{name:'startDate',type:'string',position:'query'},{name:'endDate',type:'string',position:'query'}],
            result:'{ "code": 0,"msg": null,"data": 0}',
            statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
            {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
           
          },
          {
            title:'获取指定日期后的下一个工作日(get请求)',
            path:'/kepler/share/api/workday/next/2025-01-25',
            headerData:[{name:'Content-type',value:'application/json'}],
            paramData:[],
            result:'{ "code": 0,"msg": null,"data": true}',
            statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
            {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
           
          },
          {
            title:'获取指定日期前的上一个工作日(get请求)',
            path:'/kepler/share/api/workday/prev/2025-07-28',
            headerData:[{name:'Content-type',value:'application/json'}],
            paramData:[{name:'startDate',type:'string',position:'query'},{name:'endDate',type:'string',position:'query'}],
            result:'{ "code": 0,"msg": null,"data": "string"}',
            statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
            {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
           
          },
    ]
  })

  const linkList = [
    {
      href: '/#/productService',
      title: '产品与服务'
    },
    {
     
      title: `节假日组件详情`
    },
  ]
  

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding">
        <BreadcrumbNav list={linkList} isNeedBack/>
       
      </div>
      <div className="info-section dealPadding">
        <h3 className="section-title">基础信息</h3>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">名称：</span>
            <span className="value">节假日查询组件</span>
          </div>
          <div className="info-item">
            <span className="label">版本：</span>
            <span className="value">1.0.0</span>
          </div>
        
          <div className="info-item">
            <span className="label">创建人：</span>
            <span className="value">harmonycloud</span>
          </div>
          <div className="info-item">
            <span className="label">创建时间：</span>
            <span className="value">2025-08-04 2:50:00</span>
          </div>
          <div className="info-item">
            <span className="label">修改人：</span>
            <span className="value">harmonycloud</span>
          </div>
          <div className="info-item">
            <span className="label">修改时间：</span>
            <span className="value">2025-08-04 2:50:00</span>
          </div>
          <div className="info-item">
            <span className="label">描述：</span>
            <span className="value">节假日查询</span>
          </div>
        </div>
      </div>
      <div className="info-section dealPadding" style={{marginTop:'0px'}}>
        <h3 className="section-title">使用说明</h3>
          <div className="file-content" >
            <Tabs
                defaultActiveKey="1"
                items={[
                    {
                    label: `节假日查询`,
                    key: '1',
                    children: <ApiInfo infos={info.type1} />,
                    },
                    {
                    label: `工作日查询`,
                    key: '2',
                    children: <ApiInfo infos={info.type2} />,
                    }
                ]}
                />
          </div>
      </div>
     
    </div>
  );
};

export default HolidayComponentDetail; 