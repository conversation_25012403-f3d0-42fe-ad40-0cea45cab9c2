export const dataSource = [
  {
    jkmc: "/api/scf/token",
    jkms: "通过授权码从 TAM 远程获取令牌",
    qqfs: "GET",
    qqcs: "query string code 授权码",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/userinfo",
    jkms: "获取用户信息",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/logout",
    jkms: "通过token从TAM 远程注销",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/refresh",
    jkms: "刷新token和权限缓存",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
];
export const columns = [
  {
    title: "接口名称",
    dataIndex: "jkmc",
    key: "jkmc",
  },
  {
    title: "接口描述",
    dataIndex: "jkms",
    key: "jkms",
  },
  {
    title: "请求方式",
    dataIndex: "qqfs",
    key: "qqfs",
  },
  {
    title: "请求参数",
    dataIndex: "qqcs",
    key: "qqcs",
  },
  {
    title: "返回参数",
    dataIndex: "fhcs",
    key: "fhcs",
  },
  {
    title: "备注",
    dataIndex: "bz",
    key: "bz",
  },
];
export const resCardList = [
  {
    title: [" 1. 下载安装插件，记录插件安装路径。", " 2. 设置环境变量。"],
    content: `AOE_CONFIG_PATH={#插件安装路径}
LD_LIBRARY_PATH=/opt/casb/CipherSuiteSdk_linux/lib`,
  },
  {
    title: "3. 修改数据源配置：驱动名称、连接地址。",
    content: `# spring boot
spring:
  #数据源
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置
    druid:
      mysql:
        # 1. 修改driver类名
        driver-class-name: com.ciphergateway.aoe.plugin.engine.AOEDriver
        # 2. 修改连接url，增加aoe
        url: *****************************************`,
  },
  {
    title: "4. 修改启动命令：追加 -Djava.ext.dirs 参数。",
    content: `java -Djava.ext.dirs={#插件安装路径}:$JAVA_HOME/jre/lib/ext:$JAVA_HOME/lib/ext -
jar app.jar`,
  },
];
export const resCardDevList = [
  {
    title: [" 1. 下载安装插件，记录插件安装路径。", " 2. 设置环境变量。"],
    content: `AOE_CONFIG_PATH={#插件安装路径}
LD_LIBRARY_PATH=/opt/casb/CipherSuiteSdk_linux/lib`,
  },
  {
    title:
      "3. 解压插件安装文件，将插件jar包导入本地maven仓库（或者远程仓库）。",
    content: `mvn install:install-file -Dfile=./aoe-plugin-cg-*******.jar -
DgroupId=com.ciphergateway -DartifactId=aoe-plugin -Dversion=******* -
Dpackaging=jar`,
  },
  {
    title: "4. 添加mven依赖。",
    content: `<dependency>
    <groupId>com.ciphergateway</groupId>
    <artifactId>aoe-plugin</artifactId>
    <version>*******</version>
</dependency>`,
  },
  {
    title: "5. 修改数据源配置：驱动名称、连接地址。",
    content: `# spring boot
  spring:
    #数据源
    datasource:
      type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置
    druid:
      mysql:
        # 1. 修改driver类名
        driver-class-name: com.ciphergateway.aoe.plugin.engine.AOEDriver
        # 2. 修改连接url，增加aoe
        url: *****************************************`,
  },
];
export const detailForm = [
  { key: "1", label: "名称:", value: "数据脱敏服务" },
  { key: "1", label: "版本:", value: "1.0.0" },
  { key: "1", label: "创建人:", value: "张驰" },
  { key: "1", label: "创建时间:", value: "2025-02-27 02:50:00" },
  { key: "1", label: "修改人:", value: "张驰" },
  { key: "1", label: "修改时间:", value: "2025-03-27 02:50:00" },
  // { key: "1", label: "文档链接:", value: "-" },
  {
    key: "1",
    label: "描述:",
    style: { "grid-column": "span 2" },
    value: `基于数据分类分级结果落地数据安全防护能力，对大数据敏感报表、业务系统前端展示、生产数据导入测试环境
等场景进行数据脱敏防护，收敛风险暴露面`,
  },
];
