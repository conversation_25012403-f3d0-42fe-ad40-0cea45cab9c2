import React, { useEffect, useRef, useState } from "react";
import { Input, Button, Affix, Select, Checkbox } from "antd";
import "./index.less";
import "./products.less";
import { history } from "umi";
const MockAllData = [
  {
    groupId: "prod-group-0",
    group: "常用产品",
    children: [
      {
        name: "应用管理",
      },
      {
        name: "文档库",
      },
      {
        name: "API市场",
        path: "/passService?type=api",
      },
      {
        name: "组件市场",
        path: "/passService?type=component",
      },
      {
        name: "脚手架",
      },
      {
        name: "中间件",
        path: "/passService",
      },
    ],
  },
  {
    groupId: "prod-group-1",
    group: "虚拟机",
    children: [
      {
        name: "虚拟机申请",
      },
      {
        name: "虚拟机变更",
      },
      {
        name: "虚拟机释放",
      },
    ],
  },
  {
    groupId: "prod-group-2",
    group: "容器",
    children: [
      {
        name: "集群",
      },
      {
        name: "主机",
      },
      {
        name: "网络",
      },
      {
        name: "工作空间",
      },
      {
        name: "ingress",
      },
      {
        name: "Service",
      },
    ],
  },
  {
    groupId: "prod-group-3",
    group: "知识库",
    children: [
      {
        name: "公共文档",
      },
      {
        name: "项目文档",
      },
      {
        name: "组件文档",
      },
    ],
  },
  {
    groupId: "prod-group-4",
    group: "存储",
    children: [
      {
        name: "对象存储",
      },
      {
        name: "块存储",
      },
    ],
  },
  {
    groupId: "prod-group-5",
    group: "数据库",
    children: [
      {
        name: "Mysql",
      },
      {
        name: "Postgresql",
      },
    ],
  },
  {
    groupId: "prod-group-6",
    group: "域名",
    children: [],
  },
  {
    groupId: "prod-group-7",
    group: "PaaS服务",
    children: [
      {
        name: "API市场",
        path: "/passService?type=api",
      },
      {
        name: "组件市场",
        path: "/passService?type=component",
      },
      {
        name: "中间件",
        path: "/passService",
      },
    ],
  },
  {
    groupId: "prod-group-8",
    group: "人工智能",
    children: [
      {
        name: "视觉智能",
      },
      {
        name: "模型服务",
      },
      {
        name: "决策智能",
      },
    ],
  },
  {
    groupId: "prod-group-9",
    group: "开源组件",
    children: [],
  },
];

const Products = () => {
  const allData = MockAllData;
  const [curData, setCurData] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [selectedGroup, setGroup] = useState([]);
  const [showSearch, setSearch] = useState(false);
  // const curEle = useRef();
  const handleData = () => {
    let groupData = selectedGroup.length
      ? allData.filter((g) => selectedGroup.includes(g.groupId))
      : allData;

    const data = searchValue
      ? JSON.parse(JSON.stringify(groupData)).filter((i) => {
          let flag = false;
          if (i.group.includes(searchValue)) {
            flag = true;
          } else {
            i.children = i.children.filter((c) => c.name.includes(searchValue));
            flag = !!i.children.length;
          }
          return flag;
        })
      : groupData;

    setCurData(data);
  };

  useEffect(() => {
    handleData();
  }, [allData, searchValue, selectedGroup]);

  // useEffect(() => {

  //   if(curEle.current) {
  //     const ele = document.getElementById(curEle.current);
  //     curEle.current = null;
  //     ele?.scrollIntoView({block:'center', behavior:'smooth'})
  //   }
  // }, [curData]);

  // const onClickGroup = (e) => {
  //   const {checked, value}=e.target ||{};
  //   if(checked) {
  //     curEle.current = value;
  //   }
  // };

  const onClickCard = (card) => {
    if (card.path) {
      document.body.scrollTo(0, 0);
      history.push(card.path);
    }
  };
  return (
    <div className="products-wrapper dealPadding">
      <div className="left-sidebar">
        <Affix offsetTop={22}>
          <div className="group-name item-padding">全部产品</div>
          {!showSearch && (
            <div className="search-bar">
              <Input.Search
                onFocus={() => setSearch(true)}
                onSearch={() => setSearch(true)}
                placeholder="搜索全部产品"
                bordered={false}
                className="search"
                allowClear
              />
            </div>
          )}
          <div className="item-padding nav-filter">类目筛选</div>
          <div className="item-padding">
            <Button
              onClick={() => setGroup([])}
              type="link"
              block
              className="nav-btn"
            >
              清除筛选
            </Button>
          </div>
          <div>
            <div className="item-padding">产品类别</div>
            <Checkbox.Group
              className="nav-list"
              value={selectedGroup}
              onChange={setGroup}
            >
              {allData.map((g) => {
                const groupName = g.group;
                const key = g.groupId;
                return (
                  <Checkbox
                    key={key}
                    className="item-padding nav-item"
                    // onChange={onClickGroup}
                    value={key}
                  >
                    {groupName}
                  </Checkbox>
                );
              })}
            </Checkbox.Group>
          </div>
        </Affix>
      </div>

      <div className="main-content">
        <Affix offsetTop={0}>
          <div className={`search-bar ${showSearch ? "" : "hide-search-bar"}`}>
            {showSearch && (
            <>
              <Input.Search
                placeholder="回车搜索全部产品"
                bordered={false}
                className="search"
                autoFocus
                allowClear
                onChange={(e) => {
                  const v = e.target.value;
                  if (!v) setSearchValue("");
                }}
                onSearch={(v) => setSearchValue(v?.trim?.())}
                onBlur={() => {
                  if (!searchValue?.trim()) setSearch(false);
                }}
              />
              {!!searchValue && (
                <div className="search-tip">
                  当前搜索关键词: {searchValue}
                </div>
              )}
            </>
            )}
          </div>
        </Affix>

        <div className={showSearch ? "" : "no-search-bar"}>
          {curData.map((g) => {
            const groupName = g.group;
            const key = g.groupId;
            return (
              <div key={key}>
                <div className="group-name" id={key}>
                  {groupName}
                </div>
                <div className="prod-list">
                  {g.children?.map((card) => {
                    return (
                      <div
                        onClick={() => onClickCard(card)}
                        className={`prod-card ${
                          card.path ? "prod-card-link" : ""
                        }`}
                        key={key + card.name}
                      >
                        <div className="prod-card-title">{card.name}</div>
                        <div className="prod-card-desc">
                          通过标准化封装、分类展示，提供可复用的功能模块，支持在线预览、版本管理、一键部署、权限控制、使用数据分析等功能
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

const ProductService = () => {
  const [selectedFeature, setSelectedFeature] = useState(
    "提供有竞争力的高质量产品，将虚拟机、容器、存贮、数据库、域名、PaaS服务、开源组件等产品技术与场景深度融合，为开发者打造安全、稳定、体验卓越的云服务。"
  );
  const [selectedTitle, setSelectedTitle] = useState("天合产品");

  return (
    <>
      <div className="product-service">
        <div className="content-wrapper">
          {/* 页面标题 */}
          <div className="home-header dealPadding">
            <div className="header-content">
              <h1>{selectedTitle}</h1>
              <div className="description">{selectedFeature}</div>
            </div>
          </div>
          <Products />
        </div>
      </div>
    </>
  );
};

export default ProductService;
