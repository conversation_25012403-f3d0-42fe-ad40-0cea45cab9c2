import React, { useEffect, useMemo, useState } from "react";
import { useParams, history } from "umi";
import { LeftOutlined } from "@ant-design/icons";
import CodeMirror from "@uiw/react-codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { yaml } from "@codemirror/lang-yaml";
import BreadcrumbNav from "@/components/BreadcrumbNav/index";
import ParamsSetDrawer from "../passParamsSet";
import "./index.less";
import { CONFIG_MIDDLEWARE } from "@/services";
import { Card, Space, Spin, Table, Tabs, Typography, notification } from "antd";
import {
  dataSource,
  columns,
  detailForm,
  resCardList,
  resCardDevList,
} from "./type";
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId;

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle",
};

// 工具函数：无数据时显示 /
const displayValue = (value) => {
  return value === null || value === undefined || value === "" ? "/" : value;
};

const DataDesensitization = () => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { chartName, type, id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({});

  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    return () => {
      notification.destroy("apply_success_to_jump");
    };
  }, []);
  const handleBack = () => {
    history.back();
  };

  const getDetail = useMemo(() => {
    const res = detailForm.map((item) => {
      return (
        <div
          className="info-item"
          key={item.label}
          style={item.style ? item.style : {}}
        >
          <span className="label">{item.label}</span>
          <span className="value">{displayValue(item.value)}</span>
        </div>
      );
    });
    return res;
  }, [type, middlewareInfo]);

  const getContent = (item) => {
    if (item.children) {
      return item.children.map((child) => getContent(child));
    }
    if (item.title === "基本使用") {
      return <></>;
    } else {
      return (
        <>
          {item.tip && (
            <p style={{ margin: "8px 0", color: "#a8a8a8" }}>{item.tip}</p>
          )}
          <CodeMirror
            value={item.content}
            theme="light"
            extensions={[javascript(), yaml()]}
            editable={false}
            readOnly={true}
            basicSetup={{
              lineNumbers: true,
              foldGutter: true,
              highlightActiveLine: true,
              dropCursor: true,
              allowMultipleSelections: true,
              indentOnInput: true,
            }}
            style={{ overflow: "auto" }}
          />
        </>
      );
    }
  };
  const getTitles = (item) => {
    if (item.title) {
      if (item.title instanceof Array) {
        return item.title.map((title) => {
          return (
            <>
              <Typography.Text strong>{title}</Typography.Text> <br />
            </>
          );
        });
      } else {
        return <Typography.Text strong>{item.title}</Typography.Text>;
      }
    }
  };
  const getCardContent = (contentList = resCardList) => {
    return (
      <Space direction="vertical" size="middle" style={{ display: "flex" }}>
        {contentList.map((item) => {
          return (
            <Card>
              {getTitles(item)}
              {getContent(item)}
            </Card>
          );
        })}
      </Space>
    );
  };
  const linkList = [
    {
      href: "/#/productService",
      title: "产品与服务",
    },
    {
      href: `/#/passService/internationalization/${type}/${id}/${chartName}`,
      title: `${type}详情`,
    },
  ];

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding nav-path-ui">
        <BreadcrumbNav list={linkList}></BreadcrumbNav>
        <div
          className="back-button"
          style={{ width: "60px" }}
          onClick={handleBack}
        >
          <LeftOutlined />
          <span>返回</span>
        </div>
      </div>
      <Spin spinning={!!loading}>
        {/* 基本信息区域 */}
        <div className="info-section dealPadding">
          <h3 className="section-title">基本信息</h3>
          <div className="info-grid">{getDetail}</div>
        </div>
      </Spin>
      {/* 基本信息区域 */}
      <div className="info-section dealPadding" style={{ marginTop: "0" }}>
        <div className="file-content">
          <h3 className="section-title">快速开始</h3>
          {getCardContent(resCardDevList)}
        </div>
      </div>
    </div>
  );
};

export default DataDesensitization;
