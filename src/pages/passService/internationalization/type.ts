export const dataSource = [
  {
    jkmc: "/api/scf/token",
    jkms: "通过授权码从 TAM 远程获取令牌",
    qqfs: "GET",
    qqcs: "query string code 授权码",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/userinfo",
    jkms: "获取用户信息",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/logout",
    jkms: "通过token从TAM 远程注销",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/refresh",
    jkms: "刷新token和权限缓存",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
];
export const columns = [
  {
    title: "接口名称",
    dataIndex: "jkmc",
    key: "jkmc",
  },
  {
    title: "接口描述",
    dataIndex: "jkms",
    key: "jkms",
  },
  {
    title: "请求方式",
    dataIndex: "qqfs",
    key: "qqfs",
  },
  {
    title: "请求参数",
    dataIndex: "qqcs",
    key: "qqcs",
  },
  {
    title: "返回参数",
    dataIndex: "fhcs",
    key: "fhcs",
  },
  {
    title: "备注",
    dataIndex: "bz",
    key: "bz",
  },
];
export const resCardList = [
  {
    title: [" 1. 下载安装插件，记录插件安装路径。", " 2. 设置环境变量。"],
    content: `AOE_CONFIG_PATH={#插件安装路径}
LD_LIBRARY_PATH=/opt/casb/CipherSuiteSdk_linux/lib`,
  },
  {
    title: "3. 修改数据源配置：驱动名称、连接地址。",
    content: `# spring boot
spring:
  #数据源
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    #连接池配置
    druid:
      mysql:
        # 1. 修改driver类名
        driver-class-name: com.ciphergateway.aoe.plugin.engine.AOEDriver
        # 2. 修改连接url，增加aoe
        url: *****************************************`,
  },
  {
    title: "4. 修改启动命令：追加 -Djava.ext.dirs 参数。",
    content: `java -Djava.ext.dirs={#插件安装路径}:$JAVA_HOME/jre/lib/ext:$JAVA_HOME/lib/ext -
jar app.jar`,
  },
];
export const resCardDevList = [
  {
    title: ["1.1 引入依赖"],
    tip: "在项目的 `pom.xml` 中添加 trina-i18n-starter 依赖：",
    content: `<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-i18n-starter</artifactId>
    <version>4.0.4</version>
</dependency>`,
  },
  {
    title: ["1.2 基础配置"],
    children: [
      {
        tip: "该脚手架支持优先使用配置文件再使用远程国际化配置，如果需要在项目中使用国际化功能，请按照以下步骤进行配置；在 `application.yml` 中添加国际化配置：",
        content: `spring:
  messages:
    # 支持通配符模式，可以匹配多个资源文件
    basename:
      - validation
trina:
  i18n:
    url: http://localhost:9091/api/i18n/msg/info`,
      },
      {
        tip: "在 `resources` 目录下添加国际化资源文件：",
        content: `validation.properties
validation_en_US.properties
validation_zh_CN.properties`,
      },
    ],
  },
  {
    title: ["2.1 核心功能-工具类方式"],
    children: [
      {
        tip: "使用 `I18nUtils` 工具类获取国际化消息：",
        content: `java
import com.trinasolar.i18n.utils.I18nUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MessageController {

    @GetMapping("/message")
    public String getMessage() {
        // 使用当前线程的语言环境
        String message = I18nUtils.getMessage("user.name.required");
        
        // 指定默认消息
        String messageWithDefault = I18nUtils.getMessage("user.email.invalid", "邮箱格式不正确");
        
        // 带参数的消息
        String messageWithArgs = I18nUtils.getMessage("user.age.range", new Object[]{18, 65});
        
        return message;
    }
}
`,
      },
    ],
  },
  {
    title: ["2.2 注解式国际化"],
    children: [
      {
        tip: "使用注解自动处理响应数据的国际化：",
        content: `java
@Data
@I18nClass
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MenuVO {

    /**
     * 菜单标题，值为国际化code
     */
    private String title;

    // 只会处理填了此注解的属性，且属性必须是 String 类型
    // condition：当对象的属性 type !=2 时才会进行国际化
    /**
     * 值为国际化code，但是会进行国际化处理，实际响应出去的是国际化后的菜单名称
     */
    @I18nField(condition = "type != 2")
    private String i18nTitle;

    /**
     * 菜单类型 （0目录，1菜单，2按钮）
     */
    private Integer type;
}
`,
      },
      {
        tip: "对应的 Controller：",
        content: `java
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/i18n")
public class Ii8nController {

    private final MessageSource dynamicMessageSource;

    /**
     * 从 messageSource 中获取对应的国际化信息 注意切换请求头中的 Accept-Language 对应的语言：en_US | zh_CN
     * @see org.springframework.web.servlet.i18n.AcceptHeaderLocaleResolver
     * @param code 国家化 code
     * @return 国际化文本
     */
    @GetMapping("hello")
    public String hello(@RequestParam("code") String code) {
        return dynamicMessageSource.getMessage(code, null, LocaleContextHolder.getLocale());
    }

    /**
     * 参数校验错误信息的国际化
     *
     * 当传递小于 10 的年龄时，查看返回的错误信息（请求头 Accept-Language 切换语言）
     * @param age 年龄，不能小于 10
     * @return Integer 年龄
     */
    @GetMapping("paramValidate")
    public Integer paramValidate(@Min(value = 10, message = "{validation.ageWrong}") @RequestParam("age") Integer age) {
        return age;
    }

    /**
     * 实体类的属性校验错误信息的国际化
     * @param demoDataDTO 测试入参
     * @return DemoData
     */
    @PostMapping("bodyValidate")
    public DemoDataDTO bodyValidate(@Valid @RequestBody DemoDataDTO demoDataDTO) {
        return demoDataDTO;
    }

    @GetMapping("returnResult")
    public R<List<MenuVO>> returnResult() {
        return R.ok(List.of(
                MenuVO.builder().title("menu.system.user").i18nTitle("menu.system.user").type(1).build(),
                MenuVO.builder().title("按钮1").i18nTitle("按钮1").type(2).build(),
                MenuVO.builder().title("menu.system.role").i18nTitle("menu.system.role").type(3).build()
        ));
    }
}
`,
      },
    ],
  },
  {
    title: ["3.1 高级特性-多语言环境切换"],
    children: [
      {
        tip: "支持通过多种方式指定语言环境：1. HTTP Header 方式",
        content: `curl -H "Accept-Language: en-US" http://localhost:8080/api/user`,
      },
      {
        tip: "2. 编程方式设置",
        content: `java
import org.springframework.context.i18n.LocaleContextHolder;

@Service
public class UserService {
    
    public void processWithLocale() {
        // 临时切换语言环境
        Locale originalLocale = LocaleContextHolder.getLocale();
        try {
            LocaleContextHolder.setLocale(Locale.US);
            // 在此处理业务逻辑，会使用英文环境
            String message = I18nUtils.getMessage("user.created.success");
        } finally {
            // 恢复原始语言环境
            LocaleContextHolder.setLocale(originalLocale);
        }
    }
}
`,
      },
    ],
  },
  {
    title: ["3.2 校验注解国际化"],
    children: [
      {
        tip: "Hibernate Validator 校验消息支持国际化：",
        content: `java
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.Email;
import lombok.Data;

@Data
public class CreateUserRequest {
    
    @NotEmpty(message = "{user.name.required}")
    private String name;
    
    @Email(message = "{user.email.invalid}")
    private String email;
    
    @NotEmpty(message = "{user.password.required}")
    private String password;
}
`,
      },
      {
        tip: "对应的国际化资源文件：",
        content: `properties
# messages_zh_CN.properties
user.name.required=用户名不能为空
user.email.invalid=邮箱格式不正确
user.password.required=密码不能为空

# messages_en_US.properties
user.name.required=Username is required
user.email.invalid=Invalid email format
user.password.required=Password is required
`,
      },
    ],
  },
  {
    title: ["4.1 常见问题-配置问题"],
    children: [
      {
        tip: "问题：国际化不生效",
        content: `解决方案：
1. 检查是否正确引入了 'trina-i18n-starter' 依赖
2. 确认 'spring.messages.basename' 配置正确
3. 验证资源文件路径和命名是否符合规范
4. 检查是否在响应类上添加了 '@I18nClass' 注解
`,
      },
      {
        tip: "问题：通配符资源文件加载失败",
        content: `解决方案：
1. 确认使用的是 'WildcardReloadableResourceBundleMessageSource'
2. 检查通配符模式是否正确
3. 验证资源文件是否在 classpath 中
`,
      },
    ],
  },
  {
    title: ["4.2 使用问题"],
    children: [
      {
        tip: "问题：注解式国际化不工作",
        content: `1. 确认类上有 '@I18nClass' 注解
2. 检查字段上的 '@I18nField' 注解配置
3. 验证 Controller 方法没有使用 '@I18nIgnore' 注解
4. 确认返回的数据结构正确
`,
      },
      {
        tip: "问题：动态国际化消息未生效",
        content: `解决方案：
1. 检查 'I18nMessageProvider' 实现是否正确注册到 Spring 容器
2. 验证数据库或缓存中是否存在对应的国际化消息
3. 确认语言标签格式是否正确（如：zh-CN, en-US）
`,
      },
    ],
  },
  {
    title: ["4.3 故障排查"],
    children: [
      {
        tip: "启用调试日志",
        content: `logging:
  level:
    com.trinasolar.i18n: DEBUG
    org.springframework.context.support: DEBUG
`,
      },
      {
        tip: "检查 MessageSource 配置",
        content: `java
@RestController
public class DebugController {
    
    @Autowired
    private MessageSource messageSource;
    
    @GetMapping("/debug/message")
    public String debugMessage(@RequestParam String code, 
                              @RequestParam(defaultValue = "zh-CN") String lang) {
        Locale locale = Locale.forLanguageTag(lang);
        try {
            return messageSource.getMessage(code, null, locale);
        } catch (NoSuchMessageException e) {
            return "Message not found for code: " + code + ", locale: " + locale;
        }
    }
}
`,
      },
    ],
  },
  {
    title: ["5.1 最佳实践-国际化消息组织"],
    children: [
      {
        tip: "按模块组织消息",
        content: `properties
# user 模块
user.name.required=用户名不能为空
user.email.invalid=邮箱格式不正确
user.created.success=用户创建成功

# order 模块  
order.status.pending=待处理
order.status.completed=已完成
order.amount.invalid=金额不正确

# system 模块
system.error.internal=系统内部错误
system.error.network=网络连接错误

`,
      },
      {
        tip: "使用层级结构",
        content: `properties
# 错误消息
error.validation.required=字段不能为空
error.validation.format=格式不正确
error.business.insufficient.balance=余额不足
error.system.timeout=系统超时

# 成功消息
success.user.created=用户创建成功
success.order.submitted=订单提交成功
`,
      },
    ],
  },
  {
    title: ["5.2 性能优化建议"],
    children: [
      {
        tip: "合理使用缓存",
        content: `java
@Component
public class CachedI18nMessageProvider implements I18nMessageProvider {
    
    @Cacheable(value = "i18nMessages", key = "#code + '_' + #locale.toLanguageTag()")
    @Override
    public I18nMessage getI18nMessage(String code, Locale locale) {
        // 实际的数据库查询逻辑
        return queryFromDatabase(code, locale);
    }
    
    @CacheEvict(value = "i18nMessages", allEntries = true)
    public void clearCache() {
        // 清除缓存的方法
    }
}
`,
      },
      {
        tip: "减少不必要的国际化处理",
        content: `java
@Data
@I18nClass
public class UserDTO {
    
    private Long id;
    private String name;
    
    // 对于枚举值或状态码，使用条件国际化
    @I18nField(condition = "status != null && !status.isEmpty()")
    private String status;
    
    // 对于数值类型，通常不需要国际化
    private BigDecimal amount;
    
    private LocalDateTime createTime;
}
`,
      },
    ],
  },
  {
    title: ["5.3 安全考虑"],
    children: [
      {
        tip: "验证语言标签",
        content: `java
@Component
public class LanguageValidator {
    
    private static final Set<String> SUPPORTED_LANGUAGES = 
        Set.of("zh-CN", "en-US", "ja-JP", "ko-KR");
    
    public boolean isValidLanguage(String languageTag) {
        return SUPPORTED_LANGUAGES.contains(languageTag);
    }
}
`,
      },
      {
        tip: "限制远程国际化访问",
        content: `yaml
trina:
  i18n:
    url: http://internal-i18n-service:8080
    # 添加访问控制
    api:
      timeout: 5s
      max-retries: 3
      circuit-breaker:
        enabled: true
`,
      },
    ],
  },
  {
    title: ["5.4 测试建议"],
    children: [
      {
        tip: "单元测试示例",
        content: `java
@SpringBootTest
class I18nTest {
    
    @Autowired
    private MessageSource messageSource;
    
    @Test
    void testChineseMessage() {
        Locale locale = Locale.forLanguageTag("zh-CN");
        String message = messageSource.getMessage("user.name.required", null, locale);
        assertEquals("用户名不能为空", message);
    }
    
    @Test
    void testEnglishMessage() {
        Locale locale = Locale.forLanguageTag("en-US");
        String message = messageSource.getMessage("user.name.required", null, locale);
        assertEquals("Username is required", message);
    }
    
    @Test
    void testAnnotationBasedI18n() {
        UserInfo userInfo = new UserInfo();
        userInfo.setStatus("active");
        
        // 模拟国际化处理
        LocaleContextHolder.setLocale(Locale.forLanguageTag("zh-CN"));
        // 测试注解式国际化效果
    }
}
`,
      },
      {
        tip: "限制远程国际化访问",
        content: `yaml
trina:
  i18n:
    url: http://internal-i18n-service:8080
    # 添加访问控制
    api:
      timeout: 5s
      max-retries: 3
      circuit-breaker:
        enabled: true
`,
      },
    ],
  },
  {
    title: ["6. 参考资料"],
    children: [
      {
        content: `- [Spring Boot 国际化官方文档](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.internationalization)
- [Hibernate Validator 国际化](https://hibernate.org/validator/documentation/getting-started/)
- [Caffeine 缓存配置](https://github.com/ben-manes/caffeine)`,
      },
    ],
  },
];
export const detailForm = [
  { key: "1", label: "名称:", value: "国际化组件" },
  { key: "1", label: "版本:", value: "1.0.0" },
  { key: "1", label: "创建人:", value: "罗成" },
  { key: "1", label: "创建时间:", value: "2025-02-27 02:50:00" },
  { key: "1", label: "修改人:", value: "罗成" },
  { key: "1", label: "修改时间:", value: "2025-03-27 02:50:00" },
  // { key: "1", label: "文档链接:", value: "-" },
  {
    key: "1",
    label: "描述:",
    style: { "grid-column": "span 2" },
    value: `Trina I18n Starter 国际化功能的完整使用指南，包括配置方法、API 使用、注解式国际化等`,
  },
];
