.middleware-detail {
  background: #fff;
  min-height: 100vh;

  .detail-header {
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;

    .nav-path {
      span {
        color: #666;
        margin: 0 4px;
        font-size: 14px;

        &:first-child {
          margin-left: 0;
        }
      }
    }
  }

  // 响应式调整
  @media screen and (min-width: 1920px) {
    .dealPadding {
      padding: 24px calc((100vw - 81%) / 2);
    }
  }

  // 响应式调整
  @media screen and (max-width: 1920px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  // 响应式调整
  @media screen and (max-width: 1800px) {
    .dealPadding {
      padding: 24px calc((100vw - 81%) / 2);
    }
  }
  // 响应式调整
  @media screen and (max-width: 1700px) {
    .dealPadding {
      padding: 24px calc((100vw - 86%) / 2);
    }
  }

  @media screen and (max-width: 1600px) {
    .dealPadding {
      padding: 24px calc((100vw - 1200px) / 2);
    }
  }

  @media screen and (max-width: 1200px) {
    .dealPadding {
      padding: 24px 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .dealPadding {
      padding: 24px 20px;
    }

    .home-header {
      padding: 24px;

      h1 {
        font-size: 24px;
      }

      .description {
        font-size: 14px;
      }
    }
  }
  .nav-path-ui{
    padding-top: 8px;
    padding-bottom: 4px;
  }

  .middleware-header {
    background-image: url('@/assets/images/pasdetail.svg');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    .middleware-base-info {
      display: flex;
      gap: 20px;
      .middleware-logo {
        width: 64px;
        height: 64px;
        border-radius: 8px;
      }
      .logo-title {
        display: flex;
        align-items: center;
        gap: 16px;



        .title-wrapper {
          .title {
            font-size: 20px;
            color: #333;
            font-weight: 500;
            line-height: 1.4;
          }
        }
      }
      .action-buttons{
        margin-top: 8px;
        height: 33px;
        line-height: 33px;
      }
    }
  }
}

