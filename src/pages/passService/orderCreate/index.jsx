import React, {useState, useEffect, useMemo} from 'react';
import {history, useModel, useParams, useSearchParams} from 'umi';
import { Form, Input, Select, Radio, Upload, Button, Row, Col, Tooltip, message } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import './index.less';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import {getAppList, getUserInfoByUserCode, applyObjectStorage, uploadFileForObjectStorage, getObjectStorageDetail} from "@/services";
const { Option } = Select;
const { useWatch } = Form; // 新增：导入 useWatch

const OrderCreate = () => {
  // 使用 query 参数（例如获取 id 参数：query.id）
  const [searchParams] = useSearchParams();
  console.log('地址栏 query 参数:', searchParams);

  const { type } = useParams();
  const [form] = Form.useForm();
  const [applicationSystems, setApplicationSystems] = useState([]);
  const [files, setFiles] = useState([]);
  const [applyUser, setApplyUser] = useState([]);
  const [reportToUser, setReportToUser] = useState([]);
  const [applicationDirectorUser, setApplicationDirectorUser] = useState([]);
  const [applicationOperationsUser, setApplicationOperationsUser] = useState([]);

  const isEdit = useMemo(()=>Boolean(searchParams.get('type')&&searchParams.get('type')!=='资源申请'),[searchParams])
  const [isHistoryData, setIsHistoryData] = useState(false)

  useEffect(() => {
    const type = searchParams.get('type')
    if(type){
      form.setFieldsValue({
        applyType: type,
      })
    }
    const bucketName = searchParams.get('bucketName')
    if(type!=='资源申请' && bucketName){
      form.setFieldsValue({
        bucketName,
      })
      fetchDetail(bucketName)
    }
  }, [searchParams]);

  useEffect(() => {
    const cnName = searchParams.get('cnName')
    if(isHistoryData && cnName){
      form.setFieldsValue({
        applicationName: cnName,
      })
      handleApplicationChange(cnName)
    }
  }, [isHistoryData,applicationSystems]);

  const fetchUserInfo=async(userCode)=>{
    const data = await getUserInfoByUserCode(userCode)
    setReportToUser([{
      label: data?.userRealname+'('+data?.username+')',
      value: data?.userCode,
    }] || [])
    form.setFieldsValue({
      reportTo: data?.userCode,
    })
  }

  const { userInfo } = useModel("user");
// 示例：在组件挂载后或数据更新时动态设置表单值
  useEffect(() => {
    if (userInfo?.email) {
      // 动态设置邮箱字段值（核心方法：setFieldsValue）
      setApplyUser([{
        label: userInfo.userRealname+'('+userInfo.username+')',
        value: userInfo.userCode,
      }] || [])
      form.setFieldsValue({
        email: userInfo.email, // 设置邮箱字段
        // 可同时设置其他字段，如：
        department: userInfo.orgName,
        applyUser: userInfo.userCode,
      });
      fetchUserInfo(userInfo.reportto)
    }
  }, [userInfo, form]); // 依赖 userInfo 变化时触发

  // 新增：监听环境字段值变化
  const env = useWatch('env', form);
  const applyType = useWatch('applyType', form);
  const getEnvCode=(env)=>{
    if(env==='生产环境'){
      return 'prod'
    }
    if(env==='测试环境'){
      return 'test'
    }
    if(env==='UAT'){
      return 'uat'
    }
    if(env==='开发环境'){
      return 'dev'
    }
  }

  const fetchDetail = async(bucketName) => {
    const data = await getObjectStorageDetail(bucketName)
    console.log(data)
    if(data){
      if(typeof data === 'string' && data.startsWith('{')){
        const _data=JSON.parse(data || '{}')
        console.log(_data)
        //赋值字段数组
        const fields=['env','applicationName','applicationCode','applicationDirector','applicationOperations']
        // 筛选 _data，仅保留 fields 中指定的有效字段（过滤空字符串）
        const filteredData = fields
            .filter(field => field) // 移除空字符串字段
            .reduce((obj, field) => {
              if (_data.hasOwnProperty(field)) {
                obj[field] = _data[field]; // 仅保留 _data 中存在的目标字段
              }
              return obj;
            }, {});

        // 使用筛选后的字段赋值给表单
        form.setFieldsValue(filteredData);
      }
    }else {
      setIsHistoryData(true)
    }
  }

  const fetchApps=async()=>{
    const data = await getAppList({current:1,size:9999})
    setApplicationSystems(data.records||[])
    console.log(data)
  }

  // 模拟获取应用系统列表数据
  useEffect(() => {
    fetchApps()
  }, []);

  // 处理应用系统选择变化
  const handleApplicationChange = (value) => {
    const selectedApp = applicationSystems.find(app => app.cnName === value);
    console.log(value,selectedApp)
    if (selectedApp) {
      const appAdmin=selectedApp?.responsibleUsers?.appAdmin?.[0]
      const omUserId=selectedApp?.responsibleUsers?.omUserId?.[0]
      setApplicationDirectorUser([{
        label: appAdmin?.userRealname+'('+appAdmin?.account+')' || '',
        value: appAdmin?.account || '',
      }] || [])
      setApplicationOperationsUser([{
        label: omUserId?.userRealname+'('+omUserId?.account+')' || '',
        value: omUserId?.account || '',
      }] || [])
      form.setFieldsValue({
        applicationCode: selectedApp.enSimpleName,
        applicationDirector: appAdmin?.account || '',
        applicationOperations: omUserId?.account || ''
      });
    }
  };

  // 处理表单提交
  const [isLoading, setIsLoading] = useState(false)
  const handleSubmit = () => {
    form.validateFields()
        .then(async values => {
          const params={
            ...values,
            bucketName:applyType === '资源申请'?values.bucketName+'-'+getEnvCode(env)+'-bucket':values.bucketName,
            files:files.map(f=>f.value),
          }
          console.log('表单提交数据:', params);
          setIsLoading(true)
          await applyObjectStorage(params).then(()=>{
            message.success('表单提交成功');
            history.back()
          }).catch((err)=>{
            message.error(err.msg || '申请失败')
          }).finally(()=>{
            setIsLoading(false)
          })
        })
  };

  const linkList = useMemo(() => {
    if(!isEdit){
      return [
        {
          href: '/#/productService',
          title: '产品与服务'
        },
        {
          title: `对象存储申请`
        },
      ]
    }else {
      return [
        {
          title: '应用系统管理'
        },
        {
          title: `存储服务管理`
        },
        {
          title: `对象存储申请`
        },
      ]
    }
  },[isEdit])

  // 文件格式验证
  const beforeUpload = async(file) => {
    const isLt100MB = file.size / 1024 / 1024 < 100;
    if (!isLt100MB) {
      message.error('单个文件大小不能超过100MB!');
      return false;
    }

    const allowedFormats = ['doc', 'docx', 'pdf', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'xlsm', 'csv', 'msg', 'jpeg', 'jpg', 'png', 'gif', 'bmp', 'webp', 'tiff'];
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !allowedFormats.includes(fileExtension)) {
      message.error('不支持的文件格式!');
      return false;
    }

    const formData = new FormData();
    formData.append('file', file);
    console.log(file)
    const res = await uploadFileForObjectStorage(formData)
    if(res.data.data){
      const result = JSON.parse(res.data.data)
      console.log(result.data)
      // 修复：使用函数式更新，基于最新的 prevFiles 追加新文件
      setFiles(prevFiles => [
        ...prevFiles, // 基于前序最新状态（而非当前作用域的旧 files）
        { uid: file.uid, value: result.data } // 追加当前文件
      ]);
    }
    return false; // 阻止自动上传，实际项目中应返回true并配置action
  };

  const handleRemove = async(file) => {
    console.log(file)
    setFiles(files.filter(f => f.uid !== file.uid));
  };

  return (
      <div className="middleware-detail">
        {/* 头部导航 */}
        <BreadcrumbNav list={linkList} isNeedBack/>
        {/* 标题区域 */}
        <div className="middleware-header dealPadding">
          <div className="middleware-base-info">
            <div>
              <img src={`https://tos.trinasolar.com/n5r00e239du8tvtk1hw5o4/product/external/objectStorage.png`} alt={type} className="middleware-logo" />
            </div>
            <div >
              <div className="logo-title">
                <div className="title-wrapper">
                  <span className="title">对象存储</span>
                </div>
              </div>
              <div className="action-buttons">
                通过扁平化结构管理非结构化数据，每个对象包含数据、可扩展元数据和全局唯一标识符。
              </div>
            </div>
          </div>
        </div>
        {/* 表单区域 */}
        <div className="form-container dealPadding">
          <Form
              form={form}
              layout="vertical"
              initialValues={{
                env: '生产环境',
                applyType: '资源申请',
                liter: 'GB'
              }}
          >
            {/* 表单行1：申请人、部门 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                    name="applyUser"
                    label="申请人"
                    rules={[{ required: true, message: '请输入申请人' }]}
                >
                  <Select placeholder="请选择申请人" disabled={true}>
                    {applyUser.map(item=>(
                      <Option key={item.value} value={item.value}>{item.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                    name="department"
                    label="部门"
                    rules={[{ required: true, message: '请输入部门' }]}
                >
                  <Input placeholder="请输入部门" disabled={true}/>
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行2：邮箱、地点 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                    name="email"
                    label="邮箱"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                >
                  <Input placeholder="请输入邮箱" disabled={true}/>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                    name="reportTo"
                    label="汇报对象"
                    rules={[{ required: true, message: '请输入汇报对象' }]}
                >
                  <Select placeholder="请选择汇报对象" disabled={true}>
                    {reportToUser.map(item=>(
                        <Option key={item.value} value={item.value}>{item.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行4：标题 */}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                    name="title"
                    label="标题"
                    rules={[{ required: true, message: '请输入标题' }]}
                >
                  <Input placeholder="请输入标题" />
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行5：描述 */}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                    name="description"
                    label="描述"
                    rules={[{ required: true, message: '请输入描述' }]}
                >
                  <Input.TextArea rows={4} placeholder="请输入描述" />
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行6：环境 */}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                    name="env"
                    label="环境"
                    rules={[{ required: true, message: '请选择环境' }]}
                >
                  <Radio.Group disabled={!isHistoryData && isEdit}>
                    <Radio value="生产环境">生产环境</Radio>
                    <Radio value="测试环境">测试环境</Radio>
                    <Radio value="UAT">UAT</Radio>
                    <Radio value="开发环境">开发环境</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行7：应用系统名称、应用英文名缩写 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                    name="applicationName"
                    label="应用系统名称"
                    rules={[{ required: true, message: '请选择应用系统' }]}
                >
                  <Select
                      disabled={isEdit}
                      placeholder="请选择应用系统"
                      onChange={handleApplicationChange}
                      showSearch
                      filterOption={(input, option) =>
                          (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                  >
                    {applicationSystems.map(app => (
                        <Option key={app.cnName} value={app.cnName}>{app.cnName}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                    name="applicationCode"
                    label="应用英文名缩写"
                    rules={[{ required: true }]}
                >
                  <Input placeholder="选择应用后自动填充" disabled={true}/>
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行8：应用负责人、应用运维人员 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                    name="applicationDirector"
                    label="应用负责人"
                    rules={[{ required: true }]}
                >
                  <Select placeholder="选择应用后自动填充" disabled={true}>
                    {applicationDirectorUser.map(item=>(
                      <Option key={item.value} value={item.value}>{item.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                    name="applicationOperations"
                    label="应用运维人员"
                    rules={[{ required: true }]}
                >
                  <Select placeholder="选择应用后自动填充" disabled={true}>
                    {applicationOperationsUser.map(item=>(
                      <Option key={item.value} value={item.value}>{item.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行9：申请类型 */}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                    name="applyType"
                    label="申请类型"
                    rules={[{ required: true, message: '请选择申请类型' }]}
                >
                  <Radio.Group disabled={isEdit}>
                    <Radio value="资源申请">资源申请</Radio>
                    <Radio value="资源扩容">资源扩容</Radio>
                    <Radio value="资源缩容">资源缩容</Radio>
                    <Radio value="资源回收">资源回收</Radio>
                  </Radio.Group>
                </Form.Item>
              </Col>
            </Row>

            {/* 表单行10：存储桶名称、存储桶大小/扩容/缩容容量、容量单位 */}
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                    name="bucketName"
                    label="存储桶名称"
                    rules={[{ required: true, message: '请输入存储桶名称' }]}
                >
                  {/* 使用 flex 布局使输入框和后缀在同一行 */}
                  {applyType === '资源申请' && <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Input placeholder="请输入存储桶名称" style={{ flex: 1 }}/>
                    {env && (
                        <span>-{getEnvCode(env)}-bucket</span>
                    )}
                  </div>}
                  {applyType !== '资源申请' && <Input placeholder="请输入存储桶名称" disabled={isEdit}/> }
                </Form.Item>
              </Col>

              {(applyType === '资源申请' || applyType === '资源扩容' || applyType === '资源缩容') && (
                  <>
                    <Col span={8}>
                      <Form.Item
                          name="bucketSize"
                          label={
                            applyType === '资源申请' ? '存储桶大小' :
                                applyType === '资源扩容' ? '存储桶扩容容量' : '存储桶缩容容量'
                          }
                          rules={[{
                            required: true, message: `请输入${applyType === '资源申请' ? '存储桶大小' : applyType === '资源扩容' ? '存储桶扩容容量' : '存储桶缩容容量'}` },
                            // 新增：正整数校验规则（不允许 0、负数、小数、空字符串）
                            { pattern: /^[1-9]\d*$/, message: '请输入正整数' }
                          ]}
                      >
                        <Input type="number" placeholder={`请输入${applyType === '资源申请' ? '存储桶大小' : applyType === '资源扩容' ? '存储桶扩容容量' : '存储桶缩容容量'}`} />
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                          name="liter"
                          label="容量单位"
                          rules={[{ required: true, message: '请选择容量单位' }]}
                      >
                        <Select defaultValue="GB">
                          <Option value="GB">GB</Option>
                          <Option value="TB">TB</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </>
              )}
            </Row>

            {/* 表单行5：描述 */}
            {applyType === '资源申请' && (
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                    name="serviceIpAddress"
                    label="使用存储桶的应用IP地址"
                    rules={[{ required: true, message: '请输入使用存储桶的应用IP地址' }]}
                >
                  <Input.TextArea rows={4} placeholder="请输入使用存储桶的应用IP地址" />
                </Form.Item>
              </Col>
            </Row>)}

            {/* 表单行11：附件 */}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                    name="files"
                    label="附件"
                >
                  <Upload
                      name="files"
                      multiple
                      beforeUpload={beforeUpload}
                      onRemove={handleRemove}
                  >
                    <div style={{ border: '1px dashed #ccc', padding: '20px', textAlign: 'center' }}>
                      <UploadOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                      <p>单击或拖动文件到此区域</p>
                      <Tooltip title="doc/docx/pdf/xls/xlsx/ppt/pptx/txt/xlsm/csv/msg/jpeg/jpg/png/gif/bmp/webp/tiff">
                        <span style={{ color: 'blue', marginLeft: 8 }}>支持格式</span>
                      </Tooltip>
                      <p style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>支持多选文件批量上传，单个文件大小最大限制100MB</p>
                    </div>
                  </Upload>
                </Form.Item>
              </Col>
            </Row>

            {/* 按钮行 */}
            <Row gutter={16} style={{ marginTop: '24px', textAlign: 'right' }}>
              <Col span={24}>
                <Button onClick={() => history.back()} style={{ marginRight: '8px' }}>取消</Button>
                <Button type="primary" onClick={handleSubmit} loading={isLoading}>提交</Button>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
  );
};

export default OrderCreate;
