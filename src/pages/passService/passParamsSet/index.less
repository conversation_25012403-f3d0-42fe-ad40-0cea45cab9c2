.params-set-container {
  .warning-tip {
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    padding: 8px 16px;
    border-radius: 4px;
    margin-bottom: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    span {
      color: #faad14;
      font-size: 14px;
    }

    .close-icon {
      cursor: pointer;
      color: #faad14;
      &:hover {
        opacity: 0.8;
      }
    }
  }

  .search-box {
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    gap: 12px;

    .button-group {
      display: flex;
      gap: 8px;
    }

    .ant-btn {
      flex-shrink: 0;
    }

    .ant-input-affix-wrapper {
      width: 240px;
      flex-shrink: 0;
    }
  }

  .params-table {
    .ant-table-wrapper {
      border: 1px solid #f0f0f0;
      border-radius: 4px;
    }

    .ant-table-thead > tr > th {
      background: #fafafa;
      font-weight: 500;
    }

    .ant-table-row {
      cursor: pointer;
      &:hover {
        background-color: #f5f5f5;
      }
    }

    // 禁用状态的复选框样式
    .ant-checkbox-disabled + span {
      color: rgba(0, 0, 0, 0.25);
    }

    .ant-checkbox-disabled .ant-checkbox-inner {
      background-color: #f5f5f5;
      border-color: #d9d9d9 !important;
    }

    // 表头复选框禁用状态
    .ant-table-thead .ant-checkbox-wrapper-disabled {
      opacity: 0.65;
    }
  }
} 