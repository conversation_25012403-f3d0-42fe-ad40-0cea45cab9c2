import React, { useState, useEffect } from 'react';
import { Drawer, Input, Table, Button, message, Tooltip } from 'antd';
import { SearchOutlined, CloseOutlined, QuestionCircleOutlined } from '@ant-design/icons';
import './index.less';
import { CONFIG_MIDDLEWARE, getMiddlewareHideParams } from '@/services/middleware';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId;
const ParamsSetDrawer = ({ visible, onClose, chartName, chartVersion }) => {
  const [searchText, setSearchText] = useState('');
  const [selectedParams, setSelectedParams] = useState([]);
  const [isEditing, setIsEditing] = useState(false);
  const [dataSource, setDataSource] = useState([]);

  // 表格列配置
  const columns = [
    {
      title: '参数名',
      dataIndex: 'name',
      key: 'name',
      width: 200,
    },
    {
      title: '所属节点类型',
      dataIndex: 'nodeType',
      key: 'nodeType',
      width: 150,
    },
    {
      title: '参数描述',
      dataIndex: 'description',
      key: 'description',
      render: (text) => {
        return <Tooltip title={text}><QuestionCircleOutlined /></Tooltip>
      }
    }
  ];

  useEffect(() => {
    if (chartName)
      getMiddlewareHideParams({ type: chartName, clusterId: CLUSTER_ID }).then(
        (res) => {
          setDataSource(res);
        }
      );
  }, [chartName]);

  // 行选择配置
  const rowSelection = {
    selectedRowKeys: selectedParams,
    onChange: (selectedRowKeys) => {
      if (isEditing) {
        setSelectedParams(selectedRowKeys);
      }
    },
    getCheckboxProps: () => ({
      disabled: !isEditing,
    }),
    selections: isEditing ? [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ] : [],
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleSave = () => {
    if (selectedParams.length === 0) {
      message.warning('请选择需要配置的参数');
      return;
    }
    // TODO: 处理保存逻辑
    message.success('保存成功');
    setIsEditing(false);
  };

  const handleCancel = () => {
    setSelectedParams([]);
    setIsEditing(false);
  };

  // 抽屉关闭时重置状态
  const handleDrawerClose = () => {
    setSelectedParams([]);
    setIsEditing(false);
    onClose();
  };

  return (
    <Drawer
      title="参数选配"
      placement="right"
      width={800}
      onClose={handleDrawerClose}
      open={visible}
    >
      <div className="params-set-container">
        {/* 顶部提示 */}
        <div className="warning-tip">
          <span>勾选的参数将不会出现在对应中间件服务的参数设置列表中！</span>
          <CloseOutlined className="close-icon" />
        </div>

        {/* 搜索框和按钮区域 */}
        <div className="search-box">
          {!isEditing ? (
            <Button type="primary" onClick={handleEdit}>
              编辑
            </Button>
          ) : (
            <div className="button-group">
              <Button type="primary" onClick={handleSave}>
                保存
              </Button>
              <Button onClick={handleCancel}>
                取消
              </Button>
            </div>
          )}
          <Input
            placeholder="请输入搜索关键字"
            suffix={<SearchOutlined />}
            value={searchText}
            style={{ width: 200 }}
            onChange={e => setSearchText(e.target.value)}
          />
        </div>

        {/* 表格 */}
        <div className="params-table">
          <Table
            rowSelection={rowSelection}
            columns={columns}
            dataSource={dataSource}
            pagination={{
              total: dataSource.length,
              pageSize: 10,
              showTotal: (total) => `共 ${total} 项数据`
            }}
            scroll={{ y: 'calc(100vh - 300px)' }}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default ParamsSetDrawer;
