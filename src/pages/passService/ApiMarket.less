.api-market {
  display: flex;
  gap: 24px;
  padding: 24px;
  background: #fff;
  min-height: 600px;

  // 响应式调整
  @media screen and (min-width: 1920px) {
    .dealPadding {
      padding: 24px 480px;
    }
  }

  // 响应式调整
  @media screen and (max-width: 1920px) {
    .dealPadding {
      padding: 24px 240px;
    }
  }

  @media screen and (max-width: 1600px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  @media screen and (max-width: 1200px) {
    .dealPadding {
      padding: 24px 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .dealPadding {
      padding: 24px 20px;
    }

    .home-header {
      padding: 24px;

      h1 {
        font-size: 24px;
      }

      .description {
        font-size: 14px;
      }
    }
  }
  .left-sidebar {
    width: 240px;
    flex-shrink: 0;
    background: #F4FAFE;
    border-radius: 8px;
    padding: 16px 0;

    .navigation {
      .navigation-title {
        display: flex;
        align-items: center;
        padding: 0 24px 12px;
        
        .nav-indicator {
          width: 3px;
          height: 16px;
          background: #1890ff;
          border-radius: 2px;
          margin-right: 8px;
        }
        
        h3 {
          margin: 0;
          font-size: 16px;
          font-weight: 500;
        }
      }
      
      .search-container {
        padding: 0 16px 12px;
        
        .nav-search {
          height: 36px;
          border-radius: 4px;
          padding: 4px 12px;
          border: 1px solid #d9d9d9;
          transition: all 0.3s;
          background: #fff;
          
          &:hover {
            border-color: #40a9ff;
          }
          
          &:focus, &-focused {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
          
          .ant-input-prefix {
            margin-right: 8px;
            color: rgba(0, 0, 0, 0.45);
          }
          
          input {
            font-size: 14px;
            
            &::placeholder {
              color: rgba(0, 0, 0, 0.35);
            }
          }
        }
      }
    }

    .category-item {
      padding: 12px 24px;
      cursor: pointer;
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: #333;
      font-size: 14px;
      position: relative;
      transition: all 0.3s ease;

      &:hover {
        color: #1890ff;
        background: #e6f7ff;
      }

      &.active {
        color: #1890ff;
        background: #E6F3FB;
        font-weight: 500;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 3px;
          height: 16px;
          background: #1890ff;
          border-radius: 0 2px 2px 0;
        }
      }

      .category-name {
        position: relative;
        padding-left: 8px;
      }

      .category-count {
        color: #999;
        font-size: 12px;
        background: #f0f0f0;
        padding: 2px 8px;
        border-radius: 10px;
      }
    }

    .category-tree {
      .micro-tree-list-holder-inner {
        background-color: #F4FAFE !important;
      }
      .ant-tree {
        .ant-tree-node-content-wrapper {
          display: flex;
          align-items: center;
          padding: 8px 16px;
          transition: all 0.3s;
          
          &:hover {
            background-color: #e6f7ff;
          }
          
          &.ant-tree-node-selected {
            background-color: #E6F3FB;
            color: #1890ff;
            font-weight: 500;
          }
        }
        
        .ant-tree-switcher {
          width: 24px;
          height: 24px;
          line-height: 24px;
          display: flex;
          align-items: center;
          justify-content: center;
          
          .ant-tree-switcher-icon {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
          }
        }
        
        .ant-tree-node-content-wrapper {
          .ant-tree-title {
            margin-left: 8px;
            font-size: 14px;
            color: #333;
          }
          
          .ant-tree-iconEle {
            width: 16px;
            height: 16px;
            margin-right: 8px;
            
            svg {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }

  .main-content {
    flex: 1;
    min-width: 0;

    .search-bar {
      margin-bottom: 24px;
      background: #fff;
      display: flex;
      justify-content: flex-end;

      .search-filters {
        display: flex;
        align-items: center;
        gap: 32px;

        .search-item {
          &.sort-item {
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 4px;

            span {
              color: rgba(0, 0, 0, 0.65);
              font-size: 14px;
              line-height: 22px;
            }

            &:hover {
              span {
                color: #1890ff;
              }
              .sort-arrows {
                .arrow {
                  &.up {
                    border-bottom-color: #1890ff;
                  }
                  &.down {
                    border-top-color: #1890ff;
                  }
                }
              }
            }

            .sort-arrows {
              display: flex;
              flex-direction: column;
              gap: 2px;
              margin-left: 4px;
              height: 12px;

              .arrow {
                width: 0;
                height: 0;
                border-left: 4px solid transparent;
                border-right: 4px solid transparent;

                &.up {
                  border-bottom: 4px solid rgba(0, 0, 0, 0.45);
                }

                &.down {
                  border-top: 4px solid rgba(0, 0, 0, 0.45);
                }
              }
            }
          }
        }

        .search-inputs {
          display: flex;
          gap: 16px;

          .ant-input {
            width: 200px;
            height: 36px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            font-size: 14px;
            padding: 8px 12px;
            transition: all 0.3s;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.03);
            
            &::placeholder {
              color: rgba(0, 0, 0, 0.35);
            }

            &:hover {
              border-color: #40a9ff;
            }

            &:focus {
              border-color: #40a9ff;
              box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              outline: none;
            }
          }
        }
      }
    }

    .api-list {
      display: grid;
      gap: 20px;

      @media screen and (min-width: 2000px) {
        grid-template-columns: repeat(5, minmax(300px, 380px));
        gap: 16px;
      }

      @media screen and (max-width: 1920px) {
        grid-template-columns: repeat(4, minmax(300px, 400px));
        gap: 16px;
      }
      @media screen and (max-width: 1800px) {
        grid-template-columns: repeat(3, minmax(280px, 400px));
        gap: 16px;
      }

      @media screen and (max-width: 1600px) {
        grid-template-columns: repeat(3, minmax(280px, 300px));
        gap: 16px;
      }

      @media screen and (max-width: 1200px) {
        grid-template-columns: repeat(2, minmax(280px, 300px));
      }

      .api-card {
        width: 100%;
        background: #fff;
        border-radius: 8px;
        border: 1px solid #E6E6E6;
        transition: all 0.3s ease;

        @media screen and (min-width: 1920px) {
          min-height: 180px;
        }

        @media screen and (max-width: 1919px) {
          min-height: 160px;
        }

        @media screen and (max-width: 1600px) {
          min-height: 150px;
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }

        .card-top {
          display: flex;
          align-items: flex-start;
          padding: 16px;

          .api-logo {
            width: 40px;
            height: 40px;
            background: #F4FAFE;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            flex-shrink: 0;

            img {
              width: 24px;
              height: 24px;
              object-fit: contain;
            }
          }

          .api-title {
            flex: 1;
            min-width: 0;
            margin-right: 8px;

            h4 {
              margin: 0 0 4px;
              font-size: 14px;
              line-height: 20px;
              color: rgba(0, 0, 0, 0.85);
              font-weight: 500;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            p {
              margin: 0;
              font-size: 12px;
              line-height: 18px;
              color: rgba(0, 0, 0, 0.45);
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }

          .api-status {
            flex-shrink: 0;
            
            .ant-btn {
              height: 24px;
              padding: 0 12px;
              font-size: 12px;
              border-radius: 4px;

              &.subscribed {
                background: #E9F7FE;
                  border-color: #A0D3FB;
                  color: #396CDF;

                &:hover {
                  background: #E9F7FE;
                  border-color: #A0D3FB;
                  color: #396CDF;
                }
              }
            }
          }
        }

        .card-bottom {
          padding: 0 16px 16px;

          .info-row {
            display: flex;
            gap: 16px;
            margin-bottom: 8px;
            font-size: 12px;
            line-height: 18px;
          }

          .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 6px;

            &:last-child {
              margin-bottom: 0;
            }

            .label {
              color: rgba(0, 0, 0, 0.45);
              margin-right: 8px;
              min-width: 48px;
              flex-shrink: 0;
            }

            .value {
              color: rgba(0, 0, 0, 0.65);
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &.path-item {
              .path-wrapper {
                flex: 1;
                display: flex;
                align-items: center;
                min-width: 0;

                .value {
                  flex: 1;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }

                .copy-btn {
                  flex-shrink: 0;
                  margin-left: 4px;
                  padding: 0 4px;
                  height: 20px;
                }
              }
            }
          }
        }
      }
    }
  }
} 