.pass-apply-namespace {
  .passApply {
    background: #ffffff;
    .detail-header {
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #f0f0f0;

      .nav-path {
        span {
          color: #666;
          margin: 0 4px;
          font-size: 14px;

          &:first-child {
            margin-left: 0;
          }
        }
      }

      .back-button {
        cursor: pointer;
        color: #1890ff;
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 14px;

        &:hover {
          opacity: 0.8;
        }
      }
    }


  // 响应式调整
  @media screen and (min-width: 1920px) {
    .dealPadding {
      padding: 24px calc((100vw - 81%) / 2);
    }
  }

  // 响应式调整
  @media screen and (max-width: 1920px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  // 响应式调整
  @media screen and (max-width: 1800px) {
    .dealPadding {
      padding: 24px calc((100vw - 81%) / 2);
    }
  }
   // 响应式调整
   @media screen and (max-width: 1700px) {
    .dealPadding {
      padding: 24px calc((100vw - 86%) / 2);
    }
  }

  @media screen and (max-width: 1600px) {
    .dealPadding {
      padding: 24px calc((100vw - 1200px) / 2);
    }
  }

  @media screen and (max-width: 1200px) {
    .dealPadding {
      padding: 24px 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .dealPadding {
      padding: 24px 20px;
    }

    .home-header {
      padding: 24px;

      h1 {
        font-size: 24px;
      }

      .description {
        font-size: 14px;
      }
    }
  }
  

    .container {
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .mainContent {
      // flex: 240px 0 0;
      display: flex;
      // flex: 1;
      height: calc(100vh - 64px - 84px); // 假设header高度为64px
    }

    .sideNav {
      width: 200px;
      background: #fff;
      padding: 0;
      border-right: 1px solid #e8e8e8;
      height: 100%;
    }

    .navTitle {
      font-size: 16px;
      font-weight: 500;
      color: rgba(0, 0, 0, 0.85);
      padding: 16px 24px;
    }

    .navItem {
      padding: 16px 24px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.65);
      font-size: 14px;

      &:hover {
        color: #008DDC;
        background: rgba(0, 141, 220, 0.04);
      }

      &.activeNav {
        color: #008DDC;
        background: rgba(0, 141, 220, 0.04);
      }
    }

    .navIcon {
      font-size: 8px;
      margin-right: 8px;
      color: #D9D9D9;
      display: inline-block;
      line-height: 1;

      &.activeIcon {
        color: #008DDC;
      }
    }

    .content {
      flex: 1;
      padding: 24px;
      overflow-y: auto;
    }

    .header {
      display: flex;
      align-items: center;
      height: 120px;
      background-image: url('@/assets/images/pasdetail.svg');
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;

      .logoWrapper {
        width: 80px;
        height: 80px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #fff;
        border-radius: 10px;
        margin-right: 24px;

        .logo {
          width: 68px;
          height: 50px;
          object-fit: contain;
        }
      }

      .titleWrapper {
        flex: 1;

        .mainTitle {
          font-size: 18px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 20px;
          margin-bottom: 8px;
        }

        .subTitle {
          font-size: 16px;
          color: #666;
        }
      }

      .manualLink {
        color: #008DDC;
        font-size: 14px;
        cursor: pointer;
      }
    }

    .section {
      margin-bottom: 32px;

      .sectionTitle {
        display: flex;
        align-items: center;
        margin-bottom: 24px;

        .titleIndicator {
          width: 5px;
          height: 16px;
          background: #008DDC;
          border-radius: 3px;
          margin-right: 16px;
        }

        span {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }
      }

      .optionsWrapper {
        display: flex;
        flex-wrap: wrap;
      }

      .optionCard {
        width: 195px;
        height: 60px;
        background: #fff;
        border: 1px solid rgba(126, 134, 142, 0.48);
        border-radius: 12px;
        display: flex;
        align-items: center;
        padding: 0 16px;
        cursor: pointer;
        position: relative;
        transition: all 0.3s;

        &:hover {
          border-color: #008DDC;
        }

        &.selected {
          border: 1px solid #008DDC;
        }

        .iconWrapper {
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 16px;

          .icon {
            max-width: 100%;
            max-height: 100%;
            object-fit: contain;
          }
        }

        .optionTitle {
          font-size: 16px;
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
        }

        .selectedIndicator {
          position: absolute;
          right: 0;
          bottom: 0;
          width: 24px;
          height: 22px;
          background: #008DDC;
          border-top-left-radius: 12px;
          border-bottom-right-radius: 12px;
          display: flex;
          align-items: center;
          justify-content: center;

          .checkmark {
            width: 14px;
            height: 10px;
            border: 2px solid #fff;
            border-top: none;
            border-right: none;
            transform: rotate(-45deg);
            margin-top: -2px;
          }
        }
      }
    }
    .nav-path-ui{
      padding-top: 8px;
      padding-bottom: 4px;
    }
  }

}

.pass-apply-iframe-mask {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  z-index: 1000;
  background-color: rgba(0, 0, 0, 0.45);
}
.pass-apply-iframe {
  width: 100%;
  position: relative;
  z-index: 1001;
}
.card {
  max-width: 800px;
  margin: 0 auto;
}

.sectionTitle {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 500;
  color: rgba(0, 0, 0, 0.85);
}

.indicator {
  width: 5px;
  height: 16px;
  background: #008DDC;
  border-radius: 3px;
  margin-right: 16px;
}

.categoryWrapper {
  margin-top: 16px;
  padding: 0 16px;

  :global {
    .ant-radio-wrapper {
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);

      &:hover {
        .ant-radio {
          border-color: #008DDC;
        }
      }

      .ant-radio-checked {
        .ant-radio-inner {
          border-color: #008DDC;
          &::after {
            background-color: #008DDC;
          }
        }
      }
    }
  }
}

.radioGroup {
  width: 100%;
}

.formWrapper {
  padding: 0 16px;
}

.formRow {
  display: flex;
  gap: 24px;
  margin-bottom: 24px;

  .formItem {
    flex: 1;
    margin-bottom: 0;

    :global {
      .ant-form-item-required::before {
        display: none !important;
      }

      .ant-form-item-label {
        label {
          color: rgba(0, 0, 0, 0.85);
          font-size: 14px;
          
          &.ant-form-item-required::after {
            display: inline-block;
            margin-left: 4px;
            color: #ff4d4f;
            font-size: 14px;
            font-family: SimSun, sans-serif;
            line-height: 1;
            content: '*';
          }
        }
      }

      .ant-input,
      .ant-select-selector {
        border-radius: 4px;
        
        &:hover,
        &:focus {
          border-color: #008DDC;
        }
      }

      .ant-select-focused .ant-select-selector {
        border-color: #008DDC !important;
        box-shadow: 0 0 0 2px rgba(0, 141, 220, 0.2);
      }
    }
  }
}

.remarkItem {
  :global {
    .ant-input {
      border-radius: 4px;
      resize: none;
      
      &:hover,
      &:focus {
        border-color: #008DDC;
      }
    }
  }
}

.textarea {
  width: 100%;
}

.select {
  width: 100%;
}

.resourceWrapper {
  padding: 0 16px;
}

.modeButtons {
  .ant-btn {
    min-width: 100px;
    border-radius: 4px;
    
    &.ant-btn-primary {
      background-color: #008DDC;
      border-color: #008DDC;
    }
  }
}

.specTypeGroup {
  margin-bottom: 16px;
}

.specList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  margin-top: 16px;
}

.specItem {
  height: 80px;
  border: 1px solid #E8E8E8;
  border-radius: 4px;
  padding: 12px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.3s;

  &:hover {
    border-color: #008DDC;
  }

  &.selectedSpec {
    border-color: #008DDC;
    background: rgba(0, 141, 220, 0.05);
  }
}

.specContent {
  flex: 1;
  text-align: center;
}

.specDivider {
  width: 1px;
  height: 40px;
  background: #E8E8E8;
  margin: 0 12px;
}

.specTitle {
  color: rgba(0, 0, 0, 0.45);
  font-size: 12px;
  margin-bottom: 4px;
}

.specValue {
  color: rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 500;
}

.storageConfig {
  display: flex;
  gap: 16px;
  align-items: center;
}

.resourceEvaluation {
  margin-top: 24px;
  background: #ffffff;
  padding: 16px;
  border-radius: 4px;
}

.resourceTitle {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 16px;
}

.resourceMetrics {
  .metricItem {
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .metricLabel {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    margin-bottom: 8px;
  }
}

:global {
  .ant-progress-text {
    color: rgba(0, 0, 0, 0.65);
  }
}

.serviceWrapper {
  padding: 0 16px;
}

.logSettings {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;

  .settingLabel {
    margin-right: 24px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.advancedSection {
  margin-top: 24px;
  border-radius: 4px;
  background: #fff;
}

.advancedHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;

  .arrow {
    transition: transform 0.3s;
    
    &.expanded {
      transform: rotate(180deg);
    }
  }
}

.advancedContent {
  padding-top: 16px;
}

.advancedGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
}

.advancedItem {
  display: flex;
  align-items: center;
  gap: 8px;

  span {
    color: rgba(0, 0, 0, 0.85);
  }
}

:global {
  .ant-switch {
    background-color: rgba(0, 0, 0, 0.25);
    
    &-checked {
      background-color: #008DDC;
    }
  }
} 