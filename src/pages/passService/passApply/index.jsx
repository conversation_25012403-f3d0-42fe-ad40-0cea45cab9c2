import React, { useState, useRef, useEffect } from 'react';
import { Space, Radio, Form, Input, Select, Button, Progress, Switch, Spin } from 'antd';
import { DownOutlined, LeftOutlined } from '@ant-design/icons';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import classNames from 'classnames';
import {
  getMiddlewareVersion,
  CONFIG_MIDDLEWARE,
  API_MIDDLEWARE,
} from "@/services";
import { history, useParams } from "umi";
import './index.less';
import ApplyFrame from './index_new';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId;

const { TextArea } = Input;

const PassApply = () => {

  const { type, id, chartName } = useParams();
  const [middlewareInfo, setMiddlewareInfo] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // 不展示info信息
    return;
    const fetchInfo = async () => {
      try {
        const versionRes = await getMiddlewareVersion({
          clusterId: CLUSTER_ID,
          type: type,
        });
        const middlewareInfo = versionRes.filter((item) => item.id == id)[0];
        setMiddlewareInfo(middlewareInfo);
      } catch (error) {}
    };

    setLoading(true);
    fetchInfo().finally(() => {
      setLoading(false)
    });
  }, []);

  const [selectedRegion, setSelectedRegion] = useState('changzhou');
  const [selectedEnv, setSelectedEnv] = useState('dev');
  const [category, setCategory] = useState('container');
  const [deployMode, setDeployMode] = useState('oneToOne');
  const [specType, setSpecType] = useState('standard');
  const [selectedSpec, setSelectedSpec] = useState('2core4g');
  const [advancedVisible, setAdvancedVisible] = useState(false);
  const [logSettings, setLogSettings] = useState({
    fileLog: true,
    standardLog: false,
    auditLog: false
  });
  const [advancedSettings, setAdvancedSettings] = useState({
    dataShard: false,
    customPort: false,
    containerConfig: false,
    networkSolution: false,
    loadBalancing: false
  });
  const [activeNav, setActiveNav] = useState('region');

  // 添加区域引用
  const regionRef = useRef(null);
  const envRef = useRef(null);
  const basicInfoRef = useRef(null);
  const resourceRef = useRef(null);
  const serviceRef = useRef(null);
  const [form] = Form.useForm();

  const regions = [
    {
      key: 'changzhou',
      title: '常州总部',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/7cf1fa809244daf2d0ec49b04266050f.png'
    },
    {
      key: 'yancheng',
      title: '盐城地区',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/8f667d0761cf828604e7c4b9b413906e.png'
    },
    {
      key: 'overseas',
      title: '海外基地',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/374cebd2eee85750e44fd2b7649e5f46.png'
    }
  ];

  const environments = [
    {
      key: 'dev',
      title: '开发环境',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/d973d658b3ccc6448e7263f776e6ba9e.png'
    },
    {
      key: 'test',
      title: '测试环境',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/b7bd7ea7347b06d753cce0caf6995c72.png'
    },
    {
      key: 'uat',
      title: 'UAT环境',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/d973d658b3ccc6448e7263f776e6ba9e.png'
    },
    {
      key: 'prod',
      title: '生产环境',
      icon: 'https://image-resource-nc.mastergo.com/116639797449879/116639797449881/d973d658b3ccc6448e7263f776e6ba9e.png'
    }
  ];

  // 修改导航项配置
  const navItems = [
    { key: 'region', title: '区域选择', ref: regionRef, icon: '●' },
    { key: 'env', title: '环境选择', ref: envRef, icon: '●' },
    { key: 'basic', title: '基础信息填写', ref: basicInfoRef, icon: '●' },
    { key: 'resource', title: '资源配置', ref: resourceRef, icon: '●' },
    { key: 'service', title: '服务配置', ref: serviceRef, icon: '●' },
  ];

  // 修改滚动处理函数
  const scrollToSection = (ref, key) => {
    ref.current?.scrollIntoView({ behavior: 'smooth' });
    setActiveNav(key);
  };

  const renderHeader = () => (
    <Spin spinning={loading}>
    <div className="header dealPadding">
      <div className="logoWrapper">
          <img src={ `${API_MIDDLEWARE}/images/middleware/${middlewareInfo?.imagePath}`} alt={type} className="logo" />
      </div>
      <div>
        <div className="titleWrapper">
          <span className="mainTitle">{middlewareInfo?.aliasName || middlewareInfo?.name || type}</span>
          <span className="subTitle">（{middlewareInfo?.description}）</span>
        </div>
        <div className="manualLink">操作手册</div>
      </div>
    </div>
    </Spin>
  );

  const renderSection = (title, items, selectedKey, onSelect, ref) => (
    <div className="section" ref={ref}>
      <div className="sectionTitle">
        <div className="titleIndicator" />
        <span>{title}</span>
      </div>
      <Space size={16} className="optionsWrapper">
        {items.map(item => (
          <div
            key={item.key}
            className={classNames('optionCard', {
              'selected': selectedKey === item.key
            })}
            onClick={() => onSelect(item.key)}
          >
            <div className="iconWrapper">
              <img src={item.icon} alt={item.title} className="icon" />
            </div>
            <div className="optionTitle">{item.title}</div>
            {selectedKey === item.key && (
              <div className="selectedIndicator">
                <div className="checkmark" />
              </div>
            )}
          </div>
        ))}
      </Space>
    </div>
  );

  // 渲染分类选择部分
  const renderCategory = () => (
    <div className="section" ref={basicInfoRef}>
      <div className="sectionTitle">
        <div className="titleIndicator" />
        <span>分类</span>
      </div>
      <div className="categoryWrapper">
        <Radio.Group
          value={category}
          onChange={e => setCategory(e.target.value)}
          className="radioGroup"
        >
          <Space direction="vertical" size={12}>
            <Radio value="container">容器中间件（默认）</Radio>
            <Radio value="virtual">虚拟机中间件</Radio>
          </Space>
        </Radio.Group>
      </div>
    </div>
  );

  // 模拟集群选项数据
  const clusterOptions = [
    { label: 'XXXX集群/middleware', value: 'middleware' },
    // 可以根据实际需求添加更多选项
  ];

  // 渲染基础信息部分
  const renderBasicInfo = () => (
    <div className="section" ref={basicInfoRef}>
      <div className="sectionTitle">
        <div className="titleIndicator" />
        <span>基础信息</span>
      </div>
      <div className="formWrapper">
        <Form
          form={form}
          layout="vertical"
          requiredMark="optional"
        >
          <div className="formRow">
            <Form.Item
              label="应用系统名称"
              name="appSystemName"
              className="formItem"
              required
            >
              <Select
                placeholder="请选择分类"
                className="select"
                options={[
                  { label: '选项1', value: 'option1' },
                  { label: '选项2', value: 'option2' },
                ]}
              />
            </Form.Item>
            <Form.Item
              label="集群"
              name="cluster"
              className="formItem"
              required
            >
              <Select
                placeholder="XXXX集群/middleware"
                className="select"
                options={clusterOptions}
              />
            </Form.Item>
          </div>

          <div className="formRow">
            <Form.Item
              label="PaaS服务名称"
              name="paasServiceName"
              className="formItem"
              required
            >
              <Input placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="显示名称"
              name="displayName"
              className="formItem"
            >
              <Input placeholder="请输入" />
            </Form.Item>
          </div>

          <Form.Item
            label="备注"
            name="remark"
            className="remarkItem"
          >
            <TextArea
              placeholder="请输入应用描述"
              rows={4}
              className="textarea"
            />
          </Form.Item>
        </Form>
      </div>
    </div>
  );

  // 渲染资源配置部分
  const renderResourceConfig = () => {
    const specs = [
      { cpu: '2 Core', memory: '4 Gi', key: '2core4g' },
      { cpu: '4 Core', memory: '8 Gi', key: '4core8g' },
      { cpu: '4 Core', memory: '16 Gi', key: '4core16g' },
      { cpu: '8 Core', memory: '16 Gi', key: '8core16g' },
      { cpu: '8 Core', memory: '32 Gi', key: '8core32g' },
      { cpu: '16 Core', memory: '64 Gi', key: '16core64g' },
    ];

    return (
      <div className="section" ref={resourceRef}>
        <div className="sectionTitle">
          <div className="titleIndicator" />
          <span>资源配置</span>
        </div>
        <div className="resourceWrapper">
          <Form layout="vertical" requiredMark="optional">
            <Form.Item
              label="镜像仓库"
              required
              className="formItem"
            >
              <Select placeholder="请选择分类">
                <Select.Option value="default">默认镜像仓库</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              label="版本"
              required
              className="formItem"
            >
              <Select placeholder="16.x">
                <Select.Option value="16">16.1.x</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item label="模式" required>
              <Space className="modeButtons">
                <Button
                  type={deployMode === 'oneToOne' ? 'primary' : 'default'}
                  onClick={() => setDeployMode('oneToOne')}
                >
                  一主一从
                </Button>
                <Button
                  type={deployMode === 'oneToMany' ? 'primary' : 'default'}
                  onClick={() => setDeployMode('oneToMany')}
                >
                  一主多从
                </Button>
                <Button
                  type={deployMode === 'standalone' ? 'primary' : 'default'}
                  onClick={() => setDeployMode('standalone')}
                >
                  单实例
                </Button>
              </Space>
            </Form.Item>

            <Form.Item label="节点规格" required>
              <Radio.Group
                value={specType}
                onChange={e => setSpecType(e.target.value)}
                className="specTypeGroup"
              >
                <Radio value="standard">通用规格</Radio>
                <Radio value="custom">自定义</Radio>
              </Radio.Group>

              {specType === 'standard' && (
                <div className="specList">
                  {specs.map(spec => (
                    <div
                      key={spec.key}
                      className={classNames('specItem', {
                        'selectedSpec': selectedSpec === spec.key
                      })}
                      onClick={() => setSelectedSpec(spec.key)}
                    >
                      <div className="specContent">
                        <div className="specTitle">CPU</div>
                        <div className="specValue">{spec.cpu}</div>
                      </div>
                      <div className="specDivider" />
                      <div className="specContent">
                        <div className="specTitle">内存</div>
                        <div className="specValue">{spec.memory}</div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </Form.Item>

            <Form.Item label="存储配置" required>
              <div className="storageConfig">
                <Select defaultValue="default" style={{ width: 200 }}>
                  <Select.Option value="default">请选择存储</Select.Option>
                </Select>
                <Input
                  placeholder="请输入存储配额大小"
                  suffix="GB"
                  style={{ width: 200 }}
                />
              </div>
            </Form.Item>

            <div className="resourceEvaluation">
              <div className="resourceTitle">当前规格预计使用资源</div>
              <div className="resourceMetrics">
                <div className="metricItem">
                  <div className="metricLabel">CPU</div>
                  <Progress
                    percent={66}
                    strokeColor="#52C41A"
                    format={percent => `${ 66.0 } / 100.0 Core`}
                  />
                </div>
                <div className="metricItem">
                  <div className="metricLabel">内存</div>
                  <Progress
                    percent={66}
                    strokeColor="#52C41A"
                    format={percent => `66.0 / 100.0 GB`}
                  />
                </div>
                <div className="metricItem">
                  <div className="metricLabel">存储</div>
                  <Progress
                    percent={66}
                    strokeColor="#52C41A"
                    format={percent => `66 / 100 GB`}
                  />
                </div>
              </div>
            </div>
          </Form>
        </div>
      </div>
    );
  };

  // 渲染服务配置部分
  const renderServiceConfig = () => (
    <div className="section" ref={serviceRef}>
      <div className="sectionTitle">
        <div className="titleIndicator" />
        <span>服务配置</span>
      </div>
      <div className="serviceWrapper">
        <Form layout="vertical" requiredMark="optional">
          <div className="formRow">
            <Form.Item
              label="密码"
              required
              className="formItem"
            >
              <Input.Password placeholder="请输入" />
            </Form.Item>
            <Form.Item
              label="确认认证方式"
              className="formItem"
            >
              <Select placeholder="请选择">
                <Select.Option value="password">密码认证</Select.Option>
                <Select.Option value="key">密钥认证</Select.Option>
              </Select>
            </Form.Item>
          </div>

          <Form.Item label="启用情况">
            <div className="logSettings">
              <Switch
                checked={logSettings.fileLog}
                onChange={(checked) => setLogSettings(prev => ({ ...prev, fileLog: checked }))}
              />
              <span className="settingLabel">日志文件收集</span>

              <Switch
                checked={logSettings.standardLog}
                onChange={(checked) => setLogSettings(prev => ({ ...prev, standardLog: checked }))}
              />
              <span className="settingLabel">标准日志收集</span>

              <Switch
                checked={logSettings.auditLog}
                onChange={(checked) => setLogSettings(prev => ({ ...prev, auditLog: checked }))}
              />
              <span className="settingLabel">审计日志收集</span>
            </div>
          </Form.Item>

          <div className="advancedSection">
            <div
              className="advancedHeader"
              onClick={() => setAdvancedVisible(!advancedVisible)}
            >
              <span>高级配置</span>
              <DownOutlined
                className={classNames('arrow', {
                  'expanded': advancedVisible
                })}
              />
            </div>

            {advancedVisible && (
              <div className="advancedContent">
                <div className="advancedGrid">
                  <div className="advancedItem">
                    <Switch
                      checked={advancedSettings.dataShard}
                      onChange={(checked) => setAdvancedSettings(prev => ({ ...prev, dataShard: checked }))}
                    />
                    <span>数据分盘</span>
                  </div>
                  <div className="advancedItem">
                    <Switch
                      checked={advancedSettings.customPort}
                      onChange={(checked) => setAdvancedSettings(prev => ({ ...prev, customPort: checked }))}
                    />
                    <span>自定义窗口</span>
                  </div>
                  <div className="advancedItem">
                    <Switch
                      checked={advancedSettings.containerConfig}
                      onChange={(checked) => setAdvancedSettings(prev => ({ ...prev, containerConfig: checked }))}
                    />
                    <span>容器配置</span>
                  </div>
                  <div className="advancedItem">
                    <Switch
                      checked={advancedSettings.networkSolution}
                      onChange={(checked) => setAdvancedSettings(prev => ({ ...prev, networkSolution: checked }))}
                    />
                    <span>网络方案（容器主机网络）</span>
                  </div>
                  <div className="advancedItem">
                    <Switch
                      checked={advancedSettings.loadBalancing}
                      onChange={(checked) => setAdvancedSettings(prev => ({ ...prev, loadBalancing: checked }))}
                    />
                    <span>调度策略</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </Form>
      </div>
    </div>

  );

  const handleBack = () => {
    const path = history.location.pathname.replace('/detail/apply', '/detail');
    history.push(path)
  };
  const linkList = [
    {
      href: '/#/productService',
      title: '产品与服务'
    },
    {
      href: `/#/passService/detail/${type}/${id}/${type}`,
      title: `${type}详情`
    },
    {
      // href: `/#/passService/detail/apply/${type}/${id}}/${type}`,
      title: `${type}申请`
    },
  ]

  return (
    <div className="pass-apply-namespace">
      <div className="passApply">
        {/* 头部导航 */}
          <BreadcrumbNav list={linkList} isNeedBack />
        <div className="container ">
          {/* {renderHeader()} */}
          <div className="mainContent dealPadding" style={{paddingTop:0,paddingBottom:0}}>
            <ApplyFrame  goBack={handleBack} />
            {/* <Affix offsetTop={0}>
              <div className="sideNav">
                <div className="navTitle">操作导航</div>
                {navItems.map(item => (
                  <div
                    key={item.key}
                    className={classNames('navItem', {
                      'activeNav': activeNav === item.key
                    })}
                    onClick={() => scrollToSection(item.ref, item.key)}
                  >
                    <span className={classNames('navIcon', {
                      'activeIcon': activeNav === item.key
                    })}>
                      {item.icon}
                    </span>
                    {item.title}
                  </div>
                ))}
              </div>
            </Affix>
            <div className="content">
              <Affix offsetTop={0}>
              {renderSection('区域选择', regions, selectedRegion, setSelectedRegion, regionRef)}
              {renderSection('环境选择', environments, selectedEnv, setSelectedEnv, envRef)}
              {renderCategory()}
              {renderBasicInfo()}
              {renderResourceConfig()}
              {renderServiceConfig()}
              </Affix>
            </div> */}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PassApply;
