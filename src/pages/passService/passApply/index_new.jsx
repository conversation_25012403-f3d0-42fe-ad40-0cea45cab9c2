import { message, notification, Button } from "antd";
import React, { useEffect, useState } from "react";
import { useParams, history } from "umi";

const simple = true;
const ApplyFrame = ({ goBack }) => {
  const { type } = useParams();
  const onApplyFinish = (flag, bindData) => {
    if (bindData) {
      if (!flag) return;
      if(bindData.pushPath) {
        history.push(bindData.pushPath)
        return
      }
      if (simple) {
        message.success("已提交中间件申请，请前往审批中心-我的提交页面查看进度！");
      } else {
        const appId = bindData?.appId;
        const key = "apply_success_to_jump";
        notification.success({
          duration: 5,
          message: "已提交中间件申请",
          description: "请前往审批中心-我的提交页面查看进度！",
          btn: (
            <Button
              type="primary"
              onClick={() => {
                return new Promise(() => {
                  notification.destroy(key);
                  if (appId) {
                    history.push(
                      "/kepler-webpage/svc/integrationService/applicationIntegration/config/" +
                        appId
                    );
                  }
                });
              }}
            >
              前往
            </Button>
          ),
          key,
        });
      }
    }
    goBack?.();
  };

  const [showMask, setShowMask] = useState(false)
  useEffect(() => {
    window._passApplyFinish = onApplyFinish;
    window._passApplyMask = setShowMask;
    return () => {
      try {
        delete window._passApplyFinish;
        delete window._passApplyMask;
      } catch (error) {}
    };
  }, []);
  return (
    <>
      {showMask && <div className="pass-apply-iframe-mask"></div>}
      <iframe
        className="pass-apply-iframe"
        src={`/#/kepler-webpage/svc/middlewareService/apply/${(
          type || ""
        ).toLowerCase()}/${type}`}
      />
    </>
  );
};

export default ApplyFrame;
