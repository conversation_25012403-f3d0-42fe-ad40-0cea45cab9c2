// import React, { useEffect, useState } from 'react';
// import { useParams, history } from 'umi';
// import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
// import CodeMirror from '@uiw/react-codemirror';
// import { javascript } from '@codemirror/lang-javascript';
// import { yaml } from '@codemirror/lang-yaml';
// import ParamsSetDrawer from '../passParamsSet';
// import './index.less';
// import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE } from '@/services';
// import { MiddlewareType } from '@/utils/const';
// import { Tabs } from 'antd';
// const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

// const STATUS_CONFIG = {
//   0: {
//     icon: LoadingOutlined,
//     color: "#D1D5D9",
//     text: "安装中"
//   },
//   1: {
//     icon: CheckCircleFilled,
//     color: "#1DC11D",
//     text: "运行正常"
//   },
//   2: {
//     icon: MinusCircleFilled,
//     color: "#FAC800",
//     text: "待安装"
//   },
//   3: {
//     icon: ExclamationCircleOutlined,
//     color: "#D93026",
//     text: "不可用"
//   },
//   4: {
//     icon: ExclamationCircleFilled,
//     color: "#faad14",
//     text: "运行异常"
//   }
// };

// const iconStyle = {
//   marginRight: "6px",
//   fontSize: "12px",
//   verticalAlign: "middle"
// };

// const MessageComponentDetail = () => {
//   const location = useParams();
//   // 从路由中获取 type 和 id 参数
//   const { id } = location || {};

//   const [middlewareInfo, setMiddlewareInfo] = useState({
//     id: 22,
//     name: "postgresql",
//     aliasName: "PostgreSQL",
//     description: "CloudDB PostgreSQL数据库",
//     type: "db",
//     version: "16.4,16.1,14.13,14.7,14.2,13.8,13.6,13.5,12.10,11.15",
//     image: null,
//     imagePath: "postgresql-2.5.1.svg",
//     status: null,
//     createTime: "2025-01-14 11:30:37",
//     versionStatus: "now",
//     chartName: "postgresql",
//     chartVersion: "2.5.1",
//     grafanaId: null,
//     replicas: null,
//     replicasStatus: null,
//     middlewares: null,
//     official: true,
//     vendor: "harmonycloud",
//     enable: null,
//     withMiddleware: null
//   });
//   const [middlewareMeta, setMiddlewareMeta] = useState({
//     "appName": "PostgreSQL",
//     "categoryName": "db",
//     "vendor": "谐云科技",
//     "usageCount": 49,
//     "owner": null,
//     "lastUpdateTime": "2025-01-14 11:30:37",
//     "description": "CloudDB PostgreSQL数据库",
//     "questionYaml": null
// });

//   const [selectedFile, setSelectedFile] = useState(null);
//   const [fileContent, setFileContent] = useState(
//     '<packaging>pom</packaging><organization><name>harmony cloud</name><url>https://www.harmonycloud.cn/</url></organization>');
//   const [fileTree, setFileTree] = useState({});
//   const [paramsSetVisible, setParamsSetVisible] = useState(false);
//   const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
// // //   // 获取中间件应用列表
// //   useEffect(() => {
// //     const fetchData = async () => {
// //       try {
// //         const versionRes = await getMiddlewareVersion({ 
// //           clusterId: CLUSTER_ID, 
// //           type: '' 
// //         });
        
// //         const middlewareInfo = versionRes.filter(item => item.id == id)[0];
// //         setMiddlewareInfo(middlewareInfo);
// //         const metaRes = await getMiddlewareMeta({ 
// //           clusterId: CLUSTER_ID, 
// //           type: '',
// //           chartVersion: middlewareInfo.chartVersion 
// //         });
// //         setMiddlewareMeta(metaRes);
// //         const operatorStatusRes = await getMiddlewareOperatorStatuss({ 
// //           clusterId: CLUSTER_ID, 
// //           type: middlewareInfo.name.toLocaleLowerCase(), 
// //         });
// //         setMiddlewareOperatorStatus(operatorStatusRes[0].status);
// //         const fileListRes = await getMiddlewareFileList({ 
// //           clusterId: CLUSTER_ID, 
// //           type: middlewareInfo.chartName, 
// //           chartVersion: middlewareInfo.chartVersion 
// //         });
// //         setFileTree(fileListRes);
// //       } catch (error) {
// //         console.error('获取中间件信息失败:', error);
// //       }
// //     };

// //     fetchData();
// //   }, []);

//   const handleBack = () => {
//     history.back();
//   };

//   // const handleFileClick = async (fileName) => {
//   //   setSelectedFile(fileName);
//   //   const fileContentRes = await getMiddlewareFileContent({ 
//   //     clusterId: CLUSTER_ID, 
//   //     type: middlewareInfo.chartName, 
//   //     chartVersion: middlewareInfo.chartVersion,
//   //     fileName: fileName
//   //   });
//   //   setFileContent(fileContentRes);
//   // };

// //   const renderFileTree = (items) => {
// //     if (!items || !items.children) return null;
// //     return (
// //       <ul className="file-tree">
// //         {items.children.map((item) => (
// //           <li key={item.fileName} className={item.directory ? 'folder' : 'file'}>
// //             <span onClick={() => !item.directory && handleFileClick(item.currentDirectory)}>
// //               {item.fileName}
// //             </span>
// //             {item.directory && renderFileTree(item)}
// //           </li>
// //         ))}
// //       </ul>
// //     );
// //   };

// //   const renderStatus = (value) => {
// //     const config = STATUS_CONFIG[value];
// //     if (!config) return "/";
    
// //     const Icon = config.icon;
// //     return (
// //       <>
// //         <Icon style={{ ...iconStyle, color: config.color }} />
// //         {config.text}
// //       </>
// //     );
// //   };

//   return (
//     <div className="middleware-detail">
//       {/* 头部导航 */}
//       <div className="detail-header dealPadding">
//         <div className="nav-path">
//           <span>产品与服务</span>
//           <span>/</span>
//           <span>共享组件详情</span>
//         </div>
//         <div className="back-button" onClick={handleBack}>
//           <LeftOutlined />
//           <span>返回</span>
//         </div>
//       </div>
//       <div className="info-section dealPadding">
//         <h3 className="section-title">基础信息</h3>
//         <div className="info-grid">
//           <div className="info-item">
//             <span className="label">名称：</span>
//             <span className="value">消息服务调用</span>
//           </div>
//           <div className="info-item">
//             <span className="label">版本：</span>
//             <span className="value">1.0.0</span>
//           </div>
        
//           <div className="info-item">
//             <span className="label">创建人：</span>
//             <span className="value">丁学兰</span>
//           </div>
//           <div className="info-item">
//             <span className="label">创建人时间：</span>
//             <span className="value">2025-02-07 2：50：00</span>
//           </div>
//           <div className="info-item">
//             <span className="label">修改人：</span>
//             <span className="value">丁学兰</span>
//           </div>
//           <div className="info-item">
//             <span className="label">修改时间：</span>
//             <span className="value">2025-02-07 2：50：00</span>
//           </div>
//           <div className="info-item">
//           <span className="label">文档链接：</span>
//           <span className="value">XX</span>
//         </div>
//           <div className="info-item">
//             <span className="label">描述：</span>
//             <span className="value">暂无描述</span>
//           </div>
//         </div>
//       </div>
//       <div className="info-section dealPadding" style={{marginTop:'0px'}}>
//         <h3 className="section-title">使用说明</h3>
//           <div className="file-content" >
           
//           </div>
//       </div>
//       <div className="info-section dealPadding" style={{marginTop:'0px'}}>
//         <h3 className="section-title">Checksums</h3>
//           <div className="file-content" >
//             <CodeMirror
//               value={fileContent}
//               height="200px"
//               theme="none"
//               extensions={[javascript(), yaml()]}
//               editable={false}
//               readOnly={true}
//               basicSetup={{
//                 lineNumbers: true,
//                 foldGutter: true,
//                 highlightActiveLine: true,
//                 dropCursor: true,
//                 allowMultipleSelections: true,
//                 indentOnInput: true,
                
//               }}
//               style={{ overflow: 'auto' }}
//             />
//           </div>
//       </div>
//       <div className="info-section dealPadding" style={{marginTop:'0px'}}>
//       <h3 className="section-title">Checksums</h3>
//         <div className="file-content" >
//           <CodeMirror
//             value={fileContent}
//             height="200px"
//             theme="dark"
//             extensions={[javascript(), yaml()]}
//             editable={false}
//             readOnly={true}
//             basicSetup={{
//               lineNumbers: true,
//               foldGutter: true,
//               highlightActiveLine: true,
//               dropCursor: true,
//               allowMultipleSelections: true,
//               indentOnInput: true,
              
//             }}
//             style={{ overflow: 'auto' }}
//           />
//         </div>
//     </div>
 
     
//     </div>
//   );
// };

// export default MessageComponentDetail; 
import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import ParamsSetDrawer from '../passParamsSet';
import TestComponent from '../testEnv';
import PrdComponent from '../prdEnv';
import './index.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Tabs } from 'antd';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

const MessageComponentDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({
    id: 22,
    name: "postgresql",
    aliasName: "PostgreSQL",
    description: "CloudDB PostgreSQL数据库",
    type: "db",
    version: "16.4,16.1,14.13,14.7,14.2,13.8,13.6,13.5,12.10,11.15",
    image: null,
    imagePath: "postgresql-2.5.1.svg",
    status: null,
    createTime: "2025-01-14 11:30:37",
    versionStatus: "now",
    chartName: "postgresql",
    chartVersion: "2.5.1",
    grafanaId: null,
    replicas: null,
    replicasStatus: null,
    middlewares: null,
    official: true,
    vendor: "harmonycloud",
    enable: null,
    withMiddleware: null
  });
  const [middlewareMeta, setMiddlewareMeta] = useState({
    "appName": "PostgreSQL",
    "categoryName": "db",
    "vendor": "谐云科技",
    "usageCount": 49,
    "owner": null,
    "lastUpdateTime": "2025-01-14 11:30:37",
    "description": "CloudDB PostgreSQL数据库",
    "questionYaml": null
});

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState(
    '<packaging>pom</packaging><organization><name>harmony cloud</name><url>https://www.harmonycloud.cn/</url></organization>');
  const [fileTree, setFileTree] = useState({});
  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
// //   // 获取中间件应用列表
//   useEffect(() => {
//     const fetchData = async () => {
//       try {
//         const versionRes = await getMiddlewareVersion({ 
//           clusterId: CLUSTER_ID, 
//           type: '' 
//         });
        
//         const middlewareInfo = versionRes.filter(item => item.id == id)[0];
//         setMiddlewareInfo(middlewareInfo);
//         const metaRes = await getMiddlewareMeta({ 
//           clusterId: CLUSTER_ID, 
//           type: '',
//           chartVersion: middlewareInfo.chartVersion 
//         });
//         setMiddlewareMeta(metaRes);
//         const operatorStatusRes = await getMiddlewareOperatorStatuss({ 
//           clusterId: CLUSTER_ID, 
//           type: middlewareInfo.name.toLocaleLowerCase(), 
//         });
//         setMiddlewareOperatorStatus(operatorStatusRes[0].status);
//         const fileListRes = await getMiddlewareFileList({ 
//           clusterId: CLUSTER_ID, 
//           type: middlewareInfo.chartName, 
//           chartVersion: middlewareInfo.chartVersion 
//         });
//         setFileTree(fileListRes);
//       } catch (error) {
//         console.error('获取中间件信息失败:', error);
//       }
//     };

//     fetchData();
//   }, []);

  const handleBack = () => {
    history.back();
  };

  // const handleFileClick = async (fileName) => {
  //   setSelectedFile(fileName);
  //   const fileContentRes = await getMiddlewareFileContent({ 
  //     clusterId: CLUSTER_ID, 
  //     type: middlewareInfo.chartName, 
  //     chartVersion: middlewareInfo.chartVersion,
  //     fileName: fileName
  //   });
  //   setFileContent(fileContentRes);
  // };

//   const renderFileTree = (items) => {
//     if (!items || !items.children) return null;
//     return (
//       <ul className="file-tree">
//         {items.children.map((item) => (
//           <li key={item.fileName} className={item.directory ? 'folder' : 'file'}>
//             <span onClick={() => !item.directory && handleFileClick(item.currentDirectory)}>
//               {item.fileName}
//             </span>
//             {item.directory && renderFileTree(item)}
//           </li>
//         ))}
//       </ul>
//     );
//   };

//   const renderStatus = (value) => {
//     const config = STATUS_CONFIG[value];
//     if (!config) return "/";
    
//     const Icon = config.icon;
//     return (
//       <>
//         <Icon style={{ ...iconStyle, color: config.color }} />
//         {config.text}
//       </>
//     );
//   };
const linkList = [
  {
    href: '/#/productService',
    title: '产品与服务'
  },
  {
  
    title: `短信服务调用`
  },
]
  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding">
        <BreadcrumbNav list={linkList} isNeedBack/>
       
      </div>
      <div className="info-section dealPadding">
        <h3 className="section-title">基础信息</h3>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">名称：</span>
            <span className="value">短信服务调用</span>
          </div>
          <div className="info-item">
            <span className="label">版本：</span>
            <span className="value">1.0.0</span>
          </div>
        
          <div className="info-item">
            <span className="label">创建人：</span>
            <span className="value">吴国荣</span>
          </div>
          <div className="info-item">
            <span className="label">创建时间：</span>
            <span className="value">2025-02-07 2:50:00</span>
          </div>
          <div className="info-item">
            <span className="label">修改人：</span>
            <span className="value">吴国荣</span>
          </div>
          <div className="info-item">
            <span className="label">修改时间：</span>
            <span className="value">2025-02-07 2:50:00</span>
          </div>
          <div className="info-item">
            <span className="label">描述：</span>
            <span className="value">暂无描述</span>
          </div>
        </div>
      </div>
      <div className="info-section dealPadding" style={{marginTop:'0px'}}>
        <h3 className="section-title">使用说明</h3>
          <div className="file-content" >
            <Tabs
                defaultActiveKey="1"
                items={[
                    {
                    label: `测试环境`,
                    key: '1',
                    children: <TestComponent />,
                    },
                    {
                    label: `生产环境`,
                    key: '2',
                    children: <PrdComponent />,
                    }
                ]}
                />
          </div>
      </div>
     
    </div>
  );
};

export default MessageComponentDetail; 