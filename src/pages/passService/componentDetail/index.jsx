import React, { useEffect, useState } from 'react';
import { useParams} from 'umi';
import { LeftOutlined} from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import ParamsSetDrawer from '../passParamsSet';
import './index.less';
import CommenComponentDetail from '../commenComponentDetail';
import MessageComponentDetail from '../messageDetail';
import ApplicationAttachmentsComponentDetail from '../ApplicationAttachmentsComponentDetail';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE } from '@/services';

const ComponentDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { type,id } = location || {};
    useEffect(() => {
    console.log(type)
  }, []);
  return (
    <div className="middleware-detail">
      {
        type === '短信服务调用'?<MessageComponentDetail />:(type === '应用附件服务调用'?<ApplicationAttachmentsComponentDetail />:<CommenComponentDetail />)
      }
    </div>
  );
};

export default ComponentDetail; 