import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import ParamsSetDrawer from '../passParamsSet';
import './index.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE, API_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Spin, notification } from 'antd';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

// 工具函数：无数据时显示 /
const displayValue = (value) => {
  return value === null || value === undefined || value === '' ? '/' : value;
};

const PassSerDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { chartName, type, id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({
  });
  const [middlewareMeta, setMiddlewareMeta] = useState({
  });

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState('');
  const [fileTree, setFileTree] = useState({});
  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
  const [loading, setLoading] = useState(false);
  // 获取中间件应用列表
  useEffect(() => {
    const fetchData = async () => {
      try {
        const versionRes = await getMiddlewareVersion({
          clusterId: CLUSTER_ID,
          type: type,
        });
        const middlewareInfo = versionRes.filter((item) => item.id == id)[0];
        setMiddlewareInfo(middlewareInfo);
        const [metaRes, operatorStatusRes, fileListRes] =
          await Promise.allSettled([
            getMiddlewareMeta({
              clusterId: CLUSTER_ID,
              type: type,
              chartVersion: middlewareInfo?.chartVersion,
            }),
            getMiddlewareOperatorStatuss({
              clusterId: CLUSTER_ID,
              type: middlewareInfo?.name.toLocaleLowerCase(),
            }),
            getMiddlewareFileList({
              clusterId: CLUSTER_ID,
              type: middlewareInfo?.chartName,
              chartVersion: middlewareInfo?.chartVersion,
            }),
          ]).then((ans) => ans?.map((a) => a.value) || []);
        setMiddlewareMeta(metaRes);
        setMiddlewareOperatorStatus(operatorStatusRes[0].status);
        setFileTree(fileListRes);
      } catch (error) {
        console.error("获取中间件信息失败:", error);
      }
    };

    setLoading(true);
    fetchData().finally(() => {
      setLoading(false);
    });
  }, []);

  useEffect(() => {
    return () => {
      notification.destroy('apply_success_to_jump');
    }
  }, [])
  const handleBack = () => {
    history.back();
  };

  const handleFileClick = async (fileName) => {
    setSelectedFile(fileName);
    const fileContentRes = await getMiddlewareFileContent({
      clusterId: CLUSTER_ID,
      type: middlewareInfo?.chartName,
      chartVersion: middlewareInfo?.chartVersion,
      fileName: fileName
    });
    setFileContent(fileContentRes);
  };

  const renderFileTree = (items) => {
    if (!items || !items.children) return null;
    return (
      <ul className="file-tree">
        {items.children.map((item) => (
          <li key={item.fileName} className={item.directory ? 'folder' : 'file'}>
            <span onClick={() => !item.directory && handleFileClick(item.currentDirectory)}>
              {item.fileName}
            </span>
            {item.directory && renderFileTree(item)}
          </li>
        ))}
      </ul>
    );
  };

  const renderStatus = (value) => {
    const config = STATUS_CONFIG[value];
    if (!config) return "/";

    const Icon = config.icon;
    return (
      <>
        <Icon style={{ ...iconStyle, color: config.color }} />
        {config.text}
      </>
    );
  };
  const getSrc = () => {
    switch (type) {
      case 'Kafka':
        return '/readpage/kafka.html';
      case 'Redis':
        return '/readpage/redis.html';
      case 'MySQL':
        return '/readpage/mysql.html';
      case 'Mongodb':
        return '/readpage/mongodb.html';
      case 'Elasticsearch':
        return '/readpage/es.html';
      case 'ZooKeeper':
        return '/readpage/zookeeper.html';
      case 'RocketMQ':
        return '/readpage/RocketMQ.html';
      case 'Rabbitmq':
        return '/readpage/Rabbitmq.html';
      case 'PostgreSQL':
        return '/readpage/PostgreSQL.html';
      case 'Nacos':
        return '/readpage/nacos.html';
      case 'Xxljob':
        return '/readpage/xxl-job.html';
      case 'Sentinel-dashboard':
        return '/readpage/sentinel.html';
      default:
        break;
    }
    return '';
  };
  const linkList = [
    {
      href: '/#/productService',
      title: '产品与服务'
    },
    {
      href: `/#/passService/detail/${type}/${id}/${chartName}`,
      title: `${type}详情`
    },
  ]

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} isNeedBack />
      <Spin spinning={!!loading}>
        {/* 中间件标题区域 */}
        <div className="middleware-header dealPadding">
          <div className="middleware-base-info">
            <div>
              <img src={`${API_MIDDLEWARE}/images/middleware/${middlewareInfo?.imagePath}`} alt={type} className="middleware-logo" />
            </div>
            <div >
              <div className="logo-title">
                <div className="title-wrapper">
                  <span className="title">{middlewareInfo?.aliasName || middlewareInfo?.name || middlewareMeta?.appName || type}</span>
                  <span className="subtitle">({middlewareInfo?.description})</span>
                </div>
              </div>
              <div className="action-buttons">
                <button disabled={!middlewareInfo} className="action-btn primary" onClick={() => history.push(`/passService/detail/apply/${type}/${id}/${middlewareInfo?.chartName}`)}>申请</button>
                {/* <button
                  className="action-btn"
                  onClick={() => setParamsSetVisible(true)}
                >
                  参数选配
                </button>
                <button className="action-btn link">版本管理</button>
                <button className="action-btn link">操作手册</button> */}
              </div>
            </div>

          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section dealPadding">
          <h3 className="section-title">基本信息</h3>
          <div className="info-grid">
            <div className="info-item">
              <span className="label">版本号</span>
              <span className="value api-tags">{
                middlewareInfo?.version ? middlewareInfo?.version.split(',').map((item, index) => <span className="version-tag" title={middlewareInfo?.version}>{item}</span>) : ''
              }</span>
            </div>
            <div className="info-item">
              <span className="label">中间件应用名称</span>
              <span className="value">{displayValue(middlewareInfo?.aliasName || type)}</span>
            </div>
            <div className="info-item">
              <span className="label">中间件类型</span>
              <span className="value">{displayValue(MiddlewareType[middlewareInfo?.type])}</span>
            </div>
            {/* <div className="info-item">
              <span className="label">研发商</span>
              <span className="value">{displayValue(middlewareMeta?.vendor)}</span>
            </div> */}
            <div className="info-item">
              <span className="label">当前控制器状态</span>
              <div className={`status-tag ${middlewareOperatorStatus === 1 ? 'normal' : 'error'}`}>
                {renderStatus(middlewareOperatorStatus)}
              </div>
            </div>
            {/* <div className="info-item">
              <span className="label">使用量</span>
              <span className="value">{displayValue(middlewareMeta?.usageCount)}</span>
            </div> */}
            <div className="info-item">
              <span className="label">中间件介绍</span>
              <span className="value">{displayValue(middlewareInfo?.description)}</span>
            </div>
            <div className="info-item">
              <span className="label">最近更新时间</span>
              <span className="value">{displayValue(middlewareMeta?.lastUpdateTime)}</span>
            </div>
          </div>
        </div>
      </Spin>
      {/* Helm Chart 区域 */}
      {/* <div className="helm-section dealPadding">
        <h3 className="section-title">Helm chart</h3>
        <div className="helm-content">
          <div className="file-explorer">
            <div className="file-tree-container">
              {renderFileTree(fileTree)}
            </div>
            <div className="file-content">
              {selectedFile ? (
                <CodeMirror
                  value={fileContent}
                  height="600px"
                  theme="dark"
                  extensions={[javascript(), yaml()]}
                  editable={false}
                  basicSetup={{
                    lineNumbers: true,
                    foldGutter: true,
                    highlightActiveLine: true,
                    dropCursor: true,
                    allowMultipleSelections: true,
                    indentOnInput: true,
                  }}
                  style={{ overflow: 'auto' }}
                />
              ) : (
                <div className="no-file-selected">
                  请选择要查看的文件
                </div>
              )}
            </div>
          </div>
        </div>
      </div> */}
      {/* 基本信息区域 */}
      <div className="info-section dealPadding">
        <h3 className="section-title">README</h3>
        <div style={{ width: '100%', height: '100%', textAlign: 'center', padding: '0', color: '#aaa', fontSize: 18 }}>
          <iframe src={getSrc()} frameborder="0" id="opera-doc"></iframe>
        </div>
      </div>


      {/* 参数选配抽屉 */}
      <ParamsSetDrawer
        visible={paramsSetVisible}
        onClose={() => setParamsSetVisible(false)}
        chartName={middlewareInfo?.chartName}
        chartVersion={middlewareInfo?.chartVersion}

      />
    </div>
  );
};

export default PassSerDetail; 