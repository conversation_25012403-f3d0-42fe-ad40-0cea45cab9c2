export const passServiceList = [
  {
    "groupCode": "db",
    "groupName": "数据库",
    "middlewareInfoDTOS": [
      {
        "id": 22,
        "name": "PostgreSQL",
        "aliasName": null,
        "description": "CloudDB PostgreSQL数据库",
        "type": "db",
        "version": "16.4,16.1,14.13,14.7,14.2,13.8,13.6,13.5,12.10,11.15",
        "image": null,
        "imagePath": "postgresql-2.5.1.svg",
        "status": 1,
        "createTime": "2025-01-14 11:30:37",
        "versionStatus": null,
        "chartName": "postgresql",
        "chartVersion": "2.5.1",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 25,
        "name": "TongRDS",
        "aliasName": null,
        "description": "A Helm chart for Kubernetes",
        "type": "db",
        "version": "",
        "image": null,
        "imagePath": "tongrds-2.2.2.svg",
        "status": 1,
        "createTime": "2025-02-21 09:36:59",
        "versionStatus": null,
        "chartName": "tongrds",
        "chartVersion": "2.2.2",
        "grafanaId": null,
        "replicas": 3,
        "replicasStatus": null,
        "middlewares": null,
        "official": false,
        "vendor": "other",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 47,
        "name": "Elasticsearch",
        "aliasName": null,
        "description": "CloudDB Elasticsearch数据库",
        "type": "db",
        "version": "6.8.22,7.16.3,7.16.2,8.11.3",
        "image": null,
        "imagePath": "elasticsearch-1.10.0.svg",
        "status": 1,
        "createTime": "2025-03-03 11:30:59",
        "versionStatus": null,
        "chartName": "elasticsearch",
        "chartVersion": "1.10.0",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 50,
        "name": "MySQL",
        "aliasName": null,
        "description": "CloudDB MySQL数据库",
        "type": "db",
        "version": "8.0.39,5.7.44,5.7.35",
        "image": null,
        "imagePath": "mysql-1.13.0.svg",
        "status": 1,
        "createTime": "2025-03-03 13:51:20",
        "versionStatus": null,
        "chartName": "mysql",
        "chartVersion": "1.13.0",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 53,
        "name": "Redis",
        "aliasName": null,
        "description": "CloudDB Redis缓存数据库",
        "type": "db",
        "version": "6.2.6,6.2.11,6.2.14,7.2.4,7.0.12",
        "image": null,
        "imagePath": "redis-2.12.3.svg",
        "status": 1,
        "createTime": "2025-03-03 14:14:11",
        "versionStatus": null,
        "chartName": "redis",
        "chartVersion": "2.12.3",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 94,
        "name": "Polardb",
        "aliasName": null,
        "description": "A Helm chart for Kubernetes",
        "type": "db",
        "version": "",
        "image": null,
        "imagePath": "polardb-0.2.6.svg",
        "status": 1,
        "createTime": "2025-03-28 15:18:29",
        "versionStatus": null,
        "chartName": "polardb",
        "chartVersion": "0.2.6",
        "grafanaId": null,
        "replicas": 0,
        "replicasStatus": null,
        "middlewares": null,
        "official": false,
        "vendor": "other",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 96,
        "name": "Mongodb",
        "aliasName": null,
        "description": "CloudDB MongoDB数据库",
        "type": "db",
        "version": "7.0.8",
        "image": null,
        "imagePath": null,
        "status": 1,
        "createTime": "2025-04-10 17:29:22",
        "versionStatus": null,
        "chartName": "mongodb",
        "chartVersion": "1.3.0-beta.8-fake",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      }
    ],
    "num": 7
  },
  {
    "groupCode": "mq",
    "groupName": "消息队列",
    "middlewareInfoDTOS": [
      {
        "id": 48,
        "name": "Kafka",
        "aliasName": null,
        "description": "CloudDB Kafka消息队列",
        "type": "mq",
        "version": "3.7.0",
        "image": null,
        "imagePath": "kafka-1.11.2.svg",
        "status": 1,
        "createTime": "2025-03-03 11:48:54",
        "versionStatus": null,
        "chartName": "kafka",
        "chartVersion": "1.11.2",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 54,
        "name": "RocketMQ",
        "aliasName": null,
        "description": "CloudDB RocketMQ消息队列",
        "type": "mq",
        "version": "4.9.6,5.2.0,5.3.0",
        "image": null,
        "imagePath": "rocketmq-3.2.0.svg",
        "status": 1,
        "createTime": "2025-03-03 15:32:43",
        "versionStatus": null,
        "chartName": "rocketmq",
        "chartVersion": "3.2.0",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 71,
        "name": "Rabbitmq",
        "aliasName": null,
        "description": "CloudDB RabbitMQ消息队列",
        "type": "mq",
        "version": "3.12.7",
        "image": null,
        "imagePath": "rabbitmq-1.1.1-beta.3.svg",
        "status": 1,
        "createTime": "2025-03-12 18:32:54",
        "versionStatus": null,
        "chartName": "rabbitmq",
        "chartVersion": "1.1.1-beta.3",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      }
    ],
    "num": 3
  },
  {
    "groupCode": "mse",
    "groupName": "微服务引擎",
    "middlewareInfoDTOS": [
      {
        "id": 55,
        "name": "ZooKeeper",
        "aliasName": null,
        "description": "CloudDB Zookeeper数据库",
        "type": "mse",
        "version": "3.7.1",
        "image": null,
        "imagePath": "zookeeper-1.2.0.svg",
        "status": 1,
        "createTime": "2025-03-03 15:42:17",
        "versionStatus": null,
        "chartName": "zookeeper",
        "chartVersion": "1.2.0",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 72,
        "name": "Nacos",
        "aliasName": null,
        "description": "A Helm chart for Kubernetes",
        "type": "mse",
        "version": "2.0,2.4",
        "image": null,
        "imagePath": "nacos-1.3.2-beta.5.svg",
        "status": 1,
        "createTime": "2025-03-12 18:33:18",
        "versionStatus": null,
        "chartName": "nacos",
        "chartVersion": "1.3.2-beta.5",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      }
    ],
    "num": 2
  },
  {
    "groupCode": "storage",
    "groupName": "存储服务",
    "middlewareInfoDTOS": [
      {
        "id": 17,
        "name": "Minio",
        "aliasName": null,
        "description": "Minio by Harmonycloud",
        "type": "storage",
        "version": "",
        "image": null,
        "imagePath": "minio-1.6.0.svg",
        "status": 1,
        "createTime": "2024-12-26 14:19:11",
        "versionStatus": null,
        "chartName": "minio",
        "chartVersion": "1.6.0",
        "grafanaId": null,
        "replicas": 0,
        "replicasStatus": null,
        "middlewares": null,
        "official": false,
        "vendor": "other",
        "enable": null,
        "withMiddleware": null
      }
    ],
    "num": 1
  },
  {
    "groupCode": "other",
    "groupName": "其他",
    "middlewareInfoDTOS": [
      {
        "id": 20,
        "name": "Skywalking",
        "aliasName": null,
        "description": "Helm Chart for Apache SkyWalking",
        "type": "other",
        "version": "1.2.0",
        "image": null,
        "imagePath": "skywalking-4.5.0-1.0.1-beta.3.svg",
        "status": 1,
        "createTime": "2025-01-13 10:17:32",
        "versionStatus": null,
        "chartName": "skywalking",
        "chartVersion": "4.5.0-1.0.1-beta.3",
        "grafanaId": null,
        "replicas": 0,
        "replicasStatus": null,
        "middlewares": null,
        "official": false,
        "vendor": "other",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 21,
        "name": "Logstash",
        "aliasName": null,
        "description": "Official Elastic helm chart for Logstash",
        "type": "other",
        "version": "",
        "image": null,
        "imagePath": "logstash-6.8.22-7-beta.4.svg",
        "status": 1,
        "createTime": "2025-01-14 11:00:48",
        "versionStatus": null,
        "chartName": "logstash",
        "chartVersion": "6.8.22-7-beta.4",
        "grafanaId": null,
        "replicas": 0,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 23,
        "name": "Kibana",
        "aliasName": null,
        "description": "A Helm chart for Kubernetes",
        "type": "other",
        "version": "8.11.3",
        "image": null,
        "imagePath": "kibana-8.11.3-1.0.1-beta.1.svg",
        "status": 1,
        "createTime": "2025-01-14 13:55:27",
        "versionStatus": null,
        "chartName": "kibana",
        "chartVersion": "8.11.3-1.0.1-beta.1",
        "grafanaId": null,
        "replicas": 0,
        "replicasStatus": null,
        "middlewares": null,
        "official": true,
        "vendor": "harmonycloud",
        "enable": null,
        "withMiddleware": null
      },
      {
        "id": 46,
        "name": "Flink",
        "aliasName": null,
        "description": "A Helm chart for the Apache Flink",
        "type": "other",
        "version": "",
        "image": null,
        "imagePath": "flink-1.10.0.svg",
        "status": 1,
        "createTime": "2025-02-25 14:24:41",
        "versionStatus": null,
        "chartName": "flink",
        "chartVersion": "1.10.0",
        "grafanaId": null,
        "replicas": 1,
        "replicasStatus": null,
        "middlewares": null,
        "official": false,
        "vendor": "",
        "enable": null,
        "withMiddleware": null
      }
    ],
    "num": 4
  }
]
