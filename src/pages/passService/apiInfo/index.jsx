import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import './index.less';
import { CONFIG_MIDDLEWARE } from '@/services';
import { Table } from 'antd';


const ApiInfo = ({infos}) => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};
  

 
  return (
    <div className="middleware-detail">
     {
      infos.map((info)=> <div className="info-section" style={{marginTop:'0px'}}>
      <p className='title-step'>{info.title}</p>
      <div className="file-content" >
        <p className='title-step'>请求地址</p>
        <CodeMirror
        value={info.path}
       
        theme="none"
        extensions={[javascript(), yaml()]}
        editable={false}
        readOnly={true}
        basicSetup={{
          lineNumbers: true,
          foldGutter: true,
          highlightActiveLine: true,
          dropCursor: true,
          allowMultipleSelections: true,
          indentOnInput: true,
          
        }}
        style={{ overflow: 'auto' }}
      />
      {
        info.headerData?.length > 0 && <>
        <p className='title-step'>请求头</p>
        <Table
        dataSource={info.headerData} 
        columns={[
          {
          title: '参数名',
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: '参数值',
          dataIndex: 'value',
          key: 'value',
        }
        ]} 
        pagination={false}
        />
        </>
      }
     
     
      {
        info.newParamObj && <>
        <p className='title-step'>请求参数</p>
        <CodeMirror
        value={info.newParamObj}
        theme="none"
        extensions={[javascript(), yaml()]}
        editable={false}
        readOnly={true}
        basicSetup={{
          lineNumbers: true,
          foldGutter: true,
          highlightActiveLine: true,
          dropCursor: true,
          allowMultipleSelections: true,
          indentOnInput: true,
          
        }}
        style={{ overflow: 'auto' }}
        />
        </>
      }
      {
        info.paramData.length > 0 && <>
        <p className='title-step'>请求参数说明</p>
        <Table
        dataSource={info.paramData} 
        columns={[
          {
          title: '参数名',
          dataIndex: 'name',
          key: 'name',
        },
        {
          title: '参数类型',
          dataIndex: 'type',
          key: 'type',
        },
        {
          title: '参数位置',
          dataIndex: 'position',
          key: 'position',
        }
        ]} 
        pagination={false}
        />
        </>
      }
       
      <p className='title-step'>返回结果示例</p>
      <CodeMirror
      value={info.result}
      theme="none"
      extensions={[javascript(), yaml()]}
      editable={false}
      readOnly={true}
      basicSetup={{
        lineNumbers: true,
        foldGutter: true,
        highlightActiveLine: true,
        dropCursor: true,
        allowMultipleSelections: true,
        indentOnInput: true,
        
      }}
      style={{ overflow: 'auto' }}
      />
      <p className='title-step'>状态码说明</p>
      <Table
      dataSource={info.statusData} 
      columns={[
        {
        title: '状态码',
        dataIndex: 'code',
        key: 'code',
      },
      {
        title: '状态码含义',
        dataIndex: 'codeText',
        key: 'codeText',
      },
      {
        title: '状态码说明',
        dataIndex: 'sub',
        key: 'sub',
      }
      ]} 
      pagination={false}
      />
      </div>
    </div>)
     }
 
     
    </div>
  );
};

export default ApiInfo; 