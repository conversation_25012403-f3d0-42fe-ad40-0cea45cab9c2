import React from 'react';
import { Input, Button, Space, Select, Tree } from 'antd';
import { SearchOutlined, CopyOutlined } from '@ant-design/icons';
import './ApiMarket.less';

const ApiMarket = () => {
  const apiList = [
    {
      id: 1,
      name: 'API 名称',
      description: '这是一段描述文字',
      version: 'v1.0.0',
      visits: 1234,
      apiUrl: '/api/v1/example',
      icon: 'https://example.com/icon.png'
    },
    {
      id: 2,
      name: 'PS For 绿效系统',
      version: '1.0.0',
      visits: 4,
      apiUrl: '/ccd386k/v3/znOzOqgV/s/znOzOqgev...',
      description: '智能说明',
      icon: '/path/to/api-icon.svg'
    },
    {
      id: 3,
      name: 'CSP-UAT环境',
      version: '1.0.0',
      visits: 4,
      apiUrl: '/ccd386k/v3/znOzOqgV/s/znOzOqgev...',
      description: '智能说明',
      icon: '/path/to/api-icon.svg'
    },
    {
      id: 4,
      name: '天机-人力资源管理',
      version: '1.0.0',
      visits: 4,
      apiUrl: '/ccd386k/v3/znOzOqgV/s/znOzOqgev...',
      description: '智能说明',
      icon: '/path/to/api-icon.svg'
    },
    {
      id: 5,
      name: 'TMS-物料信息管理',
      version: '1.0.0',
      visits: 6,
      apiUrl: '/ccd386k/v3/znOzOqgV/s/znOzOqgev...',
      description: '智能说明',
      icon: '/path/to/api-icon.svg'
    },
    {
      id: 6,
      name: '欧洲分布式 DEV环境',
      version: '1.0.0',
      visits: 4,
      apiUrl: '/ccd386k/v3/znOzOqgV/s/znOzOqgev...',
      description: '智能说明',
      icon: '/path/to/api-icon.svg'
    }
  ];

  // 导航树数据
  const treeData = [
    {
      title: '智能制造发布组',
      key: 'smart-manufacturing',
      selectable: true,
      className: 'highlight-node',
      children: []
    },
    {
      title: 'PaaS发布组',
      key: 'paas',
      selectable: true,
      children: [
        {
          title: '产品生命周期管理',
          key: 'plm',
          selectable: true
        },
        {
          title: '信息技术',
          key: 'it',
          selectable: true
        },
        {
          title: '人力资源管理',
          key: 'hr',
          selectable: true
        }
      ]
    },
    {
      title: '低代码平台发布组',
      key: 'low-code',
      selectable: true,
      children: []
    },
    {
      title: 'OA发布组',
      key: 'oa',
      selectable: true,
      children: []
    },
    {
      title: 'MHR发布组',
      key: 'mhr',
      selectable: true,
      children: []
    },
    {
      title: 'ITR发布组',
      key: 'itr',
      selectable: true,
      children: []
    }
  ];

  // 添加节点计数
  const getNodeCount = (node) => {
    if (node.key === 'smart-manufacturing') return '(16)';
    if (['paas', 'low-code', 'oa', 'mhr', 'itr'].includes(node.key)) return '(6)';
    return '';
  };

  // 自定义标题渲染
  const titleRender = (nodeData) => {
    const count = getNodeCount(nodeData);
    return (
      <div className="tree-node-content">
        <span className="node-title">{nodeData.title}</span>
        {count && <span className="node-count">{count}</span>}
      </div>
    );
  };

  return (
    <div className="api-market dealPadding">
      <div className="left-sidebar">
        <div className="navigation">
          <div className="navigation-title">
            <span className="nav-indicator"></span>
            <h3>导航</h3>
          </div>
          <div className="search-container">
            <Input 
              placeholder="搜索" 
              className="nav-search"
            />
          </div>
          <Tree
            treeData={treeData}
            defaultSelectedKeys={['paas']}
            defaultExpandedKeys={['paas']}
            titleRender={titleRender}
            className="category-tree"
          />
        </div>
      </div>

      <div className="main-content">
        <div className="search-bar">
          <div className="search-filters">
            <div className="search-item sort-item">
              <span>更新时间</span>
              <div className="sort-arrows">
                <div className="arrow up"></div>
                <div className="arrow down"></div>
              </div>
            </div>
            <div className="search-item sort-item">
              <span>申请量</span>
              <div className="sort-arrows">
                <div className="arrow up"></div>
                <div className="arrow down"></div>
              </div>
            </div>
            <div className="search-inputs">
              <Input 
                placeholder="产品名称" 
                
              />
              <Input 
                placeholder="描述"
                
              />
              <Input 
                placeholder="API编号/路径"
              />
            </div>
          </div>
        </div>

        <div className="api-list">
          {apiList.map((api) => (
            <div key={api.id} className="api-card">
              <div className="card-top">
                <div className="api-logo">
                  <img src={api.icon} alt={api.name} />
                </div>
                <div className="api-title">
                  <h4>{api.name}</h4>
                  <p>{api.description}</p>
                </div>
                <div className="api-status">
                  <Button>已订阅</Button>
                </div>
              </div>

              <div className="card-bottom">
                <div className="info-row">
                  <div className="info-item">
                    <span className="label">版本</span>
                    <span className="value">{api.version}</span>
                  </div>
                  <div className="info-item">
                    <span className="label">应用量</span>
                    <span className="value">{api.visits}</span>
                  </div>
                </div>

                <div className="info-item path-item">
                  <span className="label">路径</span>
                  <div className="path-wrapper">
                    <span className="value">{api.apiUrl}</span>
                    <CopyOutlined />
                  </div>
                </div>

                <div className="info-item">
                  <span className="label">产品说明</span>
                  <span className="value">{api.description}</span>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ApiMarket; 