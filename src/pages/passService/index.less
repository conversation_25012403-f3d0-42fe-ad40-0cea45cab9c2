.pass-service,
.product-service {
  height: 100%;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: center;

  .content-wrapper {
    width: 100%;
    margin: 0 auto;
  }

  .header-content {
    padding-top: 5%;
    margin-top: 0;
  }

  .home-header {
    text-align: left;
    height: 400px;
    width: 100%;
    background-image: url("~@/assets/images/header-prod.svg");
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;

    h1 {
      font-size: 32px;
      margin-bottom: 16px;
      font-weight: 500;
      color: #000;
    }

    .description {
      color: #666;
      font-size: 14px;
      line-height: 1.8;
      width: 50%;
    }
  }

  // 响应式调整
  @media screen and (min-width: 1920px) {
    .dealPadding {
      padding: 24px calc((100vw - 81%) / 2);
    }
  }

  // 响应式调整
  @media screen and (max-width: 1920px) {
    .dealPadding {
      padding: 24px 120px;
    }
  }

  // 响应式调整
  @media screen and (max-width: 1800px) {
    .dealPadding {
      padding: 24px calc((100vw - 81%) / 2);
    }
  }
  // 响应式调整
  @media screen and (max-width: 1700px) {
    .dealPadding {
      padding: 24px calc((100vw - 86%) / 2);
    }
  }

  @media screen and (max-width: 1600px) {
    .dealPadding {
      padding: 24px calc((100vw - 1200px) / 2);
    }
  }

  @media screen and (max-width: 1200px) {
    .dealPadding {
      padding: 24px 40px;
    }
  }

  @media screen and (max-width: 768px) {
    .dealPadding {
      padding: 24px 20px;
    }

    .home-header {
      padding: 24px;

      h1 {
        font-size: 24px;
      }

      .description {
        font-size: 14px;
      }
    }
  }

  .statistics-card-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 20px 0;
    position: relative;
    height: 24px;
  }

  .feature-cards {
    display: flex;
    justify-content: space-around;
    align-items: center;
    position: absolute;
    top: -70px;
    background-color: #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    backdrop-filter: blur(5px);
    cursor: pointer;

    // 响应式调整
    @media screen and (min-width: 1921px) {
      width: 81%;
    }

    // 响应式调整
    @media screen and (max-width: 1920px) {
      width: 87.5%;
    }
    // 响应式调整
    @media screen and (max-width: 1800px) {
      width: 81%;
    }
    // 响应式调整
    @media screen and (max-width: 1700px) {
      width: 86%;
    }
    // 响应式调整
    @media screen and (max-width: 1500px) {
      width: 83%;
    }
    @media screen and (max-width: 1300px) {
      width: 94%;
    }
  }

  .feature-card {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    text-align: left;
    padding: 20px;
    height: 100px;

    &.active {
      h3 {
        color: #1890ff;
        font-weight: 600;
      }

      .feature-icon {
        transform: scale(1.1);
        transition: transform 0.3s ease;
      }
    }

    &:hover {
      transform: translateY(-5px);
      // box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }

  .feature-icon {
    width: 60px;
    height: 60px;
    margin-right: 20px;
  }

  .feature-text {
    display: flex;
    flex-direction: column;
    align-items: flex-start;

    h3 {
      transition: color 0.3s ease;
    }
  }

  .feature-card p {
    margin: 0;
    line-height: 1.5;
    white-space: normal;
  }
}

.bgWhite {
  background-color: #fff;
}
