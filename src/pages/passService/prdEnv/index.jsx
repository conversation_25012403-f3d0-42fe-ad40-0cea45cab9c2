import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import './index.less';
import { CONFIG_MIDDLEWARE } from '@/services';
import { Table } from 'antd';

const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

const PrdComponent = () => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({
    id: 22,
    name: "postgresql",
    aliasName: "PostgreSQL",
    description: "CloudDB PostgreSQL数据库",
    type: "db",
    version: "16.4,16.1,14.13,14.7,14.2,13.8,13.6,13.5,12.10,11.15",
    image: null,
    imagePath: "postgresql-2.5.1.svg",
    status: null,
    createTime: "2025-01-14 11:30:37",
    versionStatus: "now",
    chartName: "postgresql",
    chartVersion: "2.5.1",
    grafanaId: null,
    replicas: null,
    replicasStatus: null,
    middlewares: null,
    official: true,
    vendor: "harmonycloud",
    enable: null,
    withMiddleware: null
  });
  const [middlewareMeta, setMiddlewareMeta] = useState({
    "appName": "PostgreSQL",
    "categoryName": "db",
    "vendor": "谐云科技",
    "usageCount": 49,
    "owner": null,
    "lastUpdateTime": "2025-01-14 11:30:37",
    "description": "CloudDB PostgreSQL数据库",
    "questionYaml": null
});

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState(
    '<packaging>pom</packaging><organization><name>harmony cloud</name><url>https://www.harmonycloud.cn/</url></organization>');
  const [fileTree, setFileTree] = useState({});
  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
const data = [
  { title: '参数名', description: 'Description 1' },
  { title: '参数值', description: 'Description 2' },
  // 更多数据...
];
const data1 = [
  { title: '参数名', description: 'Description 1' },
  { title: '参数值', description: 'Description 2' },
  { title: '参数值', description: 'Description 2' },
  // 更多数据...
];
  return (
    <div className="middleware-detail">
      <div className="info-section" style={{marginTop:'0px'}}>
        <p className='title-step'>第一步：Token申请（iPaaS自行订阅）</p>
        <div className="file-content" >
          <p className='title-step'>请求地址</p>
          <CodeMirror
          value={'https://tslapigw.trinasolar.com/632e58a6ea9347760d64c4e8/paaspub-catalog/ca0150/v1/token/create'}
         
          theme="none"
          extensions={[javascript(), yaml()]}
          editable={false}
          readOnly={true}
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            highlightActiveLine: true,
            dropCursor: true,
            allowMultipleSelections: true,
            indentOnInput: true,
            
          }}
          style={{ overflow: 'auto' }}
        />
          <p className='title-step'>请求头</p>
          <Table
          dataSource={[{name:'tsl-clientid',value:'a1ab8e84c61696dc22ce4ce29b9184c0f'},{name:'tsl-clientsecret',value:'1723f8455bc7924b3424ea78e5d84017'}]} 
          columns={[
            {
            title: '参数名',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '参数值',
            dataIndex: 'value',
            key: 'value',
          }
          ]} 
          pagination={false}
          />
          <p className='title-step'>请求参数</p>
          <CodeMirror
          value={
           `"appKey": "xxx-service-id",\n"appSecret": "0893292a-xxxx-xxxx-xxxx-510c66551339",\n"serviceType":"message"`
          }
          theme="none"
          extensions={[javascript(), yaml()]}
          editable={false}
          readOnly={true}
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            highlightActiveLine: true,
            dropCursor: true,
            allowMultipleSelections: true,
            indentOnInput: true,
            
          }}
          style={{ overflow: 'auto' }}
        />
        <p className='title-step'>参数说明</p>
        <Table 
        pagination={false}
        dataSource={[{name:'appKey',value:'应用识别码',type:'String'},{name:'appSecret',value:'请填写应用请求的密钥',type:'String'},{name:'serviceType',value:'所请求的类型',type:'String'}]} 
        columns={[
            {
            title: '应用识别码',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '请填写应用请求的密钥',
            dataIndex: 'value',
            key: 'value',
          },
          {
            title: '所请求的类型',
            dataIndex: 'type',
            key: 'type',
          }
        ]} />
        </div>
        <p className='title-step'>第二步：短信发送</p>
          <div className="file-content" >
          <p className='title-step'>请求地址</p>
          <CodeMirror
          value={'https://tslapigw.trinasolar.com/632e58a6ea9347760d64c4e8/paaspub-catalog/ca0376/v1/service/message'}
         
          theme="none"
          extensions={[javascript(), yaml()]}
          editable={false}
          readOnly={true}
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            highlightActiveLine: true,
            dropCursor: true,
            allowMultipleSelections: true,
            indentOnInput: true,
            
          }}
          style={{ overflow: 'auto' }}
        />
          <p className='title-step'>请求头</p>
          <Table
          dataSource={[{name:'tsl-clientid',value:'1ab8e84c61696dc22ce4ce29b9184c0f'},{name:'tsl-clientsecret',value:'1723f8455bc7924b3424ea78e5d84017'},{name:'token',value:'TOKEN 接口返回'}]} 
          columns={[
            {
            title: '参数名',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '参数值',
            dataIndex: 'value',
            key: 'value',
          }
          ]} 
          pagination={false}
          />
          <p className='title-step'>请求参数</p>
          <CodeMirror
          value={`"appKey":"xxx-service-id",\n"phoneNumbers":"xx",\n"signName":"xx",\n"templateCode":"xx",\n"templateParam": "xx"`
        }
        
          theme="none"
          extensions={[javascript(), yaml()]}
          editable={false}
          readOnly={true}
          basicSetup={{
            lineNumbers: true,
            foldGutter: true,
            highlightActiveLine: true,
            dropCursor: true,
            allowMultipleSelections: true,
            indentOnInput: true,
            
          }}
          style={{ overflow: 'auto' }}
        />
        <p className='title-step'>参数说明</p>
        <Table 
        pagination={false}
        dataSource={[{name:'phoneNumbers',value:'接收短信号码，多个用,分割',type:'String'},{name:'signName',value:'短信签名',type:'String'},
        {name:'templateCode',value:'短信模板,用于发送短信',type:'String'},{name:'templateParam',value:'短信模板内容',type:'String'},{name:'appKey',value:'应用识别码',type:'String'}
    ]} 
        columns={[
            {
            title: '参数名',
            dataIndex: 'name',
            key: 'name',
          },
          {
            title: '参数值',
            dataIndex: 'value',
            key: 'value',
          },
          {
            title: '类型',
            dataIndex: 'type',
            key: 'type',
          }
        ]} />
        </div>
      </div>
 
     
    </div>
  );
};

export default PrdComponent; 