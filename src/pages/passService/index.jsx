import React, { useEffect, useState } from 'react';
import './index.less';
import api from '../../assets/images/api.svg';
import fuwu from '../../assets/images/fuwu.svg';
import wendang from '../../assets/images/wendang.svg';
import yingyong from '../../assets/images/yingyong.svg';
import zhongjianjian from '../../assets/images/zhongjianjian.svg';
import zujian from '../../assets/images/zujian.svg';
import ApiMarket from './ApiMarket';
import PassSer from './passSer';
import ComponenList from './componenList';
import { useLocation, history } from 'umi';
const PassService = () => {
  const { search , path } = useLocation();
  const [selectedFeature, setSelectedFeature] = useState("");
  const [selectedTitle, setSelectedTitle] = useState("");
  const [activeCard, setActiveCard] = useState("");

  const featureTexts = {
    api: '提供统一的应用与技术组件发布、检索与管理平台，支持与iPaaS平台集成适配，实现API全生命周期管理，并通过分类展示、卡片详情和多维度检索实现应用与API资源的快速查找与调用',
    paas: 'PaaS服务集成多种多样的中间件应用以供选择使用，不仅包括内置中间件，还允许自主上传新中间件，统一纳管升级',
    component: '通过共享可复用的前端组件和后端组件以及依赖包，可减少重复开发，提升协作效率，并实现跨项目的标准化功能复用'
  };

  const featureTitles = {
    api: 'API市场',
    paas: 'PaaS服务',
    component: '组件库'
  };

  useEffect(() => {
    let type = "paas";
    if (search) {
      try {
        const query = new URLSearchParams(search);
        type = query.get("type") || type;
      } catch (error) {}
    }

    setSelectedFeature(featureTexts[type]);
    setSelectedTitle(featureTitles[type]);
    setActiveCard(type);
  }, [search]);

  useEffect(() => {

  }, [activeCard, search])

  const onClick = (key) => {
    setSelectedFeature(featureTexts[key]); 
    setSelectedTitle(featureTitles[key]);
    setActiveCard(key);
    history.replace({
      path: path,
      search:key== 'paas' ?'':`?type=${key}`
    })
  }

  return (
    <>
      <div className="pass-service">
        <div className="content-wrapper">
          {/* 页面标题 */}
          <div className="home-header dealPadding">
            <div className="header-content">
              <h1>{selectedTitle}</h1>
              <div className="description">
                {selectedFeature}
              </div>
            </div>
          </div>

          {/* 新增组件展示 */}
          <div className='statistics-card-wrapper'>
            <div className="feature-cards statistics-cards">
              <div 
                className={`feature-card ${activeCard === 'api' ? 'active' : ''}`} 
                onClick={() => {
                  onClick('api')
                }}
              >
                <img src={api} alt="API市场" className="feature-icon" />
                <div className="feature-text">
                  <h3>API市场</h3>
                  <p>提供各服务的学习路径</p>
                </div>
              </div>
              <div 
                className={`feature-card ${activeCard === 'paas' ? 'active' : ''}`}
                onClick={() => {
                  onClick('paas')
                }}
              >
                <img src={fuwu} alt="PaaS服务" className="feature-icon" />
                <div className="feature-text">
                  <h3>PaaS服务</h3>
                  <p>提供各服务的视频介绍</p>
                </div>
              </div>
              <div 
                className={`feature-card ${activeCard === 'component' ? 'active' : ''}`}
                onClick={() => {
                  onClick('component')
                }}
              >
                <img src={zujian} alt="组件库" className="feature-icon" />
                <div className="feature-text">
                  <h3>组件库</h3>
                  <p>提供具体场景案例</p>
                </div>
              </div>
            </div>
          </div>
          {/* 根据选中的卡片显示对应的内容 */}
            {activeCard === 'api' && <ApiMarket />}
            {activeCard === 'paas' && <PassSer />}
            {activeCard === 'component' && <ComponenList />}
        </div>
      </div>
    </>
  );
};

export default PassService;
