import React, { useEffect, useMemo, useState } from "react";
import { useParams, history } from "umi";
import {
  LeftOutlined,
  LoadingOutlined,
  CheckCircleFilled,
  MinusCircleFilled,
  ExclamationCircleOutlined,
  ExclamationCircleFilled,
} from "@ant-design/icons";
import CodeMirror from "@uiw/react-codemirror";
import { javascript } from "@codemirror/lang-javascript";
import { yaml } from "@codemirror/lang-yaml";
import BreadcrumbNav from "@/components/BreadcrumbNav/index";
import ParamsSetDrawer from "../passParamsSet";
import "./index.less";
import {
  getMiddlewareVersion,
  getMiddlewareMeta,
  getMiddlewareFileList,
  getMiddlewareFileContent,
  getMiddlewareOperatorStatuss,
  CONFIG_MIDDLEWARE,
  API_MIDDLEWARE,
} from "@/services";
import { Card, Space, Spin, Table, Typography, notification } from "antd";
import { dataSource, columns, detailForm, resCardList } from "./type";
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId;

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle",
};

// 工具函数：无数据时显示 /
const displayValue = (value) => {
  return value === null || value === undefined || value === "" ? "/" : value;
};

const PassAuthServerDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { chartName, type, id } = location || {};

  const [middlewareInfo, setMiddlewareInfo] = useState({});
  const [middlewareMeta, setMiddlewareMeta] = useState({});

  const [selectedFile, setSelectedFile] = useState(null);
  const [fileContent, setFileContent] = useState("");
  const [fileTree, setFileTree] = useState({});
  const [paramsSetVisible, setParamsSetVisible] = useState(false);
  const [middlewareOperatorStatus, setMiddlewareOperatorStatus] = useState([]);
  const [loading, setLoading] = useState(false);
  // 获取中间件应用列表
  useEffect(() => {
    const fetchData = async () => {
      try {
        const versionRes = await getMiddlewareVersion({
          clusterId: CLUSTER_ID,
          type: type,
        });
        const middlewareInfo = versionRes.filter((item) => item.id == id)[0];
        setMiddlewareInfo(middlewareInfo);
        const [metaRes, operatorStatusRes, fileListRes] =
          await Promise.allSettled([
            getMiddlewareMeta({
              clusterId: CLUSTER_ID,
              type: type,
              chartVersion: middlewareInfo?.chartVersion,
            }),
            getMiddlewareOperatorStatuss({
              clusterId: CLUSTER_ID,
              type: middlewareInfo?.name.toLocaleLowerCase(),
            }),
            getMiddlewareFileList({
              clusterId: CLUSTER_ID,
              type: middlewareInfo?.chartName,
              chartVersion: middlewareInfo?.chartVersion,
            }),
          ]).then((ans) => ans?.map((a) => a.value) || []);
        setMiddlewareMeta(metaRes);
        setMiddlewareOperatorStatus(operatorStatusRes[0].status);
        setFileTree(fileListRes);
      } catch (error) {
        console.error("获取中间件信息失败:", error);
      }
    };

    setLoading(true);
    fetchData().finally(() => {
      setLoading(false);
    });
  }, []);

  useEffect(() => {
    return () => {
      notification.destroy("apply_success_to_jump");
    };
  }, []);
  const handleBack = () => {
    history.back();
  };

  const handleFileClick = async (fileName) => {
    setSelectedFile(fileName);
    const fileContentRes = await getMiddlewareFileContent({
      clusterId: CLUSTER_ID,
      type: middlewareInfo?.chartName,
      chartVersion: middlewareInfo?.chartVersion,
      fileName: fileName,
    });
    setFileContent(fileContentRes);
  };

  const getDetail = useMemo(() => {
    const res = detailForm.map((item) => {
      return (
        <div className="info-item" key={item.label}>
          <span className="label">{item.label}</span>
          <span className="value">{displayValue(item.value)}</span>
        </div>
      );
    });
    return res;
  }, [type, middlewareInfo]);

  const getContent = (item) => {
    if (item.children) {
      return item.children.map((child) => getContent(child));
    }
    if (item.title === "基本使用") {
      return (
        <>
          <p style={{ marginTop: "4px", color: "#a8a8a8" }}>{item.tip}</p>
          <Table dataSource={dataSource} columns={columns} pagination={false} />
        </>
      );
    } else {
      return (
        <>
          {item.tip && (
            <p style={{ margin: "8px 0", color: "#a8a8a8" }}>{item.tip}</p>
          )}
          <CodeMirror
            value={item.content}
            theme="light"
            extensions={[javascript(), yaml()]}
            editable={false}
            readOnly={true}
            basicSetup={{
              lineNumbers: true,
              foldGutter: true,
              highlightActiveLine: true,
              dropCursor: true,
              allowMultipleSelections: true,
              indentOnInput: true,
            }}
            style={{ overflow: "auto" }}
          />
        </>
      );
    }
  };
  const getCardContent = () => {
    return (
      <Space direction="vertical" size="middle" style={{ display: "flex" }}>
        {resCardList.map((item) => {
          return (
            <Card>
              {item.title && (
                <Typography.Text strong>{item.title}</Typography.Text>
              )}
              {getContent(item)}
            </Card>
          );
        })}
      </Space>
    );
  };
  const linkList = [
    {
      href: "/#/productService",
      title: "产品与服务",
    },
    {
      href: `/#/passService/passAuthServerDetail/${type}/${id}/${chartName}`,
      title: `${type}详情`,
    },
  ];

  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding nav-path-ui">
        <BreadcrumbNav list={linkList}></BreadcrumbNav>
        <div
          className="back-button"
          style={{ width: "60px" }}
          onClick={handleBack}
        >
          <LeftOutlined />
          <span>返回</span>
        </div>
      </div>
      <Spin spinning={!!loading}>
        {/* 中间件标题区域 */}
        {/* <div className="middleware-header dealPadding">
          <div className="middleware-base-info">
            <div>
              <img
                src={`${API_MIDDLEWARE}/images/middleware/${middlewareInfo?.imagePath}`}
                alt={type}
                className="middleware-logo"
              />
            </div>
            <div>
              <div className="logo-title">
                <div className="title-wrapper">
                  <span className="title">
                    {middlewareInfo?.aliasName ||
                      middlewareInfo?.name ||
                      middlewareMeta?.appName ||
                      type}
                  </span>
                  <span className="subtitle">
                    ({middlewareInfo?.description})
                  </span>
                </div>
              </div>
              <div className="action-buttons">
                <button className="action-btn primary" onClick={() => history.push(`/passService/detail/apply/${type}/${id}/${middlewareInfo?.chartName}`)}>申请</button>
              </div>
            </div>
          </div>
        </div> */}
        {/* 基本信息区域 */}
        <div className="info-section dealPadding">
          <h3 className="section-title">基本信息</h3>
          <div className="info-grid">{getDetail}</div>
        </div>
      </Spin>
      {/* 基本信息区域 */}
      <div className="info-section dealPadding" style={{ marginTop: "0" }}>
        <h3 className="section-title">使用说明</h3>
        <div className="file-content">{getCardContent()}</div>
      </div>

      {/* 参数选配抽屉 */}
      <ParamsSetDrawer
        visible={paramsSetVisible}
        onClose={() => setParamsSetVisible(false)}
        chartName={middlewareInfo?.chartName}
        chartVersion={middlewareInfo?.chartVersion}
      />
    </div>
  );
};

export default PassAuthServerDetail;
