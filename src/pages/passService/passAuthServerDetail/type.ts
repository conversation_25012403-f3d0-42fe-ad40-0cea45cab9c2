export const dataSource = [
  {
    jkmc: "/api/scf/token",
    jkms: "通过授权码从 TAM 远程获取令牌",
    qqfs: "GET",
    qqcs: "query string code 授权码",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/userinfo",
    jkms: "获取用户信息",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/logout",
    jkms: "通过token从TAM 远程注销",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
  {
    jkmc: "/api/scf/refresh",
    jkms: "刷新token和权限缓存",
    qqfs: "GET",
    qqcs: "header string Authorization Bearer xxx",
    fhcs: "无",
    bz: "无",
  },
];
export const columns = [
  {
    title: "接口名称",
    dataIndex: "jkmc",
    key: "jkmc",
  },
  {
    title: "接口描述",
    dataIndex: "jkms",
    key: "jkms",
  },
  {
    title: "请求方式",
    dataIndex: "qqfs",
    key: "qqfs",
  },
  {
    title: "请求参数",
    dataIndex: "qqcs",
    key: "qqcs",
  },
  {
    title: "返回参数",
    dataIndex: "fhcs",
    key: "fhcs",
  },
  {
    title: "备注",
    dataIndex: "bz",
    key: "bz",
  },
];
export const resCardList = [
  {
    title: "POM引入",
    content: `<dependency>
    <groupId>com.trinasolar</groupId>
    <artifactId>trina-security-starter</artifactId>
    <version>4.0.0</version>
</dependency>`,
  },
  {
    title: "基础配置",
    content: `trina:
  iam:
    # 对应iam环境地址，如测试环境：https://pdweb1.trinasolar.com
    env-url:
    # 客户端ID，需要先到iam申请
    client-id:
    # 客户端密钥，需要先到iam申请
    client-secret:
    # 重定向url，注册在iam的应用重定向地址，可以携带path路径和参数
    redirect-url:
    # 获取用户权限接口地址，对应TASP环境地址
    permission-url:
    # 鉴权白名单路径list集合，如：/api/test/*
    white-uris:
      - xxx
      - xxx`,
  },
  {
    title: "启用注解",
    tip: "在 Spring Boot 启动类或配置类上添加注解：",
    content: `import com.trinasolar.common.security.annotation.EnableSecurity;

@EnableSecurity
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}`,
  },
  {
    title: "基本使用",
    content: ``,
    tip: "默认接口 security starter 模块自带了认证基础接口如下，主要提供给前端脚手架完成登录和鉴权逻辑。",
  },
  {
    title: "权限使用",
    children: [
      {
        content: `@RestController
@RequestMapping("/api/users")
public class UserController {
    // 需要admin角色
    @HasRole("admin")
    @GetMapping("/admin")
    public R<List<User>> getAdminUsers() { /* ... */ }

    // 需要admin或manager角色之一
    @HasRole({"admin", "manager"})
    @GetMapping("/manage")
    public R<List<User>> getManageUsers() { /* ... */ }

    // 需要同时拥有admin和manager角色
    @HasRole(value = {"admin", "manager"}, logical = HasRole.Logical.AND)
    @GetMapping("/super")
    public R<List<User>> getSuperUsers() { /* ... */ }
}`,
        tip: "角色与权限注解  @HasRole 注解",
      },
      {
        content: `@RestController
@RequestMapping("/api/users")
public class UserController {
    // 需要user:add权限
    @HasPermission("user:add")
    @PostMapping
    public R<User> addUser(@RequestBody User user) { /* ... */ }

    // 需要user:edit或user:add权限之一
    @HasPermission({"user:edit", "user:add"})
    @PutMapping("/{id}")
    public R<User> updateUser(@PathVariable Long id, @RequestBody User user) { /* ... */ }

    // 需要同时拥有user:delete和user:admin权限
    @HasPermission(value = {"user:delete", "user:admin"}, logical = HasPermission.Logical.AND)
    @DeleteMapping("/{id}")
    public R<Void> deleteUser(@PathVariable Long id) { /* ... */ }
}`,
        tip: "@HasPermission 注解",
      },
      {
        tip: "@ColPermission 注解",
        content: `@RestController
@RequestMapping("/api/data")
public class DataController {
    // 黑名单模式，自动过滤敏感字段
    @ColPermission(type = ColPermission.Type.BLACK, fields = {"salary", "ssn"})
    @GetMapping("/info")
    public R<UserInfo> getUserInfo() { /* ... */ }

    // 白名单模式，仅返回指定字段
    @ColPermission(type = ColPermission.Type.WHITE, fields = {"name", "email"})
    @GetMapping("/public")
    public R<UserInfo> getPublicInfo() { /* ... */ }
}`,
      },
    ],
  },
];
export const detailForm = [
  { key: "1", label: "名称:", value: "统一认证服务" },
  { key: "1", label: "版本:", value: "1.0.0" },
  { key: "1", label: "创建人:", value: "刘飞飞" },
  { key: "1", label: "创建时间:", value: "2025-02-27 02:50:00" },
  { key: "1", label: "修改人:", value: "刘飞飞" },
  { key: "1", label: "修改时间:", value: "2025-03-27 02:50:00" },
  { key: "1", label: "文档链接:", value: "-" },
  { key: "1", label: "描述:", value: "暂无" },
];
