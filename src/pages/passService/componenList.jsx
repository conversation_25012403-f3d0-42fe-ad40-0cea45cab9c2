import React from 'react';
import { Input, Button, Space, Select, Tree } from 'antd';
import { SearchOutlined, CopyOutlined } from '@ant-design/icons';
import './componenList.less';
import { history } from "umi";

const componenList = () => {
  const apiList = [
    {
      id: 1,
      name: '消息服务调用',
      description: '支持邮件、短信、天讯API调用，接口不变，在已有老版本升级框架',
      version: 'v1.0.0',
      icon: '/path/to/message-icon.svg'
    },
    {
      id: 2,
      name: '通知服务优化升级',
      description: '通知服务优化升级',
      version: 'v1.0.0',
      icon: '/path/to/notification-icon.svg'
    },
    {
      id: 3,
      name: 'API网关服务调用',
      description: '支持现有 API 网关服务调用升级',
      version: 'v1.0.0',
      icon: '/path/to/api-gateway-icon.svg'
    },
    {
      id: 4,
      name: '图片服务调用',
      description: '通知服务优化升级',
      version: 'v1.0.0',
      icon: '/path/to/image-icon.svg'
    },
    {
      id: 5,
      name: '应用附件服务调用',
      description: '接口不变，在已有老版本升级框架，封装成新SDK',
      version: 'v1.0.0',
      icon: '/path/to/attachment-icon.svg'
    },
    {
      id: 6,
      name: '统一认证服务调用',
      description: '封装成新 SDK',
      version: 'v1.0.0',
      icon: '/path/to/auth-icon.svg'
    },
    {
      id: 7,
      name: '全文检索',
      description: '全局检索，提供 api 供其他服务调用，存储标准化数据。支持在平台进行全局搜索',
      version: 'v1.0.0',
      icon: '/path/to/search-icon.svg'
    },
    {
      id: 8,
      name: '微服务治理',
      description: '提供微服务治理组件，支持熔断、限流、重试等治理策略',
      version: 'v1.0.0',
      icon: '/path/to/microservice-icon.svg'
    },
    {
      id: 9,
      name: 'UI组件库',
      description: '提供统一的界面交互元素，快速搭建风格一致的页面按需从库中选取组件进行页面布局与设计',
      version: 'v1.0.0',
      icon: '/path/to/ui-icon.svg'
    },
    {
      id: 10,
      name: '国际化组件',
      description: '提供国际化组件，供其他服务接入',
      version: 'v1.0.0',
      icon: '/path/to/i18n-icon.svg'
    },
    {
      id: 11,
      name: '节假日服务组件',
      description: '封装节假日信息查询服务，对外提供查询接口供其他应用调用',
      version: 'v1.0.0',
      icon: '/path/to/holiday-icon.svg'
    },
    {
      id: 12,
      name: '操作日志服务调用',
      description: '提供 api 供其他服务调用，存储标准化的操作数据，支持在平台展示各服务的操作日志',
      version: 'v1.0.0',
      icon: '/path/to/operation-log-icon.svg'
    }
  ];

  // 导航树数据
  const treeData = [
    {
      title: '智能制造发布组',
      key: 'smart-manufacturing',
      selectable: true,
      className: 'highlight-node',
      children: []
    },
    {
      title: 'PaaS发布组',
      key: 'paas',
      selectable: true,
      children: [
        {
          title: '产品生命周期管理',
          key: 'plm',
          selectable: true
        },
        {
          title: '信息技术',
          key: 'it',
          selectable: true
        },
        {
          title: '人力资源管理',
          key: 'hr',
          selectable: true
        }
      ]
    },
    {
      title: '低代码平台发布组',
      key: 'low-code',
      selectable: true,
      children: []
    },
    {
      title: 'OA发布组',
      key: 'oa',
      selectable: true,
      children: []
    },
    {
      title: 'MHR发布组',
      key: 'mhr',
      selectable: true,
      children: []
    },
    {
      title: 'ITR发布组',
      key: 'itr',
      selectable: true,
      children: []
    }
  ];

  // 添加节点计数
  const getNodeCount = (node) => {
    if (node.key === 'smart-manufacturing') return '(16)';
    if (['paas', 'low-code', 'oa', 'mhr', 'itr'].includes(node.key)) return '(6)';
    return '';
  };

  // 自定义标题渲染
  const titleRender = (nodeData) => {
    const count = getNodeCount(nodeData);
    return (
      <div className="tree-node-content">
        <span className="node-title">{nodeData.title}</span>
        {count && <span className="node-count">{count}</span>}
      </div>
    );
  };

  return (
    <div className="component-list dealPadding">
      <div className="main-content">
        <div className="search-bar">
          <div className="search-title">全部(16)</div>
          <div className="search-filters">
            {/* <div className="search-inputs">
              <Input 
                placeholder="请输入组件库名称"
              />
            </div> */}
          </div>
        </div>

        <div className="api-list">
          {apiList.map((api) => (
            <div key={api.id} className="api-card" onClick={()=>{  history.push(`/passService/componentDetail/${api.name}/${ api.id }`);}}>
              <div className="card-icon">
                <img src={api.icon} alt={"noImage"} />
              </div>
              <div className="card-title">
                <span className="title-text">{api.name}</span>
                <span className="version-tag">{api.version}</span>
              </div>
              <div className="card-description">
                {api.description}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default componenList; 