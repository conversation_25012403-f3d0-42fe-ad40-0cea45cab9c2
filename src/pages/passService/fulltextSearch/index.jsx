
import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import ParamsSetDrawer from '../passParamsSet';
import TestComponent from '../testEnv';
import PrdComponent from '../prdEnv';
import ApiInfo from '../apiInfo';
import './index.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Tabs } from 'antd';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
const CLUSTER_ID = CONFIG_MIDDLEWARE.clusterId

const STATUS_CONFIG = {
  0: {
    icon: LoadingOutlined,
    color: "#D1D5D9",
    text: "安装中"
  },
  1: {
    icon: CheckCircleFilled,
    color: "#1DC11D",
    text: "运行正常"
  },
  2: {
    icon: MinusCircleFilled,
    color: "#FAC800",
    text: "待安装"
  },
  3: {
    icon: ExclamationCircleOutlined,
    color: "#D93026",
    text: "不可用"
  },
  4: {
    icon: ExclamationCircleFilled,
    color: "#faad14",
    text: "运行异常"
  }
};

const iconStyle = {
  marginRight: "6px",
  fontSize: "12px",
  verticalAlign: "middle"
};

const FulltextSearchDetail = () => {
  const location = useParams();
  // 从路由中获取 type 和 id 参数
  const { id } = location || {};
  const handleBack = () => {
    history.back();
  };
  const [info, setInfo] = useState(
    [{
        title:'ElasticSearch-创建索引(post请求)',
        path:'/kepler/share/api/es/create/index',
        paramObj:`{
            "indexName": "test_index",
            "settings": {
                "number_of_shards": 3,
                "number_of_replicas": 1,
                "analysis": {
                    "analyzer": {
                        "content_analyzer": {
                            "type": "custom",
                            "tokenizer": "ik_max_word",
                            "filter": ["lowercase"]
                        },
                        "search_analyzer": {
                            "type": "custom",
                            "tokenizer": "ik_smart",
                            "filter": ["lowercase"]
                        }
                    }
                }
            },
            "mappings": {
                "_doc": {
                    "properties": {
                        "content": {
                            "type": "text",
                            "analyzer": "content_analyzer",
                            "search_analyzer": "search_analyzer",
                            "fields": {
                                "keyword": {
                                    "type": "keyword",
                                    "ignore_above": 256
                                }
                            }
                        }
                    }
                }
            }
        }`,
        paramData:[{name:'body',type:'object',position:'body',sub:'none'}],
        result:'{"code": 0,"msg": "string","data":null}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
      },
      {
        title:'ElasticSearch-删除索引(post请求)',
        path:'/kepler/share/api/es/delete/index',
        paramObj:'{"indexName": "test3_index"}',
        paramData:[{name:'body',type:'object',position:'body',sub:'none'}],
        result:'{"code": 0,"msg": null,"data":true}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
      },
      {
        title:' ElasticSearch-删除文档数据(post请求)',
        path:'/kepler/share/api/search/delete/document',
        paramObj:'{"indexName": "holidays_index","esDocumentId": "u3uWdJgBf6q3rBC2-qtR"}',
        paramData:[{name:'body',type:'object',position:'body',sub:'none'}],
        result:'{"code": 0,"msg": "string","data":null}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
      {
        title:'ElasticSearch-批量存储文档(post请求)',
        path:'/kepler/share/api/search/documents',
        paramObj:`{"indexName": "test_index","documents": [
                {
                    "documentType": "holiday",
                    "sourceSystem": null,
                    "title": "国庆节",
                    "content": {
                        "countryCode": "CN",
                        "createTime": "2025-07-30T16:48:32",
                        "date": "2025-10-03",
                        "delFlag": "0",
                        "id": 23,
                        "nameCn": "国庆节",
                        "type": "官方",
                        "updateTime": "2025-07-30T16:48:32",
                        "year": "2025"
                    }
                },
                {
                    "documentType": "holiday",
                    "sourceSystem": null,
                    "title": "国庆节",
                    "content": {
                        "countryCode": "CN",
                        "createTime": "2025-07-30T16:48:32",
                        "date": "2025-10-01",
                        "delFlag": "0",
                        "id": 23,
                        "nameCn": "国庆节",
                        "type": "官方",
                        "updateTime": "2025-07-30T16:48:32",
                        "year": "2025"
                    }
                }
            ]
        }`,
        paramData:[{name:'body',type:'object',position:'body',sub:'none'}],
        result:'{"code": 0,"msg": "string","data":null}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      },
      {
        title:'ElasticSearch-聚合检索(post请求)',
        path:'/kepler/share/api/search/multi/fulltext',
        paramObj:'{"indexName": "test_index","keyword": "国庆","from": 0,"size": 100}',
        paramData:[{name:'dates',type:'Object',position:'body',sub:'none'}],
        result:'{"code": 0,"msg": null,"data": {"totalHits": 0,"hits": [null],"clusterHitStats": {}}}',
        statusData:[{code:'200',codeText:'OK',sub:'none'},{code:'400',codeText:'Bad Request',sub:'请求参数错误'},
        {code:'500',codeText:'Internal Server Error',sub:'服务器内部错误'}]
       
      }
    ]
   
  )

  useEffect(() => {
    if(info){
      info.forEach(element => {
        if(element.paramObj){
          const jsonObjectRequest = JSON.parse(element.paramObj);
          const formattedJsonRequest = JSON.stringify(jsonObjectRequest, null, 2);
          element.newParamObj = formattedJsonRequest
        }
      });
      setInfo([...info])
    }
  }, [])
  const linkList = [
    {
      href: '/#/productService',
      title: '产品与服务'
    },
    {
    
      title: `全文检索组件详情`
    },
  ]
  return (
    <div className="middleware-detail">
      {/* 头部导航 */}
      <div className="detail-header dealPadding">
        <BreadcrumbNav list={linkList} isNeedBack/>
       
      </div>
      <div className="info-section dealPadding">
        <h3 className="section-title">基础信息</h3>
        <div className="info-grid">
          <div className="info-item">
            <span className="label">名称：</span>
            <span className="value">全文检索组件</span>
          </div>
          <div className="info-item">
            <span className="label">版本：</span>
            <span className="value">1.0.0</span>
          </div>
        
          <div className="info-item">
            <span className="label">创建人：</span>
            <span className="value">harmonycloud</span>
          </div>
          <div className="info-item">
            <span className="label">创建时间：</span>
            <span className="value">2025-08-06 3:50:00</span>
          </div>
          <div className="info-item">
            <span className="label">修改人：</span>
            <span className="value">harmonycloud</span>
          </div>
          <div className="info-item">
            <span className="label">修改时间：</span>
            <span className="value">2025-08-06 3:50:00</span>
          </div>
          <div className="info-item">
            <span className="label">描述：</span>
            <span className="value">全文检索查询</span>
          </div>
        </div>
      </div>
      <div className="info-section dealPadding" style={{marginTop:'0px'}}>
        <h3 className="section-title">使用说明</h3>
          <div className="file-content" >
            <ApiInfo infos={info} />
          </div>
      </div>
     
    </div>
  );
};

export default FulltextSearchDetail; 