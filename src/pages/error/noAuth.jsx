import { Button, Result, Row } from 'antd';
import React from 'react';
import { history } from 'umi';
import unauthorizedImage from '@/assets/images/pic_tip_unauthorized.png';
import { homePath } from "@/constants"

const unauthorizedIcon = (
    <>
        <img src={unauthorizedImage} style={{ height: 290 }} />
    </>
);
export default function NotFound(){
    return (
        <Row
            style={{ height: '100vh' }}
            align="middle"
            justify="center"
        >
            <Result
                icon={unauthorizedIcon}
                title="抱歉，您没有权限访问"
                subTitle="需要开通权限可以联系管理员"
                extra={
                    <>
                        <Button
                            type="primary"
                            onClick={() => history.push(homePath)}
                        >
                            返回首页
                        </Button>
                    </>
                }
            />
        </Row>
    );
};
