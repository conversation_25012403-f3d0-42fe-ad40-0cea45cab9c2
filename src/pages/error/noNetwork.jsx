import { Button, Result, Row } from 'antd';
import React from 'react';
import { history, useSearchParams } from 'umi';
import  NotFoundImage from '@/assets/images/404.png';

const NotFoundIcon = (
    <>
        <img src={NotFoundImage} style={{ height: 290 }} />
    </>
);
export default function NotFound(){
    const [searchParams] = useSearchParams();
    const handleFresh = () => {
        const redirectUrl = searchParams.get('redirectUrl')
        history.replace(redirectUrl)
    }
    return (
        <Row
            style={{ height: '100vh' }}
            align="middle"
            justify="center"
        >
            <Result
                icon={NotFoundIcon}
                title="网络错误"
                subTitle="请检查网络后刷新页面"
                extra={
                    <>
                        <Button
                            type="primary"
                            onClick={handleFresh}
                        >
                            刷新重试
                        </Button>
                    </>
                }
            />
        </Row>
    );
};
