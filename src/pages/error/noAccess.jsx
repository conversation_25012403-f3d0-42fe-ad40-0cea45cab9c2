import React from 'react';
import { Button, Result } from 'antd';
import locale from "@/locale";
import styles from './index.less';

const NoFoundPage = () => {
  
  return (
    <Result
      status="403"
      title={locale.error.noAccess}
      subTitle={
        <div style={{ display: 'flex', flexDirection: 'column' }}>
          <span>{locale.error.noAccessReason}</span>
          <span>1.{locale.error.noAccessDesc1}</span>
          <span>2.{locale.error.noAccessDesc2}</span>
        </div>
      }
      className={styles.errorPage}
      extra={
        <Button type="primary" onClick={() => (window.location.href = '/')}>
          {locale.error.goBack}
        </Button>
      }
    />
  );
};

export default NoFoundPage;
