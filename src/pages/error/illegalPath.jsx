import { But<PERSON>, Result, Row } from 'antd';
import React from 'react';
import { history } from 'umi';
import  NotFoundImage from '@/assets/images/404.png';
import { homePath } from "@/constants"

const NotFoundIcon = (
    <>
        <img src={NotFoundImage} style={{ height: 290 }} />
    </>
);
export default function NotFound(){
    return (
        <Row
            style={{ height: '100vh' }}
            align="middle"
            justify="center"
        >
            <Result
                icon={NotFoundIcon}
                title="页面访问路径无效"
                subTitle="可能的原因：菜单配置不正确、访问路径不是一个有效路由地址……"
                extra={
                    <>
                        <Button
                            type="primary"
                            onClick={() => history.push(homePath)}
                        >
                            返回首页
                        </Button>
                    </>
                }
            />
        </Row>
    );
};
