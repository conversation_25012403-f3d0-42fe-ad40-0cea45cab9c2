import { But<PERSON>, Result, Row } from 'antd';
import React from 'react';
import { history } from 'umi';
import  NotFoundImage from '@/assets/images/404.png';
import { homePath } from "@/constants"

const NotFoundIcon = (
    <>
        <img src={NotFoundImage} style={{ height: 290 }} />
    </>
);
export default function NotFound(){
    return (
        <Row
            style={{ height: '100vh' }}
            align="middle"
            justify="center"
        >
            <Result
                icon={NotFoundIcon}
                title="咦！页面找不不到了"
                subTitle="可能的原因：网址输入错误、网页已失效……"
                extra={
                    <>
                        <Button
                            type="primary"
                            onClick={() => history.push(homePath)}
                        >
                            返回首页
                        </Button>
                    </>
                }
            />
        </Row>
    );
};
