import { Button, Result } from 'antd';
import React from 'react';
import locale from "@/locale";
import styles from './index.less';

const NoMenuPage = () => {
    return (
        <Result
            title={locale.error.noMenu}
            subTitle={locale.error.noMenuDescripe}
            className={styles.errorPage}
            extra={
                <Button type="primary" onClick={() => (window.location.href = '/')}>
                    {locale.error.goBack}
                </Button>
            }
        />
    );
};

export default NoMenuPage;
