import React, { useState } from 'react';
import { Card, Row, Col, Typography, Switch, Space } from 'antd';
import "./index.less";
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { HolderOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

const LeftSet = (props) => {
    const { moduleShows, workItems, onModuleShowsChange, onWorkItemsChange } = props
    // 模块显示设置开关状态
    const [moduleSwitches, setModuleSwitches] = useState({
        personalProfile: true,
        workMatters: true,
        documentCollection: true,
        quickEntry: true,
        recentlyVisitedSystems: true,
        recentlyVisitedApps: true
    });

    // 处理标签页开关变化
    const handleTabSwitchChange = (key, value) => {
        const newWorkItems = workItems.map(item => {
            if (item.name === key.name) {
                return {
                    ...item,
                    show: value
                };
            }
            return item;
        });
        onWorkItemsChange(newWorkItems);
    };

    // 处理标签页拖拽排序
    const onDragEnd = (result) => {
        if (!result.destination) {
            return;
        }
        const sourceIndex = result.source.index;
        const destinationIndex = result.destination.index;
        const newWorkItems = [...workItems];
        const [removed] = newWorkItems.splice(sourceIndex, 1);
        newWorkItems.splice(destinationIndex, 0, removed);
        newWorkItems.forEach((item, index) => {
            item.sort = index + 1;
        });
        onWorkItemsChange(newWorkItems);
    };

    const handleModuleSwitchChange = (name, value) => {
        const newModuleShows = moduleShows.map(module => {
            if (module.name === name) {
                return { ...module, show: value };
            }
            return module;
        });
        // 调用父组件传递的回调函数，将新数据传递回父组件
        onModuleShowsChange(newModuleShows);
    };

    return (
        <div className='left'>
            <Row gutter={[16, 16]} style={{height: '100%'}}>
                {/* 左侧模块显示设置 */}
                <Col span={12} style={{height: '100%'}}>
                    <div className='content'>
                        <div style={{fontWeight: '600', fontSize: '18px', lineHeight: '32px'}}>模块显示设置</div>
                        <div style={{color: '#8f96a2'}}>选择在工作台上显示那些模块</div>
                        <div className='list-content'>
                            {moduleShows.map(module => (
                                <Row align="middle" className='row-item' key={module.name}>
                                    <Col span={20}>
                                        <span style={{ color: ['个人简介', '工作事项'].includes(module.name) ? '#999' : 'inherit' }}>
                                            {module.name}
                                            {['个人简介', '工作事项'].includes(module.name) && <span style={{ color: 'red' }}>（必选）</span>}
                                        </span>
                                    </Col>
                                    <Col span={4}>
                                        <Switch
                                            checked={module.show}
                                            onChange={(value) => {
                                                if (['个人简介', '工作事项'].includes(module.name)) return;
                                                handleModuleSwitchChange(module.name, value);
                                            }}
                                            defaultChecked={module.show}
                                            disabled={['个人简介', '工作事项'].includes(module.name)}
                                        />
                                    </Col>
                                </Row>
                            ))}
                        </div>
                    </div>
                </Col>
                {/* 右侧工作事项标签页设置 */}
                <Col span={12} style={{height: '100%'}}>
                    <div className='content'>
                        <div style={{fontWeight: '600', fontSize: '18px', lineHeight: '32px'}}>工作事项标签页设置</div>
                        <div style={{color: '#8f96a2'}}>选择显示的标签页并调整顺序</div>
                        <div className='list-content'>
                            <DragDropContext onDragEnd={onDragEnd}>
                                <Droppable droppableId="tabList">
                                    {(provided) => (
                                        <div
                                            {...provided.droppableProps}
                                            ref={provided.innerRef}
                                        >
                                            {workItems.map((item, index) => (
                                                <Draggable key={item.name} draggableId={item.name} index={index}>
                                                    {(provided) => (
                                                        <Row
                                                            align="middle"
                                                            className='drag-item'
                                                            style={{ marginBottom: 8 }}
                                                            ref={provided.innerRef}
                                                            {...provided.draggableProps}
                                                            {...provided.dragHandleProps}
                                                        >
                                                            <Col span={2}>
                                                                <HolderOutlined style={{ cursor: 'grab' }} />
                                                            </Col>
                                                            <Col span={19}>{item.name}</Col>
                                                            <Col span={3}>
                                                                <Switch
                                                                    checked={item.show}
                                                                    onChange={(value) => handleTabSwitchChange(item, value)}
                                                                    defaultChecked
                                                                />
                                                            </Col>
                                                        </Row>
                                                    )}
                                                </Draggable>
                                            ))}
                                            {provided.placeholder}
                                        </div>
                                    )}
                                </Droppable>
                            </DragDropContext>
                        </div>
                    </div>
                </Col>
            </Row>
        </div>
    );
};

export default LeftSet;