.set {
    width: calc(100% - 30px);
    margin: 15px auto;
    height: calc(100% - 30px);
    overflow: auto;
    background: #fff;
    display: flex;
    flex-direction: column;

    .main-content {
        padding: 16px 40px;
        height: calc(100% - 100px);
        flex-grow: 1;
        overflow: hidden;

        .left {
            padding: 16px 0 0;
            height: 100%;

            .content {
                padding: 25px 20px;
                background: #f2f2f2;
                height: 100%;
                border-radius: 12px;
                display: flex;
                flex-direction: column;

                .list-content {
                    flex-grow: 1;
                    overflow: auto;

                    .row-item {
                        line-height: 60px;
                    }
                    .drag-item {
                        height: 42px;
                        line-height: 42px;
                        padding: 0 10px;
                        background: #fff;
                        margin-top: 10px;
                        border-radius: 6px;
                    }
                }
            }
        }
        .right {
            padding: 16px 0 0;
            height: 100%;

            .quick-content {
                padding: 25px 20px;
                background: #f2f2f2;
                border-radius: 12px;
                height: 100%;
                display: flex;
                flex-direction: column;

                .list-content {
                    flex-grow: 1;
                    overflow: auto;

                    .drag-item {
                        background-color: #f9fafb;
                        margin-top: 15px;
                        border-radius: 5px;
                        padding: 10px 10px;
                        .img-style {
                            width: 40px;
                            height: 40px;
                            border-radius: 4px;
                            background-color: #3C82F6;
                            color: #fff;
                            font-size: 16px;
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                        .url-style {
                            width: 40px;
                            height: 40px;
                            border-radius: 4px;
                        }
                    }
                    .add-icon {
                        width: 100%;
                        margin: auto;
                        text-align: center;
                        background-color: #eff6ff;
                        color: #3C82F6;
                        height: 42px;
                        line-height: 42px;
                        border-radius: 8px;
                        margin-top: 25px;
                        cursor: pointer;
                    }
                }
            }
        }
    }
    
}