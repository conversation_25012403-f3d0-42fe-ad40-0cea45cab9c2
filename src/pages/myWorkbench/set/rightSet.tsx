import React, { forwardRef, useEffect, useImperativeHandle, useMemo, useState } from 'react';
import { Col, Row, Card, Input, Switch, Upload, message, Button, Form } from 'antd';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import "./index.less";
import { HolderOutlined, DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { localStorageEnum } from '@/enums/storageEnum';
import storage from '@/utils/storage';

const TOKEN = localStorageEnum.TOKEN; // 保存token信息
const { TextArea } = Input;

const RightSet = forwardRef((props, ref) => {
    const { accessConfigs, onAccessConfigChange } = props
    const [tabOrder, setTabOrder] = useState([]);
    const token = storage.getLocal(TOKEN)
    const formArray = Array.from({ length: 6 }, () => {
        const [form] = Form.useForm();
        return form;
    })
    const [forms, setForms] = useState(formArray);


    // 处理标签页拖拽排序
    const onDragEnd = (result) => {
        if (!result.destination) return;

        const items = Array.from(accessConfigs);
        const [reorderedItem] = items.splice(result.source.index, 1);
        items.splice(result.destination.index, 0, reorderedItem);
        items.forEach((item, index) => {
            item.sort = index + 1;
        });
        onAccessConfigChange(items);
    };

    // 处理图片上传
    const handleUpload = (info, index) => {
        if (info.file.status === 'done') {
            const newTabOrder = [...accessConfigs];
            newTabOrder[index].logoUrl = info.file.response.data.url;
            onAccessConfigChange(newTabOrder);
            message.success(`${info.file.name} 上传成功`);
        } else if (info.file.status === 'error') {
            message.error(info.file.response.msg || '上传失败');
        }
    };
    const handleBeforeUpload = (file, index) => {
        const isImage = file.type.indexOf('image/') === 0;
        if (!isImage) {
            message.error('请上传图片文件！');
            return false;
        }
        return true;
    };

    // 处理名称输入变化
    const handleNameChange = (e, index) => {
        const newTabOrder = [...accessConfigs];
        newTabOrder[index].name = e.target.value;
        onAccessConfigChange(newTabOrder);
    };

    // 处理链接输入变化
    const handleLinkChange = (e, index) => {
        const newTabOrder = [...accessConfigs];
        newTabOrder[index].url = e.target.value;
        onAccessConfigChange(newTabOrder);
    };

    // 处理删除项
    const handleDelete = (index) => {
        const newTabOrder = [...accessConfigs];
        newTabOrder.splice(index, 1);
        onAccessConfigChange(newTabOrder);
    };

    // 处理添加快捷入口
    const handleAddEntry = () => {
        if (accessConfigs.length < 6) {
            const newEntry = {
                id: new Date().getTime().toString(),
                logoUrl: '',
                name: '',
                url: '',
                sort: accessConfigs.length + 1
            };
            forms[accessConfigs.length].setFieldsValue(newEntry)
            onAccessConfigChange([...accessConfigs, newEntry]);
        }
    };

    const validateForm = async () => {
        const allValues = [];
        const allErrors = [];

        // 遍历每个表单实例进行校验
        for (let i = 0; i < forms.length; i++) {
            try {
                const values = await forms[i].validateFields();
                allValues.push(values);
            } catch (errorInfo) {
                allErrors.push({ index: i, error: errorInfo });
            }
        }

        if (allErrors.length > 0) {
            return { error: allErrors, values: null };
        }

        return { error: null, values: allValues };
    };

    useImperativeHandle(ref, () => ({
        validateForm
    }));


    return (
        <div className='right'>
            <div className='quick-content'>
                <div style={{ fontWeight: '600', fontSize: '18px', lineHeight: '32px' }}>快捷入口设置</div>
                <div style={{ color: '#8f96a2' }}>自定义工作台的快捷入口，最多可配置6个</div>
                <div className='list-content'>
                    <DragDropContext onDragEnd={onDragEnd}>
                        <Droppable droppableId="tabList">
                            {(provided) => (
                                <div
                                    {...provided.droppableProps}
                                    ref={provided.innerRef}
                                >
                                    {accessConfigs.map((item, index) => (
                                        <Draggable key={item.id.toString()} draggableId={item.id.toString()} index={index}>
                                            {(provided) => (
                                                <Row
                                                    align="middle"
                                                    className='drag-item'
                                                    style={{ marginBottom: 8 }}
                                                    ref={provided.innerRef}
                                                    {...provided.draggableProps}
                                                    {...provided.dragHandleProps}
                                                >
                                                    <Col span={2} style={{textAlign: 'center'}}>
                                                        <HolderOutlined style={{ cursor: 'grab' }} />
                                                    </Col>
                                                    <Col span={4} style={{textAlign: 'center'}}>
                                                        <Upload
                                                            name="file"
                                                            action="/api/doc/global/categories/document/single/upload"
                                                            headers={{
                                                                Authorization: `Bearer ${token}` 
                                                            }}
                                                            listType="picture"
                                                            beforeUpload={(info) => handleBeforeUpload(info, index)}
                                                            showUploadList={false}
                                                            onChange={(info) => handleUpload(info, index)}
                                                        >
                                                            {item.logoUrl ? (
                                                                <div>
                                                                    <img src={item.logoUrl} alt="icon" className='url-style' />
                                                                    <div style={{ textAlign: 'center', color: '#3C82F6', fontSize: '14px' }}>更换</div>
                                                                </div>
                                                            ) : (
                                                                <div>
                                                                    <div className='img-style'>
                                                                        {item.name && item.name.length > 0 ? item.name[0] : '未'}
                                                                    </div>
                                                                    <div style={{ textAlign: 'center', color: '#3C82F6', fontSize: '14px' }}>更换</div>
                                                                </div>
                                                            )}
                                                        </Upload>
                                                    </Col>
                                                    <Col span={16}>
                                                        <Form
                                                            form={forms[index]}
                                                            initialValues={item}
                                                        >
                                                            <Form.Item
                                                                name="name"
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: '',
                                                                    },
                                                                ]}
                                                                style={{marginBottom: '8px'}}
                                                            >
                                                                <Input
                                                                    placeholder="名称，如：公司官网"
                                                                    onChange={(e) => handleNameChange(e, index)}
                                                                />
                                                            </Form.Item>
                                                            <Form.Item
                                                                name="url"
                                                                rules={[
                                                                    {
                                                                        required: true,
                                                                        message: '',
                                                                    },
                                                                ]}
                                                                style={{marginBottom: '8px'}}
                                                            >
                                                                <Input
                                                                    placeholder="链接，如：http://example.com"
                                                                    onChange={(e) => handleLinkChange(e, index)}
                                                                />
                                                            </Form.Item>
                                                        </Form>
                                                    </Col>
                                                    <Col span={2} style={{textAlign: 'center'}}>
                                                        <DeleteOutlined onClick={() => handleDelete(index)} style={{ cursor: 'pointer' }} />
                                                    </Col>
                                                </Row>
                                            )}
                                        </Draggable>
                                    ))}
                                    {provided.placeholder}
                                </div>
                            )}
                        </Droppable>
                    </DragDropContext>
                    {accessConfigs.length < 6 && (
                        <div onClick={handleAddEntry} className='add-icon'>
                            <PlusOutlined style={{marginRight: '10px'}} />
                            添加快捷入口
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
});

export default RightSet;