import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Statistic, Input, DatePicker, Tabs, List, Pagination, Spin } from 'antd';
import "./index.less";
import Icon from '@ant-design/icons';
import { history, useParams, useLocation } from 'umi';
const { TabPane } = Tabs;
const { Title } = Typography;
import iconSvg from '../../../assets/images/workbench.svg'
import iconSvgGary from '../../../assets/images/workbrenchGary.svg'
import { getWorkItemList } from '@/services/workbench';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Search } = Input;

const JobList = (props) => {
    const { userInfo, workItemShow } = props; 
    const [loadFlag, setLoadFlag] = useState(false);
    const [activeTab, setActiveTab] = useState('');
    const [listData, setListData] = useState([]);
    const [total, setTotal] = useState(0);
    const [tabList, setTabList] = useState([
        'PaaS', '存储', '文档', 'API申请', 'API审批', '需求', '任务', '缺陷', 'TIS工单', '代码合并请求'
    ]);
    const [sortedTabs, setSortedTabs] = useState([]);
    const [searchParams, setSearchParams] = useState({
        title: "",
        startTime: "",
        endTime: "",
        type: "1",
        size: 10,
        page: 1
    })
    const mapType = {
        'PaaS': '1',
        '存储': '2',
        '文档': '3',
        'API申请': '4',
        'API审批': '10',
        '需求': '5',
        '任务': '6',
        '缺陷': '7',
        'TIS工单': '8',
        '代码合并请求': '9'
    }

    const fetchWorkList = () => {
        if (!activeTab) {
            return;
        }
        setListData([])
        let queryId = 1
        if (userInfo.adminRole) {
            queryId = 0
        }
        setLoadFlag(true)
        const queryParam = {
            ...searchParams,
            type: mapType[activeTab]
        }
        getWorkItemList(queryId, queryParam).then(res => {
            setListData(res.list)
            setTotal(res.total)
            setLoadFlag(false)
        }).finally(() => {
            setLoadFlag(false)
        })
    };

    const handleSearch = (value) => {
        setSearchParams((prevParams) => ({
            ...prevParams,
            title: value || '',
        }));
    };

    const handleKeyChange = (e) => {
        if (e.key === 'Enter') {
            setSearchParams((prevParams) => ({
                ...prevParams,
                title: e.target.value || '',
            }));
        }
    };

    const handleDateChange = (dates, dateStrings) => {
        // 处理日期范围选择逻
        const start = dateStrings[0]
            ? dayjs(dateStrings[0]).format('YYYY-MM-DD HH:mm:ss')
            : '';
        const end = dateStrings[1]
            ? dayjs(dateStrings[1]).format('YYYY-MM-DD HH:mm:ss')
            : '';
        setSearchParams((prevParams) => ({
            ...prevParams,
            startTime: start,
            endTime: end,
        }));
    };

    const handleTabChange = async (key) => {
        setActiveTab(key);
        setSearchParams((prevParams) => ({
            ...prevParams,
            size: 10,
            page: 1,
        }));
    };

    const handlePageChange = async (pageNo, sizeNo) => {
        setSearchParams((prevParams) => ({
            ...prevParams,
            size: sizeNo,
            page: pageNo,
        }));
    };

    useEffect(() => {
        fetchWorkList();
    }, [searchParams]);
    useEffect(() => {
        const handleKeyDown = (event) => {
            if (event.key === 'Tab') {
                event.preventDefault(); // 阻止默认的 Tab 键行为
                const currentIndex = tabList.indexOf(activeTab);
                const nextIndex = (currentIndex + 1) % tabList.length;
                setActiveTab(tabList[nextIndex]);
                handleTabChange(tabList[nextIndex]);
            }
        };

        window.addEventListener('keydown', handleKeyDown);

        return () => {
            window.removeEventListener('keydown', handleKeyDown);
        };
    }, [activeTab]);

    const extractRouteFromUrl = (url) => {
    const hashIndex = url.indexOf('/#');
        if (hashIndex !== -1) {
            return url.slice(hashIndex + 2); // 从 /# 之后开始截取
        }
        return url; 
    }
    const handleJumpClick = (item) => {
        if (item.jumpUrl) {
            history.push(extractRouteFromUrl(item.jumpUrl))
        }
    }

    const handleJumpTis = (item) => {
        if (item.jumpUrl) {
            window.open(item.jumpUrl)
        }
    }

    const paasTab = (
        <TabPane tab="PaaS" key="PaaS">
            <div style={{height: '410px', overflow: 'auto', position: 'relative'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                        <List.Item onClick={() => handleJumpClick(item)} style={{ cursor: 'pointer', borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                            <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                {/* 使用 img 标签显示本地 SVG 图片 */}
                                <img
                                    src={iconSvg}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                <div title={item.title} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                    {item.title}
                                </div>
                            </div>
                            <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                <span className='over-style' style={{width: '40%'}}>提交时间：<span title={item.createTime}>{item.createTime}</span></span>
                                <span className='over-style' style={{width: '30%'}}>应用系统：<span title={item.appSystem}>{item.appSystem}</span></span>
                                <span className='over-style' style={{width: '30%'}}>状态：<span title={item.status}>{item.status}</span></span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const storageTab = (
        <TabPane tab="存储" key="存储">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                        <List.Item onClick={() => handleJumpClick(item)} style={{ cursor: 'pointer', borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                            <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                {/* 使用 img 标签显示本地 SVG 图片 */}
                                <img
                                    src={iconSvg}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                {/* 名称 */}
                                <div title={item.title} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                    {item.title}
                                </div>
                            </div>
                            {/* 发起人及接收时间 */}
                            <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                <span className='over-style' style={{width: '14%'}}>类型：<span style={{color: '#1890ff'}} title={item.type}>{item.type}</span></span>
                                <span className='over-style' style={{width: '26%'}}>提交时间：<span title={item.createTime}>{item.createTime}</span></span>
                                <span className='over-style' style={{width: '30%'}}>应用系统：<span title={item.appSystem}>{item.appSystem}</span></span>
                                <span className='over-style' style={{width: '30%'}}>状态：<span title={item.status}>{item.status}</span></span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const documentTab = (
        <TabPane tab="文档" key="文档">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                        <List.Item onClick={() => handleJumpClick(item)} style={{ cursor: 'pointer', borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                            <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                {/* 使用 img 标签显示本地 SVG 图片 */}
                                <img
                                    src={iconSvg}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                {/* 名称 */}
                                <div title={item.title} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                    {item.title}
                                </div>
                            </div>
                            {/* 发起人及接收时间 */}
                            <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                <span className='over-style' style={{width: '26%'}}>类型：<span style={{color: '#1890ff'}} title={item.type}>{item.type}</span></span>
                                <span className='over-style' style={{width: '32%'}}>提交时间：<span title={item.createTime}>{item.createTime}</span></span>
                                <span className='over-style' style={{width: '30%'}}>状态：<span title={item.status}>{item.status}</span></span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />  
        </TabPane>
    )
    const apiTab = (
        <TabPane tab="API申请" key="API申请">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                        <List.Item onClick={() => handleJumpClick(item)} style={{ cursor: 'pointer', borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                            <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                {/* 使用 img 标签显示本地 SVG 图片 */}
                                <img
                                    src={iconSvg}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                {/* 名称 */}
                                <div title={item.title} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                    {item.title}
                                </div>
                            </div>
                            {/* 发起人及接收时间 */}
                            <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                <span className='over-style' style={{width: '26%'}}>类型：<span style={{color: '#1890ff'}} title={item.type}>{item.type}</span></span>
                                <span className='over-style' style={{width: '32%'}}>提交时间：<span title={item.createTime}>{item.createTime}</span></span>
                                <span className='over-style' style={{width: '30%'}}>状态：<span title={item.status}>{item.status}</span></span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const apiTabApprove = (
        <TabPane tab="API审批" key="API审批">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => (
                        <List.Item onClick={() => handleJumpClick(item)} style={{ cursor: 'pointer', borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                            <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                {/* 使用 img 标签显示本地 SVG 图片 */}
                                <img
                                    src={iconSvg}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                {/* 名称 */}
                                <div title={item.title} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                    {item.title}
                                </div>
                            </div>
                            {/* 发起人及接收时间 */}
                            <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                <span className='over-style' style={{width: '26%'}}>类型：<span style={{color: '#1890ff'}} title={item.type}>{item.type}</span></span>
                                <span className='over-style' style={{width: '32%'}}>提交时间：<span title={item.createTime}>{item.createTime}</span></span>
                                <span className='over-style' style={{width: '30%'}}>状态：<span title={item.status}>{item.status}</span></span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const demandTab = (
        <TabPane tab="需求" key="需求">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.tag) {
                        case 'feature':
                            backgroundColor = '#008DDC';
                            break;
                        case 'story':
                            backgroundColor = '#0EC804';
                            break;
                        case 'Epic':
                            backgroundColor = '#042B41';
                            break;
                        default:
                            backgroundColor = '#008DDC';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    {/* 使用 img 标签显示本地 SVG 图片 */}
                                    <span style={{ backgroundColor, color: 'white', padding: '0px 5px 2px 5px',lineHeight: '16px', borderRadius: '3px', marginRight: '10px' }}>
                                        {item.tag}
                                    </span>
                                    <div title={item.title} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.title}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span className='over-style' style={{width: '14%'}}>状态：<span style={{color: '#1890ff'}}>{item.status}</span></span>
                                    <span className='over-style' style={{width: '26%'}}>预计结束时间：<span title={item.createTime}>{item.createTime}</span></span>
                                    <span className='over-style' style={{width: '30%'}}>停留时间：<span title={item.dwellTime}>{item.dwellTime}</span></span>
                                    <span className='over-style' style={{width: '30%'}}>所属项目：<span title={item.appSystem}>{item.appSystem}</span></span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />
        </TabPane>
    )
    const taskTab = (
        <TabPane tab="任务" key="任务">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    {/* 使用 img 标签显示本地 SVG 图片 */}
                                    <span style={{ backgroundColor: '#008ddc', color: 'white', padding: '0px 5px 2px 5px',lineHeight: '16px', borderRadius: '3px', marginRight: '10px' }}>
                                        {item.tag}
                                    </span>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.title}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span className='over-style' style={{width: '14%'}}>状态：<span style={{color: '#1890ff'}} title={item.status}>{item.status}</span></span>
                                    <span className='over-style' style={{width: '26%'}}>预计结束时间：<span title={item.createTime}>{item.createTime}</span></span>
                                    <span className='over-style' style={{width: '30%'}}>停留时间：<span title={item.dwellTime}>{item.dwellTime}</span></span>
                                    <span className='over-style' style={{width: '30%'}}>所属项目：<span title={item.appSystem}>{item.appSystem}</span></span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />     
        </TabPane>
    )
    const defectTab = (
        <TabPane tab="缺陷" key="缺陷">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.tag) {
                        case 'bug':
                            backgroundColor = '#fa1937';
                            break;
                        default:
                            backgroundColor = '#fa1937';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    {/* 使用 img 标签显示本地 SVG 图片 */}
                                    <span style={{ backgroundColor, color: 'white', padding: '0px 5px 2px 5px',lineHeight: '16px', borderRadius: '3px', marginRight: '10px' }}>
                                        {item.tag}
                                    </span>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.title}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span className='over-style' style={{width: '14%'}}>状态：<span style={{color: '#1890ff'}} title={item.status}>{item.status}</span></span>
                                    <span className='over-style' style={{width: '26%'}}>预计结束时间：<span title={item.createTime}>{item.createTime}</span></span>
                                    <span className='over-style' style={{width: '30%'}}>停留时间：<span title={item.dwellTime}>{item.dwellTime}</span></span>
                                    <span className='over-style' style={{width: '30%'}}>所属项目：<span title={item.appSystem}>{item.appSystem}</span></span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            /> 
        </TabPane>
    )
    const tisTab = (
        <TabPane tab="TIS工单" key="TIS工单">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.tag) {
                        case '高':
                            backgroundColor = '#fa1937';
                            break;
                        case '中':
                            backgroundColor = '#6997ff';
                            break;
                        case '低':
                            backgroundColor = '#8797aa';
                            break;
                        default:
                            backgroundColor = '#6997ff';
                        }
                        return (
                            <List.Item onClick={() => handleJumpTis(item)} style={{ cursor: 'pointer', borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.title}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span className='over-style' style={{width: '16%'}}>工单状态：<span style={{color: '#1890ff'}} title={item.status}>{item.status}</span></span>
                                    <span className='over-style' style={{width: '12%'}}>
                                        优先级：<span style={{backgroundColor, color: '#fff', padding: '2px 5px', borderRadius: '3px', fontSize: '12px'}}>{item.tag}</span>
                                    </span>
                                    <span className='over-style' style={{width: '22%'}}>
                                        发起人：<span style={{color: '#444'}} title={item.creator}>{item.creator}</span>
                                    </span>
                                    <span className='over-style' style={{width: '25%'}}>
                                        创建时间：<span style={{color: '#444'}} title={item.createTime}>{item.createTime}</span>
                                    </span>
                                    <span className='over-style' style={{width: '25%'}}>
                                        流程名称：<span title={item.appSystem} style={{color: '#444'}}>{item.appSystem}</span>
                                    </span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />  
        </TabPane>
    )
    const codeTab = (
        <TabPane tab="代码合并请求" key="代码合并请求">
            <div style={{height: '410px', overflow: 'auto'}}>
                <List
                    dataSource={listData}
                    renderItem={(item) => {
                        let backgroundColor;
                        switch (item.tag) {
                        case '高':
                            backgroundColor = '#fa1937';
                            break;
                        case '中':
                            backgroundColor = '#6997ff';
                            break;
                        case '低':
                            backgroundColor = '#8797aa';
                            break;
                        default:
                            backgroundColor = '#6997ff';
                        }
                        return (
                            <List.Item style={{ borderBottom: '1px solid #e8e8e8', display: 'flex', flexDirection: 'column' }}>
                                <div style={{ display: 'flex', alignItems: 'center', width: '100%', lineHeight: '26px',marginBottom: '3px' }}>
                                    <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1 }}>
                                        {item.title}
                                    </div>
                                </div>
                                {/* 发起人及接收时间 */}
                                <div style={{ color: '#8d8d8d', width: '100%', fontSize: 14 }}>
                                    <span className='over-style' style={{width: '16%'}} title={item.tag}>
                                        {item.tag}
                                    </span>
                                    <span className='over-style' style={{width: '12%'}}>
                                        状态：
                                        <span style={{color: '#1890ff'}} title={item.status}>{item.status}</span>
                                    </span>
                                    <span className='over-style' style={{width: '18%'}}>
                                        发起人：<span style={{color: '#444'}} title={item.creator}>{item.creator}</span>
                                    </span>
                                    <span className='over-style' style={{width: '18%'}}>
                                        发起时间：<span style={{color: '#444'}} title={item.createTime}>{item.createTime}</span>
                                    </span>
                                    <span className='over-style' style={{width: '18%'}}>
                                        应用系统：<span style={{color: '#444'}} title={item.appSystem}>{item.appSystem}</span>
                                    </span>
                                    <span className='over-style' style={{width: '18%'}}>
                                        应用程序：<span style={{color: '#444'}} title={item.appProgram}>{item.appProgram}</span>
                                    </span>
                                </div>
                            </List.Item>
                        )
                    }}
                />
            </div>
            <Pagination
                current={searchParams.page}
                pageSize={searchParams.size}
                total={total}
                showTotal={(total, range) => `共 ${total} 项数据`}
                showSizeChanger={true}
                onChange={handlePageChange}
                style={{ textAlign: 'center', marginTop: 10, float: 'right' }}
            />   
        </TabPane>
    )
    const tabMap = {
        'PaaS': paasTab,
        '存储': storageTab,
        '文档': documentTab,
        'API申请': apiTab,
        'API审批': apiTabApprove,
        '需求': demandTab,
        '任务': taskTab,
        '缺陷': defectTab,
        'TIS工单': tisTab,
        '代码合并请求': codeTab,
    };
    const fetchTabList = async () => {
        // 获取tab排序顺序 并存储到tabList
        let filteredWorkItems;
        // 如果 userInfo.adminRole 为 true，只保留特定的项
        if (userInfo.adminRole) {
            const specificItems = ['PaaS', '存储', '文档', 'API申请', 'API审批'];
            filteredWorkItems = workItemShow.filter(item => 
                specificItems.includes(item.name)
            );
        } else {
            filteredWorkItems = workItemShow.filter(item => item.show);
        }
        // 根据 sort 字段排序
        const sortedWorkItems = filteredWorkItems.sort((a, b) => a.sort - b.sort);
        // 提取 name 字段作为 tabList
        const response = sortedWorkItems.map(item => item.name);
        setTabList(response);
        setActiveTab(response[0]);
    };
    useEffect(() => {
        fetchTabList();
        fetchWorkList();
    }, [workItemShow]);
    useEffect(() => {
        fetchTabList();
    }, [userInfo]);
    useEffect(() => {
        const sorted = tabList.map(key => tabMap[key]);
        setSortedTabs(sorted);
    }, [tabList, listData])
    return (
        <div className='jobList' style={{height:'580px',overflow:'auto', background: '#fff', padding: 10}}>
            <div style={{ display: 'flex', alignItems: 'center', padding: '0px 10px', justifyContent: 'space-between', lineHeight: '32px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{ backgroundColor: '#008bd6', width: 6, height: 20, marginRight: 8, borderRadius: 3 }} />
                    <Typography.Title level={5} style={{ margin: 0, fontWeight: 500 }}>工作事项</Typography.Title>
                </div>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <Search
                        placeholder="请输入搜索内容"
                        onSearch={handleSearch}
                        onKeyDown={(e) => handleKeyChange(e)}
                        style={{ width: 200, marginRight: 16 }}
                    />
                    <RangePicker
                        onChange={handleDateChange}
                        showTime={true}
                        format={'YYYY-MM-DD HH:mm:ss'}
                        style={{ width: 330 }}
                    />
                </div>
            </div>
            <div className='list-content'>
                <div style={{position: 'relative'}}>
                    <Spin spinning={loadFlag} tip="加载中..." style={{ position: 'absolute', top: '200px', left: '50%', transform: 'translate(-50%, -50%)' }}>
                        <Tabs activeKey={activeTab} onChange={handleTabChange} style={{height: '56px', marginTop: '5px'}}>
                            {sortedTabs}
                        </Tabs>
                    </Spin>
                </div>
            </div>
        </div>
    );
};

export default JobList;
