import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Statistic, Table, List } from 'antd';
import "./index.less";
import { history } from 'umi';
import { getDictChildren } from '@/services/workbench';


const System = (props) => {
    const { appSystems } = props
    const [dataSource, setDataSource] = useState(appSystems);
    const [appOptions, setAppOptions] = useState([]);
    const [statusOptions, setStatusOptions] = useState([]);

    useEffect(() => {
        getDictChildren({ type: 'stage_status', current: 1, size: 1000 }).then(
            (res) => {
                setStatusOptions(res.records);
            },
        );
        getDictChildren({ type: 'business_domain', current: 1, size: 1000 }).then(
            (res) => {
                setAppOptions(res.records);
            },
        );
    },[])

    const handleAppClick = (item) => {
        history.push(`/kepler-webpage/svc/integrationService/applicationIntegration/detail/${item.systemId}/inner`)
    }

    const handleMoreClick = () => {
        history.push('/kepler-webpage/svc/integrationService/applicationIntegration')
    }

    useEffect(() => {
        setDataSource(appSystems)
    }, [appSystems])

    const findLabel = (value, options) => {
        const matchedOption = options.find(option => option.value === value);
        return matchedOption ? matchedOption.label : value;
    }
  
    return (
        <div className='systemList' style={{height:'270px',overflow:'auto', background: '#fff', padding: 10}}>
            <div style={{ display: 'flex', alignItems: 'center', padding: '0px 10px', justifyContent: 'space-between', lineHeight: '32px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{ backgroundColor: '#008bd6', width: 6, height: 20, marginRight: 8, borderRadius: 3 }} />
                    <Typography.Title level={5} style={{ margin: 0, fontWeight: 500 }}>最近访问应用系统</Typography.Title>
                </div>
                <div style={{ display: 'flex', alignItems: 'center', color: '#008BD6', cursor: 'pointer' }}>
                    <span onClick={() => handleMoreClick()}>更多</span>
                </div>
            </div>
            <div className='system-content'>
                <div style={{ display: 'flex', backgroundColor: '#fff', lineHeight: '42px', marginTop: '10px',padding: '0 7px 0 15px' }}>
                    <div style={{ width: '18%', flexShrink: 0, flexGrow: 0 }}>应用系统名称</div>
                    <div style={{ width: '28%', flexShrink: 0, flexGrow: 0 }}>应用系统英文名称</div>
                    <div style={{ width: '17%', flexShrink: 0, flexGrow: 0 }}>状态</div>
                    <div style={{ width: '17%', flexShrink: 0, flexGrow: 0 }}>业务域</div>
                    <div style={{ width: '20%', flexShrink: 0, flexGrow: 0 }}>应用系统管理员</div>
                </div>
                <div style={{height: '166px', overflow: 'auto'}}>
                    <List
                        dataSource={dataSource}
                        renderItem={(item) => (
                            <List.Item style={{ display: 'flex', lineHeight: '24px' }}>
                                <div className='over-style' style={{ width: '18%', textAlign: 'left' }}>
                                    <span title={item.cnName} onClick={() => handleAppClick(item)} style={{ color: '#008DDC', cursor: 'pointer' }}>{item.cnName}</span>
                                </div>
                                <div className='over-style' title={item.enName} style={{ width: '28%' }}>{item.enName}</div>
                                <div className='over-style' title={findLabel(item.stageStatus, statusOptions)} style={{ width: '17%' }}>
                                    {findLabel(item.stageStatus, statusOptions)}
                                </div>
                                <div className='over-style' title={findLabel(item.businessDomain, appOptions)} style={{ width: '17%' }}>
                                    {findLabel(item.businessDomain, appOptions)}
                                </div>
                                <div className='over-style' title={item.appAdmin} style={{ width: '20%' }}>{item.appAdmin}</div>
                            </List.Item>
                        )}
                    />
                </div>
            </div>
        </div>
    );
};

export default System;
