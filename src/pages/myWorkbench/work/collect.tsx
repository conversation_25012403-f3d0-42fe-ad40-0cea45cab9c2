import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Statistic, List } from 'antd';
import "./index.less";
import { history, useParams, useLocation } from 'umi';
import iconCollect from "../../../assets/images/iconCollect.svg"

const Collect = (props) => {
    const { documentBrowses } = props

    const handleDocClick = (item) => {
        let newPath = `/dochub/docs`
        if (item.id) {
            newPath = `/dochub/docs/detail/${item.id}`
        }
        history.push(newPath)
    }
    return (
        <div className='collectList' style={{ cursor: 'pointer', height:'580px',overflow:'auto', background: '#fff', padding: 10}}>
            <div style={{ display: 'flex', alignItems: 'center', padding: '0px 10px', justifyContent: 'space-between', height: '32px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{ backgroundColor: '#008bd6', width: 6, height: 20, marginRight: 8, borderRadius: 3 }} />
                    <Typography.Title level={5} style={{ margin: 0, fontWeight: 500 }}>文档浏览记录</Typography.Title>
                </div>
            </div>
            <div style={{height: '515px', overflow: 'auto', marginTop: '10px'}}>
                <List
                    dataSource={documentBrowses}
                    renderItem={(item) => (
                        <List.Item>
                            <div onClick={() => handleDocClick(item)} style={{ display: 'flex', alignItems: 'center', width: '100%', justifyContent: 'space-between' }}>
                                <img
                                    src={iconCollect}
                                    alt=""
                                    style={{ width: 18, height: 18, marginRight: 10 }}
                                />
                                <div title={item.alias} style={{ width: '50%', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis', flex: 1, marginRight: 20 }}>
                                    {item.alias}
                                </div>
                                {/* <div style={{ width: '30%', display: 'flex', alignItems: 'center',marginRight: '20px' }}>
                                    <div style={{ backgroundColor: '#ffc107', borderRadius: '50%', width: 20, height: 20, display: 'flex', alignItems: 'center', justifyContent: 'center', marginRight: 5 }}>
                                        <span style={{ display:'inline-block',width: 20, height: 20,textAlign:'center', color: '#fff', fontSize: 12 }}>{item.createBy[0]}</span>
                                    </div>
                                    <span title={item.createBy} style={{whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>{item.createBy}</span>
                                </div> */}
                                <span title={item.createTime} style={{  width: '25%',color: '#8d8d8d', fontSize: 14, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis'}}>{item.createTime}</span>
                            </div>
                        </List.Item>
                    )}
                />
            </div>
        </div>
    );
};

export default Collect;
