import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography, Statistic, Table, List, message } from 'antd';
import "./index.less";
import { CopyOutlined } from '@ant-design/icons';
import { history } from 'umi';


const App = (props) => {
    const { appPrograms } = props
    const [dataSource, setDataSource] = useState(appPrograms);

    const handleAppClick = (item) => {
        history.push(`/system/systemManage/${item.id}/baseInfo`)
    }

    const copyToClipboard = (text) => {
        navigator.clipboard.writeText(text)
           .then(() => {
                message.success('已复制到剪贴板');
            })
           .catch((error) => {
                message.error('复制失败：' + error.message);
            });
    };

    useEffect(() => {
        setDataSource(appPrograms)
    }, [appPrograms])

    return (
        <div className='appList systemList' style={{height:'270px',overflow:'auto', background: '#fff', padding: 10}}>
            <div style={{ display: 'flex', alignItems: 'center', padding: '0px 10px', justifyContent: 'space-between', lineHeight: '32px' }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div style={{ backgroundColor: '#008bd6', width: 6, height: 20, marginRight: 8, borderRadius: 3 }} />
                    <Typography.Title level={5} style={{ margin: 0, fontWeight: 500 }}>最近访问应用程序</Typography.Title>
                </div>
                {/* <div style={{ display: 'flex', alignItems: 'center', color: '#008BD6', cursor: 'pointer' }}>
                    <span>更多</span>
                </div> */}
            </div>
            <div className='system-content'>
                <div style={{ display: 'flex', backgroundColor: '#fff', lineHeight: '42px', marginTop: '10px',padding: '0 7px 0 15px' }}>
                    <div style={{ width: '20%', flexShrink: 0, flexGrow: 0 }}>应用程序名称</div>
                    <div style={{ width: '20%', flexShrink: 0, flexGrow: 0 }}>英文名称</div>
                    <div style={{ width: '45%', flexShrink: 0, flexGrow: 0 }}>代码仓库</div>
                    <div style={{ width: '15%', flexShrink: 0, flexGrow: 0 }}>开发负责人</div>
                </div>
                <div style={{height: '166px', overflow: 'auto'}}>
                    <List
                        dataSource={dataSource}
                        renderItem={(item) => (
                            <List.Item style={{ display: 'flex', lineHeight: '24px' }}>
                                <div className='over-style' style={{ width: '20%', textAlign: 'left' }}>
                                    <span title={item.programNameCn} onClick={() => handleAppClick(item)} style={{ color: '#008DDC', cursor: 'pointer' }}>{item.programNameCn}</span>
                                </div>
                                <div title={item.programNameEn} className='over-style' style={{ width: '20%' }}>{item.programNameEn}</div>
                                <div style={{ width: '45%' }}>
                                    <div style={{display: 'flex', alignItems: 'center', width: 'calc(100% - 20px)',whiteSpace: 'nowrap', textOverflow: 'ellipsis'}}>
                                        <span title={item.gitlabRepoUrl} style={{ maxWidth: 'calc(100% - 20px)', whiteSpace: 'nowrap',textOverflow: 'ellipsis',overflow: 'hidden'}}>
                                            {item.gitlabRepoUrl}
                                        </span>
                                        { item.gitlabRepoUrl &&
                                            <CopyOutlined
                                                style={{ marginLeft: 8, cursor: 'pointer', color: '#1890ff' }}
                                                title='复制'
                                                onClick={() => copyToClipboard(item.gitlabRepoUrl)}
                                            />
                                        }
                                        
                                    </div>
                                </div>
                                <div title={item.developDirector} className='over-style' style={{ width: '15%' }}>{item.developDirector}</div>
                            </List.Item>
                        )}
                    />
                </div>
            </div>
        </div>
    );
};

export default App;
