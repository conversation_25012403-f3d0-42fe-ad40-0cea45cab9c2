import React, {useEffect, useState} from 'react';
import { Card, Row, Col, Typography } from 'antd';
import "./index.less";
import { history, useParams, useLocation } from 'umi';
import Nav from './nav';
import JobList from './jobList';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import Collect from './collect';
import System from './system';
import App from './app';
import Count from './count';
import { SettingOutlined, SwapOutlined } from '@ant-design/icons';
import { getWorkbenchConfig, getWorkbenchData, getWorkbenchInfo } from '@/services/workbench';


const MyWorkbench = () => {
    const extraLocation = useLocation();
    const [userInfo, setUserInfo] = useState({
        userId: '',
        admin: false,
        userRealname: '',
        adminRole: false,
    })
    const [accessConfigs, setAccessConfigs] = useState([]) // 快捷入口
    const [appSystems, setAppSystems] = useState([])    // 最近使用系统
    const [appPrograms, setAppPrograms] = useState([])      // 最近使用程序
    const [documentBrowses, setDocumentBrowses] = useState([])      // 文档浏览记录
    const [statModule, setStatModule] = useState({})        // 统计模块区域
    const [systemNum, setSystemNum] = useState(0)
    const [programNum, setProgramNum] = useState(0)

    const [accessShow, setAccessShow] = useState([]); // 获取配置快捷入口配置信息
    const [moduleShow, setModuleShow] = useState([
        {name: '个人简介', show: true},
        {name: '工作事项', show: true},
        {name: '文档浏览记录', show: true},
        {name: '快捷入口', show: true},
        {name: '最近访问应用系统', show: true},
        {name: '最近访问应用程序', show: true},
    ]);   // 获取配置各功能模块展示信息
    const [workItemShow, setWorkItemShow] = useState([]);   // 获取工作事项排序和显示信息
    const [roleFlag, setRoleFlag] = useState(false)

    useEffect(() => {
        const fetchUserInfo = async () => {
            const resultRes = await getWorkbenchInfo();
            setRoleFlag(true)
            const resData = {
                userId: resultRes.id,
                admin: resultRes.admin, // 存在管理员角色 默认显示管理员
                userRealname: resultRes.userRealname,
                adminRole: extraLocation.search.includes('fromSet') ? false : resultRes.admin,     // 前端判断当前角色是否是管理员 可切换角色
            }
            setUserInfo(resData)
        };
        fetchUserInfo();
    },[])
    useEffect(() => {
        fetchConfigs()
    },[userInfo])
    useEffect(() => {
        if (roleFlag) {
            fetchDataInfo();
        }
    },[userInfo])
    const fetchConfigs = async () => {
        const resultRes = await getWorkbenchConfig();
        setModuleShow(resultRes.moduleShows);
        setAccessShow(resultRes.accessConfigs);
        setWorkItemShow(resultRes.workItems);
    }

    const shouldShowApp = moduleShow.find(item => item.name === '最近访问应用程序')?.show;
    const shouldShowSystem = moduleShow.find(item => item.name === '最近访问应用系统')?.show;
    const shouldShowDocument = moduleShow.find(item => item.name === '文档浏览记录')?.show;
    const systemSpan = shouldShowSystem && !shouldShowApp && !userInfo.adminRole ? 24 : 12;
    const appSpan = shouldShowApp && !shouldShowSystem && !userInfo.adminRole ? 24 : 12;
    const workSpan = !userInfo.adminRole && !shouldShowDocument ? 24 : 16;
    const documentSpan = !userInfo.adminRole && !shouldShowDocument ? 0 : 8;

    const fetchDataInfo = async () => {
        let queryId = 1
        if (userInfo.adminRole) {
            queryId = 0
        }
        getWorkbenchData(queryId).then(res => {
            if (res.systemNum) {
                setSystemNum(res.systemNum)
            }
            if (res.programNum) {
                setProgramNum(res.programNum)
            }
            if (res.accessConfigs && res.accessConfigs.length > 0) {
                setAccessConfigs(res.accessConfigs)
            }
            if (res.appSystems && res.appSystems.length > 0) {
                setAppSystems(res.appSystems)
            }
            if (res.appPrograms && res.appPrograms.length > 0) {
                setAppPrograms(res.appPrograms)
            }
            if (res.documentBrowses && res.documentBrowses.length > 0) {
                setDocumentBrowses(res.documentBrowses)
            }
            if (userInfo.adminRole && res.statModule) {
                setStatModule(res.statModule)
            }
        })
    };
    
    const linkList = [
        {
            title: '我的工作台'
        },
    ]

    const handleSetEvent = () => {
        history.push(`/myWorkbench/set`);
    }

    const handleRoleClick = () => {
        setUserInfo((prevParams) => ({
            ...prevParams,
            adminRole: !userInfo.adminRole,
        }));
    }

    return (
        <div className='workbench' style={{height:'100%',overflow:'auto', display: 'flex'}}>
            <div className='work-left'>
                {   userInfo.admin &&
                    <div 
                        style={{paddingLeft: '30px', paddingTop: '10px', cursor: 'pointer'}}
                        onClick={() => handleRoleClick()}
                    >
                        <SwapOutlined rotate={90} style={{marginRight: '5px'}} />
                        {userInfo.adminRole ? '管理员' : '个人用户'}
                    </div>
                }  
                <Nav moduleShow={moduleShow} userInfo={userInfo} accessConfigs={accessConfigs} systemNum={systemNum} programNum={programNum}></Nav>
            </div>
            <div className="work-content">
                <div style={{ display: 'flex', alignItems: 'center', background: '#fff' }}>
                    <BreadcrumbNav list={linkList} />
                    {   !userInfo.adminRole &&
                        <SettingOutlined
                            style={{ marginRight: '30px', fontSize: '20px', color: '#1890ff', cursor: 'pointer' }}
                            onClick={handleSetEvent}
                        />
                    }
                </div>
                <Row gutter={16} style={{marginTop: '10px',width: '100%', overflowX: 'hidden'}}>
                    <Col span={workSpan}>
                        <JobList workItemShow={workItemShow} userInfo={userInfo}></JobList>
                    </Col>
                    <Col span={documentSpan}>
                        {/* {userInfo.admin ?  <Count statModule={statModule} /> : <Collect documentBrowses={documentBrowses} />} */}
                        {userInfo.adminRole && <Count statModule={statModule} />}
                        {!userInfo.adminRole && shouldShowDocument && <Collect documentBrowses={documentBrowses} />}
                    </Col>
                </Row>
                <Row gutter={16} style={{marginTop: '10px',width: '100%', overflowX: 'hidden'}}>
                    {(shouldShowSystem || userInfo.adminRole) && (
                        <Col span={systemSpan}>
                            <System appSystems={appSystems} />
                        </Col>
                    )}
                    {(shouldShowApp || userInfo.adminRole) && (
                        <Col span={appSpan}>
                            <App appPrograms={appPrograms} />
                        </Col>
                    )}
                </Row>
            </div>
        </div>
    );
};

export default MyWorkbench;
