import React, {useEffect, useRef, useState} from 'react';
import { Card, Row, Col, Typography, Statistic, Avatar, Tag, List, Badge } from 'antd';
import "./index.less";
import { history } from 'umi';
import { BellOutlined, SoundFilled, SoundOutlined, UserOutlined } from '@ant-design/icons';
import { getNoticeList } from '@/services/workbench';


const Nav = (props) => {
  const { accessConfigs, programNum, systemNum, moduleShow, userInfo } = props
  const [avatarUrl, setAvatarUrl] = useState(''); // 这里可以根据实际情况设置头像的 URL

  // 模拟通知数据
  const [notices, setNotices] = useState([]);
  // 当前页码
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  // 加载状态
  const [loading, setLoading] = useState(false);
  // 滚动容器的引用
  const scrollRef = useRef(null);

  const shouldShowQuickEnter = moduleShow.find(item => item.name === '快捷入口')?.show;

  useEffect(() => {
    fetchWorkNoticeList(page)
  }, [])

  const fetchWorkNoticeList = async (currentPage) => {
    setLoading(true);
    const query = {
      size: 5,
      page: currentPage,
    }
    getNoticeList(query).then(res => {
      setLoading(false)
      setNotices([...notices, ...res.records]);
      const tempPage = page + 1;
      setPage(tempPage);
      setTotalPages(res.pages)
    }).finally(() => {
      setLoading(false)
    })
  };

    // // 模拟加载下一页数据的函数
    // const loadMoreData = async () => {
    //     if (loading || page >= totalPages) return;
    //     setLoading(true);
    //     try {
    //         // 模拟请求延迟
    //         await new Promise(resolve => setTimeout(resolve, 1000));
    //         // 生成新的数据
    //         const newData = Array.from({ length: 2 }, (_, index) => ({
    //             id: notices.length + index + 1,
    //             date: `2025-09-${18 - page - index}`,
    //             content: `这是第 ${notices.length + index + 1} 条通知内容`,
    //             isRead: Math.random() > 0.5
    //         }));
    //         setNotices([...notices, ...newData]);
    //         setPage(page + 1);
    //     } catch (error) {
    //         console.error('加载数据失败:', error);
    //     } finally {
    //         setLoading(false);
    //     }
    // };

  // 监听滚动事件
  useEffect(() => {
      const handleScroll = () => {
          const { scrollTop, scrollHeight, clientHeight } = scrollRef.current;
          if (scrollTop + clientHeight >= scrollHeight - 20) {
            if (loading || page > totalPages) return;
            fetchWorkNoticeList(page)
          }
      };
      const scrollContainer = scrollRef.current;
      scrollContainer.addEventListener('scroll', handleScroll);
      return () => {
          scrollContainer.removeEventListener('scroll', handleScroll);
      };
  }, [notices, page, loading]);

  const handleQuickClick = (item) => {
    window.open(item.url)
  }

  return (
    <div className='nav'>
      <div style={{ textAlign: 'center', padding: '20px 20px 40px' }}>
        {/* <Avatar
          size={80}
          src={avatarUrl ? avatarUrl : undefined}
          icon={!avatarUrl && <UserOutlined />}
        /> */}
        <Avatar
          size={80}
          style={{
            backgroundColor: 'rgba(40, 105, 246, 0.1)',
            color: '#2869F6',
            fontSize: '32px',
            borderRadius: '50%', // 设置圆角
          }}
        >
          {userInfo.userRealname?.charAt(0)}
        </Avatar>
        <Typography.Title level={4} style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis',marginTop: '16px' }}>
          欢迎使用，<span title={userInfo.userRealname}>{userInfo.userRealname}</span>
        </Typography.Title>
        {
          !userInfo.adminRole &&
          <Row justify="center" style={{ marginTop: '8px' }}>
            <Col>
              <div style={{height: '30px', lineHeight: '30px', background: '#f5f5f5', borderRadius: '15px', padding: '0 20px'}}>应用系统 <span style={{fontWeight: 600}}>{systemNum}</span> 个</div>
            </Col>
          </Row>
        }
        {
          !userInfo.adminRole &&
          <Row justify="center" style={{ marginTop: '16px' }}>
            <Col>
              <div style={{height: '30px', lineHeight: '30px', background: '#f5f5f5', borderRadius: '15px', padding: '0 20px'}}>应用程序 <span style={{fontWeight: 600}}>{programNum}</span> 个</div>
            </Col>
          </Row>
        }
      </div>
      <div className='notice-content'>
        <div style={{ display: 'flex', alignItems: 'center', padding: '0px 20px' }}>
          <SoundFilled style={{ marginRight: 8, color: '#008bd6' }} />
          <Typography.Title level={5} style={{ margin: 0, marginRight: 8 }}>通知公告</Typography.Title>
          <span style={{color: '#bfbfbf', fontSize: '12px', alignSelf: 'flex-end'}}>(未读)</span>
        </div>
        <div style={{height: '285px', overflow: 'auto'}} ref={scrollRef}>
          <List
            dataSource={notices}
            bordered
            renderItem={(item) => (
              <List.Item
                style={{ borderBottom: '1px solid #e8e8e8' }}
              >
                <List.Item.Meta
                  avatar={
                    <Badge
                      dot
                      style={{
                        backgroundColor: item.isRead ? '#bfbfbf' : '#f5222d',
                        cursor: 'default'
                      }}
                    />
                  }
                  title={
                    <Typography.Text
                      style={{color: '#bfbfbf'}}
                    >
                      {item.createTime}
                    </Typography.Text>
                  }
                  description={
                    <div style={{
                        maxWidth: '100%',
                        whiteSpace: 'nowrap',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        cursor: 'pointer'
                    }}>
                        <Typography.Text
                            title={item.content}
                        >
                            {item.content}
                        </Typography.Text>
                    </div>
                  }
                />
              </List.Item>
            )}
          />
          {loading && <div style={{ textAlign: 'center', padding: '10px' }}>加载中...</div>}
        </div>
      </div>
      {(shouldShowQuickEnter || userInfo.adminRole) && 
        <div className='quick-content'>
          <div style={{ display: 'flex', alignItems: 'center', padding: '0px 20px', marginBottom: '20px' }}>
            <div style={{ backgroundColor: '#008bd6', width: 6, height: 20, marginRight: 8, borderRadius: 3 }} />
            <Typography.Title level={5} style={{ margin: 0, fontWeight: 500 }}>快捷入口</Typography.Title>
          </div>
          <div style={{ height: userInfo.adminRole? '290px' : '200px', overflow: 'auto'}}>
            <Row gutter={16} justify="left">
              {accessConfigs.map((entry, index) => (
                <Col span={12} key={index} style={{padding: '16px 0', float: 'left'}}>
                  <div style={{ textAlign: 'center', cursor: 'pointer' }} onClick={() => handleQuickClick(entry)}>
                    {entry.logoUrl ? (
                      <img
                        src={entry.logoUrl}
                        style={{ width: 32, height: 32, marginBottom: 8, margin: 'auto' }}
                      />
                    ) : (
                      <div className='entry-icon'>
                        {entry.name[0] || ''}
                      </div>
                    )}
                    <div style={{ marginTop: '15px' }}>{entry.name}</div>
                  </div>
                </Col>
              ))}
            </Row>
          </div>
        </div>
      }
    </div>
  );
};

export default Nav;