.workbench {
    .work-left {
        width: 248px;
        height: 910px;
        background: #fff;
    }
    .nav {
        height: 888px;
        display: flex;
        flex-direction: column;
        .notice-content {
            .micro-list {
                border: none;
                padding: 5px 15px;
                .micro-list-item {
                    padding: 10px 0;
                }
            }
        }
        .quick-content {
            margin-top: 40px;
            flex: 1;
            display: flex;
            flex-direction: column;
            .entry-icon {
                width: 32px;
                height: 32px;
                line-height: 32px;
                margin-bottom: 8px;
                font-size: 18px;
                margin: auto;
                background-color: rgba(40, 105, 246, 0.1); // 可以根据需求修改背景颜色
                display: flex;
                justify-content: center;
                        
            }
        }
    }
    .work-content {
        flex: 1;
        padding-left: 8px;
        box-sizing: border-box;
        .list-content {
            padding: 0 15px;
            .micro-list {
                .micro-list-item {
                    padding: 8px 0;
                    
                    .over-style {
                        display: inline-block;
                        float: left;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
        .systemList {
            tr {
                th,td {
                    border: none;
                }
                th {
                    font-weight: normal;
                }
                th::before {
                    display: none;
                }
            }
            .system-content {
                .micro-list-item {
                    padding-left: 15px;
                }
                .over-style {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
        .countList {
            .rectangle {
                display: inline-block;
                background-color: #008bd6;
                width: 6px;
                height: 20px;
                margin-right: 8px;
                border-radius: 3px;
            }
            .micro-list {
                .micro-list-item {
                    padding: 12px 10px;
                    justify-content: left;
                    border: none;
                }
                .text-style {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}