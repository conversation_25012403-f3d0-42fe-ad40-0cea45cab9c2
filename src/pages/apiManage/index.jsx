import React, { useEffect, useRef, useState } from "react";
import { Input, Tabs , Affix, Menu , Checkbox,message,Empty } from "antd";
import "./index.less";
import "./apiManageIndex.less";
import { history } from "umi";
import { getAllMiddlewareAppList,addFavorite,removeFavorite } from '@/services/middleware';
import MySubscriptions from './apiManageComponent/MySubscriptions'
import SubscripLogC from './apiManageComponent/SubscripLog'
import {
  PieChartOutlined
} from '@ant-design/icons';
import { Link, Outlet } from 'umi';





const apiManage = ({searchValue}) => {

  

  const [current, setCurrent] = useState('1');
  
  useEffect(() => {  
  getList()       
  }, [searchValue]);

  const getList = () => {
   
  }

  const onClick = (e) => {
  
    setCurrent(e.key);
  };
  return (
    <div className="dealPadding products-api-manage">
      <div>
        <div className="sidebar sidebar-expand">
          <div className="sidebar-content">
            <Menu mode="vertical"  selectedKeys={[current]} style={{border:'0px'}} onClick={onClick}>
              <Menu.Item key="1">
                <Link to="/apiManage/subscrip">订阅列表  <PieChartOutlined /></Link>
              </Menu.Item>
              <Menu.Item key="2">
                <Link to="/apiManage/subscripLog">订阅日志查询 <PieChartOutlined /></Link>
              </Menu.Item>
             
            </Menu>
          
          </div>
        
        </div>
      </div>

      <div className="main-content">
      
        <>
        <Outlet />
        </>
       
      
       
     
        
      </div>
    </div>
  );
};


export default apiManage;
