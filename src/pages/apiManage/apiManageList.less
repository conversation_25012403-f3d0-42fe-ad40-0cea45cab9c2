.products-wrapper {
    display: flex;
    align-items: stretch;
    min-height: 600px;
    padding-top: 0 !important;
    gap: 24px;
    .group-name {
      font-weight: 500;
      font-size: 20px;
      line-height: 32px;
      margin-bottom: 8px;
      font-size: 16px;
    }
  
    .search {
      border-bottom: 1px solid #e6e6e6;
  
      button {
        box-sizing: content-box;
        border-top-color: transparent;
        border-radius: 0;
        border-left: none;
        border-right: none;
        background: transparent;
        border-bottom-color: transparent;
      }
  
      .@{ant-prefix}-input-group-addon {
        background: transparent;
      }
  
      .@{ant-prefix}-input-affix-wrapper {
        border-right: none;
        // border-bottom: 1px solid rgb(217, 217, 217);
        border-radius: 0;
  
        &.@{ant-prefix}-input-affix-wrapper-focused {
          border-bottom-color: @primary-color;
          & + span button {
            border-bottom-color: @primary-color;
          }
        }
      }
    }
  
    .left-sidebar {
      flex: 240px 0 0;
      background: #fff;
      padding-top: 24px;
      line-height: 32px;
      padding: 10px;
  
      // .item-padding {
      //   padding: 0 24px;
      // }
      .nav-filter {
        font-size: 16px;
      }
      .nav-btn {
        padding-left: 0;
        text-align: left;
        margin-bottom: 12px;
      }
      .nav-list {
        width: 100%;
        padding: 0 20px;
      }
      .nav-item {
        cursor: pointer;
        line-height: 36px;
        display: flex;
        column-gap: 4px;
        margin: 0;
        padding-left: 16px;
        &:hover {
          background: #e6f3fb;
        }
      }
  
      .search-bar {
        padding:  0 24px 16px;
      }
    }
    .main-content {
      flex: 1 0 0;
      width: 0;
  
      .search-bar {
        padding-top: 24px;
        padding-bottom: 16px;
        background: #fff;
  
        .search-tip {
          padding-left: 8px;
          padding-top: 8px;
          color: @text-color-secondary;
        }
      }
  
      .hide-search-bar {
        padding: 0;
      }
  
      .no-search-bar {
        padding-top: 24px;
      }
  
      .prod-list {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
  
        padding-bottom: 48px;
        grid-gap: 20px;
  
        @media screen and (max-width: 992px) {
          grid-template-columns: 1fr 1fr;
        }
        @media screen and (max-width: 768px) {
          grid-template-columns: 1fr;
        }
      }
      .prod-card {
        background: #fff;
        border-radius: 8px;
        border: 1px solid #e6e6e6;
        transition: all 0.3s ease;
        padding: 20px;
  
        line-height: 24px;
  
        &-title {
          font-size: 16px;
          font-weight: 500;
        }
  
        &-desc {
          padding-top: 12px;
          color: #767676;
        }
  
        // &:hover {
        //   transform: translateY(-2px);
        //   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        // }
  
        &.prod-card-link:hover {
          cursor: pointer;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
        }
      }
    }
    .menu-box{
      .micro-menu-item{
        border-radius: 0px ;
      }
    }
  }
  
  
  
  