import React,{ useState,useEffect } from 'react';
import { Input, Button, Space, Select, Tooltip, Affix,Empty,Pagination,DatePicker  } from 'antd';
import { history } from 'umi';
import './apiManageList.less';
import './index.less';
const { RangePicker } = DatePicker;
const dateFormat = 'YYYY-MM-DD';


const apiManageList = ({prodcut,getList,total,tagId}) => {
  // 处理卡片点击事件，跳转到详情页面
  const handleCardClick = (api) => {
    history.push(`/apiMarket/detail/${ api.id }`, { api });
  };
  const [currentPage, setCurrentPage] = useState(1);
  const [name, setName] = useState('');
  const [pubOrgName, setPubOrgName] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [apiPath, setApiPath] = useState('');
  const [createTime, setCreateTime] = useState([]);
  
  
    // 处理分页变化
    const onPageChange = (page,pageSize) => {
      setCurrentPage(page);
      setPageSize(pageSize);
      getList({pageSize:pageSize,pageNo:page,name:name,pubOrgName:pubOrgName,tagId:tagId,"createTime[0]":createTime[0],"createTime[1]":createTime[1],apiPath:apiPath})
    };
    // useEffect(() => {
    //   setCurrentData(prodcut.slice((currentPage - 1) * pageSize, currentPage * pageSize))
    // }, [currentPage,prodcut]);
    useEffect(() => {
      setCurrentPage(1);
      console.log(createTime)
      getList({pageSize:pageSize,pageNo:1,name:name,pubOrgName:pubOrgName,tagId:tagId,"createTime[0]":createTime[0],"createTime[1]":createTime[1],apiPath:apiPath})
    }, [name,pubOrgName,tagId,apiPath,createTime]);
  return (
    <div className="pass-ser pass-ser-api-manage" style={{paddingTop:'0px'}}>
      <div className="main-content">
        <div style={{display:'flex',alignItems:'center',margin:'10px 0px',marginTop:'25px'}}>
          <Input placeholder="请输入名称搜索" style={{width:'180px'}}  onChange={e => setName(e.target.value)} />
          <Input placeholder="请输入发布组搜索" style={{width:'180px',margin:'0 10px'}} onChange={e => setPubOrgName(e.target.value)} />
          <Input placeholder="请输入接口编号/路径" style={{marginRight:'10px',width:'180px'}} onChange={e => setApiPath(e.target.value)} />
          <RangePicker format={dateFormat}    onChange={(value, dateString) => {
            setCreateTime([`${dateString[0]} 00:00:00`,`${dateString[1]} 23:59:59`])
            console.log('Selected Time: ', value);
            console.log('Formatted Selected Time: ', dateString);
          }} />
        </div>
       
        {
          prodcut?.length > 0?   <div className="api-list">
          {prodcut.map((api) => (
            <div
              key={api.id}
              className={api.name.indexOf('建设中')> -1 ?'api-card api-card-disabled':'api-card'}
              onClick={() => handleCardClick(api)}
              style={{ cursor: 'pointer' }}
            >
              <div className="card-header">
                <div  className="card-header-main">
                <div className="header-content">
                    <div className="api-logo">
                        {api.name?.charAt(0)}
                    </div>
                    <div style={{marginLeft:'15px'}}>
                      <h4 className="api-name">{api.name}</h4>
                      <div className="api-tags">
                        <span >{api.code}</span>
                      </div>
                    </div>
                  </div>
                
                </div>
              </div>
              <div className="card-info">
               
                <div>
                  <div style={{marginBottom:'10px'}}><span className="card-info-item" ><div className='card-title-left'>版本：</div>{api.version}</span></div>
                  <div style={{margin:'10px 0px'}}><span className="card-info-item"><div className='card-title-left'>发布组：</div>{api.pubOrgName}</span></div>
                  <div><span className="card-info-item"><div className='card-title-left'>产品描述：</div>{api.desc}</span></div>
                </div>
              </div>
            </div>
          ))
        }
        </div>:<Empty description="暂无数据" style={{ height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center'}}/>
        }
        
        <div  style={{marginTop:'10px'}}>
        <Pagination current={currentPage} showTotal={total => `共 ${total} 项数据`}   showSizeChanger onChange={onPageChange} total={total} pageSize={pageSize} style={{float:'right'}} />
        </div>
        
      </div>
    </div>
  );
};

export default apiManageList; 


