import React, { useEffect, useState } from 'react';
import { useParams, history } from 'umi';
import { CopyOutlined} from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import './MySubscriptions.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE, API_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Spin, DatePicker ,Input,Button ,Table,message,Col, Row } from 'antd';
import  apiLogoIMage from '@/assets/images/apiLogo.png';
import { getOrderPage } from '@/services';

import ApiDebugger from './ApiDebugger'
import { set } from 'nprogress';
// 开始
const { RangePicker } = DatePicker;
const { Search } = Input;
const MySubscriptions = ({setOPerateData}) => {
  const location = useParams();
  // 从路由中获取 type 和 chartName 参数
  const { id } = location || {};
  const apiId = id;
  const [productName, setProductName] = useState('');
  const [apiPath, setApiPath] = useState('');
  const [pubOrgName, setPubOrgName] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  const [timeRange, setTimeRange] = useState();
  const [total, setTotal] = useState();
  const [time, setTime] = useState([]);
  useEffect(() => {
   
  }, [])
  const handleBack = () => {
    history.back();
  };

 
  ///////开始//////////////
  const linkList = [
    {
      title: 'API管理'
    },
    {
      title: `我的订阅`
    },
  ]
const [data, setData] = useState();
const onPageChange = (page,pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };
  const resetSearch = () => {
    setCurrentPage(1)
    setTimeRange(null)
    setTime(null);
    setProductName(null);
    setApiPath('')
    setPubOrgName('')
    getList(false,{
          pageNo:1,
          pageSize:pageSize,
          productName:null,
          apiPath:null,
          pubOrgName:null
      })
      
    }
const getList = (isSearch,param) => {
  if(isSearch){
    setCurrentPage(1)
  }
  let dataParam = {}
  if(param){
      dataParam = param;
  }else{
      let list = {
        productName:productName,
        apiPath:apiPath,
        pubOrgName:pubOrgName,
        pageNo:isSearch ? 1 : currentPage,
        pageSize:pageSize,
        startTime:time?.[0] ? time?.[0] + ' 00:00:00' : '',
        endTime:time?.[1] ? time?.[1] + ' 23:59:59' : ''
      }
      // const timeParam = {
      //   "createTime[0]":time?.[0] ? time?.[0] + ' 00:00:00' : '',
      //   "createTime[1]":time?.[1] ? time?.[1] + ' 23:59:59' : ''
      // };
      // const encodedTimeParam = {};
  
      //     for (const key in timeParam) {
      //         const encodedKey = encodeURI(key);
      //         encodedTimeParam[encodedKey] = timeParam[key];
      //     }
      dataParam = {...list}
  }
 
  getOrderPage(dataParam).then((res) => {
    if(res){
      setData(res.list || [])
      setTotal(res.total)
    }else{
      message.error(res?.message || res?.msg)
    }
  }).catch((data) => {
    message.error(data?.msg)
  })
}
const onChange = (dates, dateStrings) => {
  setTimeRange(dates)
  setTime(dateStrings)
}; 
  
const formatDates = (dates) => {
  if (dates && dates.length === 2) {
    return dates.map(date => date.toISOString());
  }
  return [];
};  
useEffect(() => {
  getList()
}, [pageSize,currentPage]);

const goDebugger = (data) => {
  history.push(`/apiManage/apiDebugger/${ data.productId }`, { data })
}
  


  const columns = [
    {
      title: '产品名称',
      dataIndex: 'productName',
      key: 'productName',
      render: (text) => <span>{text}</span>,
    },
    {
      title: '编码',
      key: 'productCode',
      dataIndex: 'productCode'
    },
    {
      title: '产品版本',
      dataIndex: 'productVersion',
      key: 'productVersion',
    },
    
    {
      title: '发布组',
      dataIndex: 'pubOrgName',
      key: 'pubOrgName',
    },
    {
      title: '订阅组',
      key: 'subOrgName',
      dataIndex: 'subOrgName'
    },
    {
        title: 'ClientID',
        key: 'clientId',
        dataIndex: 'clientId'
      },
      {
        title: '创建时间',
        key: 'createTime',
        dataIndex: 'createTime'
      },
   
    {
      title: '操作',
      key: '操作',
      render: (api, record) => (
        <span size="middle" onClick={()=>{goDebugger(record)}}>
          <a>在线调试</a>
        </span>
      ),
    },
  ];

  return (
    <div className="mySubscriptions sub-app-box-container">
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div  className='micro-spin-container-box sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header">
          <div className="middleware-base-info">
          <Row>
            <Col span={12}>
                <div className='mySubscriptions-search'>
                    <span>产品名称：</span>
                    <Input placeholder="请输入产品名称" style={{width:'350px'}} value={productName} onChange={(e)=>{setProductName(e.target.value)}} />
                </div>
            </Col>
            <Col span={12}>
                <div className='mySubscriptions-search'>
                    <span>发布组：</span>
                    <Input placeholder="请输入发布组名称" style={{width:'350px'}} value={pubOrgName} onChange={(e)=>{setPubOrgName(e.target.value)}} />
                </div>
            </Col>
            
        </Row>
        <Row>
        <Col span={12}>
            <div className='mySubscriptions-search'>
                <span>接口编号/路径：</span>
                <Input placeholder="请输入接口编号/路径" style={{width:'350px'}} value={apiPath} onChange={(e)=>{setApiPath(e.target.value)}} />
            </div>
        </Col>
        <Col span={9}>
            <div className='mySubscriptions-search'>
                <span>创建时间：</span>
                  <RangePicker
                    format="YYYY-MM-DD"
                    style={{ width: 350 }}
                    onChange={onChange}
                    value={timeRange} 
                  />
            </div>
        </Col>
        <Col span={3}>
            <div >
                <Button style={{margin:'0px 5px'}} onClick={()=>{getList(true)}}>搜索</Button>
                <Button onClick={()=>{resetSearch()}}>重置</Button>
            </div>
        </Col>
        
    </Row>
        
            

          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div>
            <Table columns={columns} dataSource={data} 
                pagination={{
                  current:currentPage,
                  showTotal:()=>{return `共 ${total} 项数据`},
                  showSizeChanger:true,
                  onChange:(page,pageSize)=>{onPageChange(page,pageSize)},
                  total:total,
                  pageSize:pageSize
                }}
            />
          </div>
        </div>
      </div>
     
     
    </div>
  );
};



const MySubscriptionsC = () => {
    const [oPerateData, setOPerateData] = useState();

    return (
      <>
            {
                !oPerateData?<MySubscriptions setOPerateData={setOPerateData} />:<ApiDebugger oPerateData={oPerateData}/>
            }
            
        
      </>
    );
  };
  
  export default MySubscriptionsC;