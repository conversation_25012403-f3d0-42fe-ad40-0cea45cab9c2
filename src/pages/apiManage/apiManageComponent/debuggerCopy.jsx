import React, { useState,useEffect  } from 'react';
import { 
  Card, 
  Button, 
  Input, 
  Select, 
  Form, 
  Divider, 
  Row, 
  Col, 
  Tabs, 
  Table, 
  Layout,
  Typography,
  message,
  Radio
} from 'antd';
import { SendOutlined, PlusOutlined, DeleteOutlined  } from '@ant-design/icons';
import axios from 'axios';
import './ApiDebugger.less';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import EditableTable from './TableEdit'
import { getApiDetailInfoData,getApiSend } from '@/services';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import { useParams, history,useLocation } from 'umi';
import  noResponseImage from '@/assets/images/noResponse.png';

const { Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { Header, Footer, Sider, Content } = Layout;
const HTTP_METHODS = ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'];
const BODY_TYPES = ['form-data', 'x-www-form-urlencoded', 'raw'];
const headerStyle = {
    textAlign: 'center',
    color: '#fff',
    height: 64,
    paddingInline: 48,
    lineHeight: '64px',
    backgroundColor: '#4096ff',
  };
  
  const contentStyle = {
    textAlign: 'center',
 
    color: '#fff',
    backgroundColor: '#fff',
    marginRight:'8px'
  };
  

const ApiDebugger = () => {
  const location = useParams();
  const { pid } = location || {};
  const productId = pid;
  const oPerateData = useLocation().state?.data;
  const linkList = [
    {
      title: 'API管理'
    },
    {
      href: '/#/apiManage/subscrip',
      title: `我的订阅`
    },
    {
      title: `在线调试`
    },
  ]
  const [form] = Form.useForm();
  const [response, setResponse] = useState();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('path');
  const [pathData, setPathData] = useState([]);
  const [queryData, setQueryData] = useState([]);
  const [headerData, setHeaderData] = useState([]);
  const [bodyData, setBodyData] = useState({});
  const [method, setMethod] = useState();
  const [fullPathList, setFullPathList] = useState([]);
  const [paramList, setParamList] = useState({});
  const [url, setUrl] = useState('');
  const [bodyType, setBodyType] = useState('bodyRawParam');
  const [selectList, setSelectList] = useState({
    pathList:[],
    queryList:[],
    headersList:[],
    bodyFormDataList:[],
    bodyWwwList:[]
  });
  const [selectedRowKeysTotal, setSelectedRowKeysTotal] = useState({
    pathList:[],
    queryList:[],
    headersList:[],
    bodyFormDataList:[],
    bodyWwwList:[]
  });
   useEffect(() => {

    const pathList = getInitSelectArray(paramList.path);
    selectedRowKeysTotal.pathList = pathList;

    const queryList = getInitSelectArray(paramList.query);
    selectedRowKeysTotal.queryList = queryList;

    const headersList = getInitSelectArray(paramList.headers);
    selectedRowKeysTotal.headersList = headersList;

    const bodyFormDataList = getInitSelectArray(dataSourceOrigin.bodyFormData);
    selectedRowKeysTotal.bodyFormDataList = bodyFormDataList;

    
    const bodyWwwList = getInitSelectArray(dataSourceOrigin.bodyWww);
    selectedRowKeysTotal.bodyWwwList = bodyWwwList;
    console.log(selectedRowKeysTotal)
    setSelectedRowKeysTotal({...selectedRowKeysTotal})
    
    // setSelectedRowKeys()

   }, [paramList])
  
  
  
  const [initData,setInitData] = useState([])
function classifyParameters(apiArray) {
  const classification = {
      query: [],
      body: [],
      path: [],
      formdata: [],
      bodyRawParam:'',
      bodyFormData:[],
      bodyWww:[],
      headers:[]
  };
  apiArray.parameters.forEach(param => {
    const paramType = param.in ? param.in : 'unknown';
    if(apiArray.consumes === 'application/json' || !apiArray.consumes){
      if(paramType === 'body'){
        classification["bodyRawParam"] = param.reqBodyJson
      }else{
        classification[paramType].push(param);
      }
    }else if(apiArray.consumes === 'multipart/form-data'){
      if(paramType === 'formData'){
        classification["bodyFormData"].push(param)
      }else{
        classification[paramType].push(param);
      }
    }else if(apiArray.consumes === 'application/x-www-form-urlenco'){
      if(paramType === 'formData'){
        classification["bodyWww"].push(param)
      }else{
        classification[paramType].push(param);
      }
    }
});
addKey(classification.query)
addKey(classification.path)
addKey(classification.headers)
addKey(classification.bodyFormData)
addKey(classification.bodyWww)
return classification;
}
const addKey = (array) => {
    array.forEach((item, index) => {
    item.key = index; // 直接修改原对象
  });
}
const getApiDetailInfo = () => {
  const param = {
    id:productId
  }
  getApiDetailInfoData(param).then((res) => {
    if(res){
      const apiEndpoints = res.apiEndpoints || [];
      setInitData(apiEndpoints)

    }
  })
}
useEffect(() => {
  getApiDetailInfo();
 }, [])
 const arrayToObj = (array) => {
  const result = {};
  (array && array.length > 0) && array.forEach(item => {
    result[item.name] = item.exampleValue;
  });
  return result;
 }
 //获取最终勾选的数据（处理勾选数据可能被更改情况）
 const getSelectData = (selectData,originData) => {
  if(selectData.length === 0 || !selectData || !originData){
    return;
  }
  const originDataKeyMap = new Map();
  originData.forEach(item => originDataKeyMap.set(item.key, item));

  // 2. 倒序遍历数组A（避免删除元素导致的索引错乱）
  for (let i = selectData.length - 1; i >= 0; i--) {
    const currentKey = selectData[i].key;
    
    if (originDataKeyMap.has(currentKey)) {
      // 情况1：key在B中存在 → 更新A当前项为B的对应项
      selectData[i] = originDataKeyMap.get(currentKey);
    } else {
      // 情况2：key在B中不存在 → 从A删除该项
      selectData.splice(i, 1);
    }
  }
 }

  const handleSubmit = async () => {
    const values = await form.validateFields();
    getSelectData(selectList.headersList,paramList.headers)
    getSelectData(selectList.pathList,paramList.path)
    getSelectData(selectList.queryList,paramList.query)
    getSelectData(selectList.bodyFormDataList,paramList.bodyFormData)
    getSelectData(selectList.bodyWwwList,paramList.bodyWww)
    console.log(selectList.headersList)
    let headerObj = arrayToObj(selectList.headersList);
    headerObj["tsl-clientid"] = oPerateData?.clientId || '';
    headerObj["tsl-clientsecret"] = oPerateData?.clientSecret || '';
      const dataParam = {
        "api_method": values.method,
        "api_url": values.url,
        "api_header":JSON.stringify(headerObj) ,
        "api_body": paramList.bodyRawParam,
        "api_pathParams": JSON.stringify(arrayToObj(selectList.pathList))
      }
      if(selectList.queryList.length > 0){
        selectList.queryList.forEach(item => {
          dataParam[item.name] = item.exampleValue;
        });
      }

      const config = {
        method: "post",
        url: `/kepler/integration/app-market/api/send`,
        headers: buildHeaders(values),
        params:dataParam,
        data:buildBody(values)?buildBody(values):{}
      };
      console.log(config)
      try {
        const res = await axios(config);
        let info = res.data?.data || {};
        console.log("aaa")
        console.log(info)
        if(info?.data){
          try {
            const parsedData = JSON.parse(info.data);
            const formattedJSON = JSON.stringify(parsedData, null, 2);
            info.data = formattedJSON
          } catch (e) {
            console.error("Invalid JSON:", e);
          }
        }
        setResponse({
          status: info.status,
          statusText: info.statusText,
          headers: info.headers,
          data: info.data,
          time: info.time,
          url:info.requestUrl,
          requestBody:info.requestBody,
          requestHeaders:info.requestHeaders
        });
    } catch (error) {
      console.log(error.response)
      message.error(error.message);
    } finally {
      setLoading(false);
    }
  };

  // const buildUrl = (values) => {
  //   let url = values.url;
   
  //   // 处理 path 参数
  //   if (selectList.pathList.length > 0) {
  //     selectList.pathList.forEach(param => {
  //       if (param.name && param.exampleValue) {
  //         console.log(url.replace(`{${param.name}}`, param.exampleValue))
  //         url = url.replace(`{${param.name}}`, param.exampleValue);
  //       }
  //     });
  //   }
    
  //   // 处理 query 参数
  //   if (selectList.queryList.length > 0) {
  //     const queryString = selectList.queryList
  //       .filter(param => param.name && param.exampleValue)
  //       .map(param => `${encodeURIComponent(param.name)}=${encodeURIComponent(param.exampleValue)}`)
  //       .join('&');
      
  //     if (queryString) {
  //       url += (url.includes('?') ? '&' : '?') + queryString;
  //     }
  //   }  
  //   return url;
  // };

  const buildHeaders = (values) => {
    const headers = {};
    if(selectList.bodyFormDataList.length > 0 || selectList.bodyWwwList.length > 0 || paramList.bodyRawParam){
      if (bodyType === 'bodyWww' && selectList.bodyWwwList.length > 0) {
        headers['Content-Type']='application/x-www-form-urlencoded'
      }else if(bodyType === 'bodyRawParam' && paramList.bodyRawParam){
        headers['Content-Type']='application/json'
      }
    }
  
    
    console.log(headers)
    return headers;
  };  
  const buildBody = (values) => {
      if (values.method === 'GET') return undefined;
      // 处理 body 参数
      let requestBody = null;
      if (selectList.bodyWwwList.length > 0  || selectList.bodyFormDataList.length>0 || paramList.bodyRawParam) {
        switch (bodyType) {
          case "bodyFormData":
            const formData = new FormData();
            selectList.bodyFormDataList.forEach(item => {
              if (item.name) formData.append(item.name, item.exampleValue);
            });
            requestBody = formData;
            break;
            
          case "bodyWww":
            const params = new URLSearchParams();
            selectList.bodyWwwList.forEach(item => {
              if (item.name) params.append(item.name, item.exampleValue);
            });
            requestBody = params.toString();
          
            break;
            
          case "bodyRawParam":
            try {
              requestBody = JSON.parse(paramList.bodyRawParam);
            
            } catch (e) {
              console.error("Invalid JSON");
              return;
            }
            break;
        }
      }
      return requestBody;
  };
  const handleChange = (value) => {
  
   const item = initData.filter((item)=>{return item.path === value});
   setFullPathList(item[0]?.fullPath);
  
  form.setFieldsValue({
    url:'',
    method:item[0]?.httpMethod
  })
  let paramList =  classifyParameters(item[0]);
  if(paramList?.bodyRawParam){
    const jsonObject= JSON.parse(paramList?.bodyRawParam);
    const formattedJsonRequest = JSON.stringify(jsonObject, null, 4);
    paramList.bodyRawParam = formattedJsonRequest;
  }
  setParamList(paramList)
  
    // 这里可以添加业务逻辑处理^^1^^2^^
  }; 
  //切换api，重新获取相关参数
  const handleChangeApi= (value) => {
    setUrl(value)
    console.log('选中值:', value); 
  }; 
 
  //删除参数
   const handleDelete = (key) => {
    if(activeTab === 'path'){
      const a = paramList.path?.filter((item) => item.key !== key);
      const b = selectList.pathList.filter((item) => item.key !== key);
      paramList.path = [...a];
      selectList.pathList = [...b]
      setParamList({...paramList})
      setSelectList({...selectList})
    }else if(activeTab === 'query'){
      const a = paramList.query?.filter((item) => item.key !== key);
      const b = selectList.queryList.filter((item) => item.key !== key);
      console.log(a)
      paramList.query = [...a];
      selectList.queryList = [...b]
      setParamList({...paramList})
      setSelectList({...selectList})
    }else if(activeTab === 'headers'){
      const a = paramList.headers?.filter((item) => item.key !== key);
      const b = selectList.headersList.filter((item) => item.key !== key);
      console.log(a)
      paramList.headers = [...a];
      selectList.headersList = [...b]
      setParamList({...paramList})
      setSelectList({...selectList})
    }else if(activeTab === 'body'){
        const a = paramList[bodyType]?.filter((item) => item.key !== key);
        paramList[bodyType] = [...a];
        setParamList({...paramList})
        if(bodyType === 'bodyFormData'){
          const b = selectList.bodyFormDataList.filter((item) => item.key !== key);
          selectList.bodyFormDataList = [...b]
        }else{
          const b = selectList.bodyWwwList.filter((item) => item.key !== key);
          selectList.bodyWwwList = [...b]
        }
        setSelectList({...selectList})
    }
    // setDataSource(dataSource.filter(item => item.key !== key));
  };
  const handleChangeCode = (value) => {
    paramList.bodyRawParam = value;
    setParamList({...paramList})
  };
  const formatJson = (jsonString) => {
    try {
      const obj = JSON.parse(jsonString);
      const formatted = JSON.stringify(obj, null, 4);
      return formatted;
    } catch (e) {
      message.error("请输入规范的JSON数据")
      return jsonString; // 如果解析失败，返回原始字符串
    }
  }
  const handleFormat = () => {
        const formattedJsonRequest = formatJson(paramList.bodyRawParam);
        paramList.bodyRawParam = formattedJsonRequest;
        setParamList({...paramList})
   
  };
  // const [selectList, setSelectList] = useState({
  //   pathList:[],
  //   queryList:[],
  //   headersList:[],
  //   bodyFormDataList:[],
  //   bodyWwwList:[]
  // });

  return (
    <div className='debugger-api sub-app-box-container'>
      <BreadcrumbNav list={linkList} isNeedBack/>
      <div style={{width:'100%',height:'100%',display:'flex',background:'transparent',margin:'8px'}} className='sub-app-box-container-content'>
          <Sider width="55%" style={{background:'transparent',marginRight:'8px'}}>
          <div className='Sider-top'>
          
            <div >
              <Form
                form={form}
                layout="horizontal"
                initialValues={{
                  method: 'GET',
                  url: '',
                  bodyType: 'raw',
                  pathParams: [{ key: '', value: '' }],
                  queryParams: [{ key: '', value: '' }],
                  headers: [{ key: '', value: '' }],
                  bodyParams: [{ key: '', value: '' }],
                  rawBody: '{\n  \n}'
                }}
              >
              <Row>
              <Col span={20}>
                <Form.Item name="apiName" label="在线调试">
                  <Select onChange={handleChange}>
                    {initData.map(method => (
                      <Option key={`${method.name} ${method.path}`} value={method.path}>{`${method.name} ${method.path}`}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
             
            </Row>
                <Row>
                  
                    <Col span={3}>
                      <Form.Item name="method" label="">
                        <Select onChange={(value)=>{setMethod(value)}}>
                          {HTTP_METHODS.map(method => (
                            <Option key={method} value={method}>{method}</Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={17}>
                      <Form.Item 
                      name="url" 
                      label=""
                      rules={[{ required: true, message: '请输入 API URL' }]}
                      style={{width:'100%'}} 
                    >
                      <Select onChange={handleChangeApi} value={url}>
                        {fullPathList.map(list => (
                          <Option key={list} value={list}>{list}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                    </Col>
                
                  <Col span={4}>
                      <Form.Item>
                      <Button 
                        type="primary" 
                        
                        onClick={handleSubmit}
                        loading={loading}
                        style={{marginLeft:'10px',backgroundColor:'#eff7ff',color:'#1a9fe7'}}
                      >
                        发送
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
  
          </div>
          <div className='Sider-bottom'>
            <div>
                <div className='flex-app'>
                    <div className='header-icon'></div>
                    <div>请求鉴权</div>
                </div>
                <div>
                
                  <div className='flex-app'>
                    <span className='debugger-title'>TSL-ClientID</span>
                    <span style={{backgroundColor:'#eff7ff'}} className='debugger-text'>{oPerateData?.clientId}</span>
                  </div>
                  <div className='flex-app'>
                    <span className='debugger-title'>TSL-ClientSecret</span>
                    <span style={{backgroundColor:'#fef1f0'}} className='debugger-text'>{oPerateData?.clientSecret}</span>
                  </div>
                </div>
            </div>
            <div style={{marginTop:'25px'}}>
              <div className='flex-app'>
                  <div className='header-icon'></div>
                  <div>参数配置</div>
              </div>
              <div>
                <Tabs activeKey={activeTab} onChange={setActiveTab}>
                  <TabPane tab="Path" key="path">
                    <EditableTable setSelectList={setSelectList}  selectList={selectList} dataSource={paramList.path} handleDelete={handleDelete} setParamList={setParamList} dataSourceOrigin={paramList} activeTab={activeTab} />
                  </TabPane>
                  <TabPane tab="Query" key="query">
                    <EditableTable setSelectList={setSelectList} selectList={selectList} dataSource={paramList.query} handleDelete={handleDelete} setParamList={setParamList} dataSourceOrigin={paramList} activeTab={activeTab} />
                  </TabPane>
                  <TabPane tab="Headers" key="headers">
                    <EditableTable setSelectList={setSelectList} selectList={selectList} dataSource={paramList.headers} handleDelete={handleDelete} setParamList={setParamList} dataSourceOrigin={paramList} activeTab={activeTab} />
                  </TabPane>
                  <TabPane tab="Body" key="body" >
                  <div style={{marginBottom:'15px'}}>
                    <Radio.Group
                    name="radiogroup"
                   defaultValue={'bodyRawParam'}
                    onChange={(e)=>{setBodyType(e.target.value)}}
                    options={[
                      { value: "bodyFormData", label: 'form-data' },
                      { value: "bodyWww", label: 'x-www-form-urlencoded' },
                      { value: "bodyRawParam", label: 'raw' }
                    ]}
                    />
                    {
                      bodyType === 'bodyRawParam'?  <Button 
                      type="text" 
                      onClick={handleFormat}
                      loading={loading}
                      style={{marginLeft:'10px',backgroundColor:'#eff7ff',color:'#1a9fe7'}}
  
                    >
                      格式化
                    </Button>:''
                    }
                  
                  </div>
                 
                    <EditableTable setSelectList={setSelectList} bodyType={bodyType} dataSource={paramList.bodyWww} handleChange={handleChangeCode}
                    dataSourceOrigin={paramList} selectList={selectList} activeTab={activeTab} handleDelete={handleDelete} setParamList={setParamList}/>
                  </TabPane>
                </Tabs> 
              </div>
            </div>
                  
          </div>
        </Sider>
          <Content className='content-top'>
          {response ? (
            <div style={{ marginTop: 24 }}>
                <div className='flex-app'>
                    <div className='header-icon'></div>
                    <div>返回结果</div>
                </div>
              <div style={{ marginBottom: 16,marginTop:'20px' }}>
                <span strong>状态码：</span>
                <span code style={{ paddingRight:'40px' }}>{response?.status}</span>
              
                <span strong>响应耗时：</span>
                <span code>{response?.time}ms</span>
              </div>
              <Divider style={{margin:'5px 0px'}} />

              <Tabs defaultActiveKey="response">
                <TabPane tab="响应内容" key="response">
                  <pre style={{ 
                    background: '#f6f8fa', 
                    padding: 16, 
                    borderRadius: 4,
                    maxHeight: '400px',
                    overflow: 'auto'
                  }}>
                    {response.data}
                  </pre>
                </TabPane>
                <TabPane tab="响应头" key="headers">
                  <Table
                    columns={[
                      { title: 'Header 名', dataIndex: 'key', width: '30%' },
                      { title: 'Header 值', dataIndex: 'value' }
                    ]}
                   
                    dataSource={response?.headers?Object?.entries(response?.headers).map(([key, value]) => ({
                      key,
                      value: Array.isArray(value) ? value.join(', ') : value
                    })):[]}
                    size="small"
                    pagination={false}
                  />
                </TabPane>
                <TabPane tab="实际请求" key="request">
                  <pre style={{ 
                    background: '#f6f8fa', 
                    padding: 16, 
                    borderRadius: 4,
                    maxHeight: '400px',
                    overflow: 'auto'
                  }}>
                    {
                      JSON.stringify({
                      method: response?.method,
                      url: response?.url,
                      headers: response?.requestHeaders,
                      requestBody: response?.requestBody
                    }, null, 2)
                  
                  }
                  </pre>
                </TabPane>
              </Tabs>
            </div>
          ):<div style={{textAlign:'center'}}>
            <img src={noResponseImage} style={{ height: "240px",marginTop:'80px' }} />  
          </div>
        }
        </Content>
      </div>
     
    </div>
  );
};

export default ApiDebugger;