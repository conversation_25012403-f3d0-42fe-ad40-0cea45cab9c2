import React, { useState } from 'react';
import './HtmlFormatter.css';

const HtmlFormatter = ({ htmlString }) => {
  const [isFormatted, setIsFormatted] = useState(true);
  const [copied, setCopied] = useState(false);

  // 格式化HTML函数
  const formatHtml = (html) => {
    // 简单实现：添加缩进和换行
    let formatted = '';
    let indent = '';
    
    html.split(/>\s*</).forEach(element => {
      if (element.match(/^\/\w/)) {
        indent = indent.substring(2);
      }
      
      formatted += indent + '<' + element + '>\n';
      
      if (element.match(/^<?\w[^>]*[^\/]$/) && !element.startsWith('!')) {
        indent += '  ';
      }
    });
    
    return formatted.substring(1, formatted.length - 1);
  };

  // 高亮HTML代码
  const highlightHtml = (html) => {
    return html
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/(".*?")/g, '<span class="attr-value">$1</span>')
      .replace(/(\w+)=/g, '<span class="attr-name">$1</span>=')
      .replace(/&lt;\/?(\w+)/g, '<span class="tag">&lt;$1</span>')
      .replace(/&lt;!/g, '<span class="doctype">&lt;!</span>');
  };

  const formattedHtml = isFormatted ? formatHtml(htmlString) : htmlString;
  const highlightedHtml = highlightHtml(formattedHtml);

  const copyToClipboard = () => {
    navigator.clipboard.writeText(htmlString).then(() => {
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    });
  };

//   const toggleView = () => {
//     setIsFormatted(!isFormatted);
//   };

  return (
    <div className="html-formatter">
      <div className="code-container">
        <pre>
          <code 
            dangerouslySetInnerHTML={{ __html: highlightedHtml }} 
          />
        </pre>
      </div>
    </div>
  );
};

export default HtmlFormatter;