import React, { useState,useEffect } from 'react';
import { 
  But<PERSON>, 
  Row, 
  Col, 
  Tabs, 
  message, 
  Layout,
  Collapse 
} from 'antd';
import './ApiDebugger.less';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import { getSubscribeLogsDetail } from '@/services';
import { useParams} from 'umi';
import dayjs from 'dayjs';
import { DownloadOutlined  } from '@ant-design/icons';
import axios from 'axios';
import { BaseUrlEnum } from '@/enums/httpEnum'
const { TabPane } = Tabs;
const { Sider } = Layout;
const linkList = [
	{
		title: 'API管理'
	},
	{
		href:"/#/apiManage/subscripLog",   
		title: `订阅日志查询`
	},
	{
		title: `订阅日志详情`
	}
]
const SubscripLogDetail = () => {
const location = useParams();
const { eid } = location || {};
const eventId = eid;
  const [activeTab1, setActiveTab1] = useState('requestBody');
  const [activeTab2, setActiveTab2] = useState('queryString');
  const [activeTab3, setActiveTab3] = useState('responseBody');
  const [activeTab4, setActiveTab4] = useState('requestHeaders');
  
  const [detainInfo,setDetailInfo] = useState({});
  const [active3,setActive3] = useState();
  const [active2,setActive2] = useState();
  const [active1,setActive1] = useState();
  const [active4,setActive4] = useState();
  const [sizeData,setSizeData] = useState(0)
  function getStringSizeInMB(str) {
    if(!str){
      return;
    }
    const blob = new Blob([str]);
    return blob.size / (1024 * 1024);
  }
  const getLogDetail =  () => {
    getSubscribeLogsDetail({eventId:eventId}).then((res) => {
      if(res){
        let detailInfo = res.event || {};
        try {
            const jsonObject = JSON.parse(detailInfo?.responseBody);
            const formattedJson = JSON.stringify(jsonObject, null, 4);
            detailInfo.responseBody = formattedJson;
        } catch (error) {
            
        }
        try {
            const jsonObjectRequest = JSON.parse(detailInfo?.requestBody);
            const formattedJsonRequest = JSON.stringify(jsonObjectRequest, null, 4);
            detailInfo.requestBody = formattedJsonRequest;
        } catch (error) {
           
        }
        if(detailInfo.responseBody){
            setActive3('1')
        }
        const sizeData = getStringSizeInMB(detailInfo.responseBody);
        setSizeData(sizeData)
        if(detailInfo.queryString){
            setActive2('1')
        }
        if(detailInfo.requestBody){
            setActive1('1')
        }
        if(detailInfo.requestHttpHeaders){
            setActive4('1')
        }
       
        setDetailInfo(detailInfo)
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  useEffect(() => {
    getLogDetail()
}, []);
const downloadResponse = async () => {
    const a=detainInfo.responseBody
    try {
      const response = await axios.post(`${BaseUrlEnum.KEPLERAPI}/app-market/download`,{a}, {
        responseType: 'blob', 
      });
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '');
      document.body.appendChild(link);
      link.click();
      // 清理
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download failed:', error);
      // 错误处理
    }
  };

  return (
    <div className='sub-app-box-container'>
      <BreadcrumbNav list={linkList} isNeedBack/>
      <div className='sub-app-box-container-content debugger-api'>
          <Sider width="100%" style={{background:'transparent',marginRight:'8px'}}>
          <div className='Sider-bottom' style={{height:'100%'}}>
            <div>
                <div className='flex-app' style={{marginBottom:'25px'}}>
                    <div className='header-icon'></div>
                    <div>日志详情</div>
                </div>
                <div>
                    <Row>
                        <Col span={24}>
                            <div className='flex-app'>
                                <span className='debugger-title'>请求地址</span>
                                <span className='debugger-text'>{detainInfo.requestMethod}: {detainInfo.showGwUrl}{detainInfo.uriPath}</span>
                            </div>
                        </Col>
                    </Row>
                    <Row gutter={16}>
                        <Col className="gutter-row" span={8}>
                            <div className='flex-app'>
                                <span className='debugger-title'>状态码</span>
                                <span className='debugger-text'>{detainInfo.statusCode}</span>
                            </div>
                        </Col>
                    <Col className="gutter-row" span={8}>
                        <div className='flex-app'>
                            <span className='debugger-title'>请求时间</span>
                            <span  className='debugger-text'>{detainInfo.datetime ? dayjs(new Date(detainInfo.datetime)).format('YYYY-MM-DD HH:mm:ss') : ''}</span>
                        </div>
                    </Col>
                    <Col className="gutter-row" span={8}>
                        <div className='flex-app'>
                            <span className='debugger-title'>产品名称</span>
                            <span className='debugger-text'>{detainInfo.productTitle}</span>
                        </div>
                    </Col>
                  </Row>
                  <Row gutter={16}>
                        <Col className="gutter-row" span={8}>
                            <div className='flex-app'>
                                <span className='debugger-title'>产品版本</span>
                                <span  className='debugger-text'>{detainInfo.productVersion}</span>
                            </div>
                        </Col>
                    <Col className="gutter-row" span={8}>
                        <div className='flex-app'>
                            <span className='debugger-title'>响应时间（毫秒）</span>
                            <span  className='debugger-text'>{detainInfo.timeToServeRequest}</span>
                        </div>
                    </Col>
                    <Col className="gutter-row" span={8}>
                    <div className='flex-app'>
                        <span className='debugger-title'>产品编码</span>
                        <span  className='debugger-text'>{detainInfo.productName}</span>
                    </div>
                </Col>
                  </Row>
                  <Row gutter={16}>
                        <Col className="gutter-row" span={8}>
                            <div className='flex-app'>
                                <span className='debugger-title'>处理网关</span>
                                <span className='debugger-text'>{detainInfo.gatewayServiceName}</span>
                            </div>
                        </Col>
                    <Col className="gutter-row" span={8}>
                        <div className='flex-app'>
                            <span className='debugger-title'>处理网关IP</span>
                            <span  className='debugger-text'>{detainInfo.gatewayIp}</span>
                        </div>
                    </Col>
                  
                  </Row>
                </div>
            </div>
            <div style={{marginTop:'25px'}}>
            
                <Collapse 
                style={{margin:'20px 0px'}}
                expandIconPosition={'end'}
                items={[
                    {
                        key: '1',
                        label: '请求参数body',
                        children:    <Tabs activeKey={activeTab1} onChange={setActiveTab1}>
                        <TabPane tab="格式化" key="requestBody">
                            {
                                detainInfo.requestBody && <div className="file-content" >
                                <CodeMirror
                                value={detainInfo.requestBody}
                                max-height="200px"
                                minHeight='100px'
                                theme="light"
                                extensions={[javascript(), yaml()]}
                                editable={false}
                                readOnly={true}
                                basicSetup={{
                                    lineNumbers: true,
                                    foldGutter: true,
                                    highlightActiveLine: true,
                                    dropCursor: true,
                                    allowMultipleSelections: true,
                                    indentOnInput: true,
                                    
                                }}
                                style={{ overflow: 'auto' }}
                                />
                            </div>
                            }
                        </TabPane>
                        <TabPane tab="源" key="requestBodyOrigin">
                                {
                                    detainInfo.requestBody  
                                }
                        </TabPane>
                       
                    </Tabs> 
                      }
                ]} 
                defaultActiveKey={ detainInfo.requestBody?'1':''} 
                activeKey={active1}
                 />

                <Collapse 
                style={{margin:'20px 0px'}}
                activeKey={active2}
                expandIconPosition={'end'}
                items={[
                    {
                        key: '1',
                        label: '请求参数QueryString',
                        children:   <Tabs activeKey={activeTab2} onChange={setActiveTab2}>
                        <TabPane tab="格式化" key="queryString">
                            {
                                detainInfo.queryString && <div className="file-content" >
                                <CodeMirror
                                value={detainInfo.queryString}
                                max-height="200px"
                                minHeight='100px'
                                theme="light"
                                extensions={[javascript(), yaml()]}
                                editable={false}
                                readOnly={true}
                                basicSetup={{
                                    lineNumbers: true,
                                    foldGutter: true,
                                    highlightActiveLine: true,
                                    dropCursor: true,
                                    allowMultipleSelections: true,
                                    indentOnInput: true,
                                   
                                    
                                }}
                                style={{ overflow: 'auto' }}
                                />
                            </div>
                            }
                         
                        </TabPane>
                        <TabPane tab="源" key="queryStringOrigin">
                                {
                                    detainInfo.queryString  
                                }
                        </TabPane>
                       
                    </Tabs> 
                      }
                ]} 
                defaultActiveKey={ detainInfo.queryString?'1':''}   />
                <Collapse 
                activeKey={active4}
                expandIconPosition={'end'}
                   style={{margin:'20px 0px'}}
                   items={[
                       {
                           key: '1',
                           label: '请求参数Header',
                           children: 
                           <Tabs activeKey={activeTab4} onChange={setActiveTab4}>
                       <TabPane tab="格式化" key="requestHeaders">
                        {
                            detainInfo.requestHttpHeaders && <div className="file-content" >
                            <CodeMirror
                            value={JSON.stringify(detainInfo.requestHttpHeaders,null,4)}
                            max-height="200px"
                            minHeight='100px'
                            theme="light"
                            extensions={[javascript(), yaml()]}
                            editable={false}
                            readOnly={true}
                            basicSetup={{
                                lineNumbers: true,
                                foldGutter: true,
                                highlightActiveLine: true,
                                dropCursor: true,
                                allowMultipleSelections: true,
                                indentOnInput: true,
                            
                                
                            }}
                            style={{ overflow: 'auto' }}
                            />
                        </div>
                        }
                       </TabPane>
                       <TabPane tab="源" key="requestHeadersOrigin">
                        {
                            JSON.stringify(detainInfo.requestHttpHeaders)  
                        }
                       </TabPane>
                   
                   </Tabs> 
                         }
                   ]} 
                    />
             <Collapse 
             activeKey={active3}
             expandIconPosition={'end'}
                style={{margin:'20px 0px'}}
                items={[
                    {
                        key: '1',
                        label: '响应数据',
                        children: 
                        <Tabs activeKey={activeTab3} onChange={setActiveTab3}>
                    <TabPane tab="格式化" key="responseBody">
                    <p style={{position:'absolute',right:'0px',top:'-50px'}}>  
                        <Button 
                        type="primary" 
                        icon={<DownloadOutlined />}
                        onClick={downloadResponse}
                    
                        style={{marginLeft:'10px',backgroundColor:'#eff7ff',color:'#1a9fe7'}}
                        >
                        下载
                        </Button>
                    </p>
                    {
                        sizeData > 5? <p style={{color:'#1a9fe7',textAlign:'center'}}>响应体过大，请下载查看！</p>: 
                        detainInfo.responseBody && <div className="file-content" >
                            <CodeMirror
                            value={detainInfo.responseBody}
                            max-height="200px"
                            minHeight='100px'
                            theme="light"
                            lineSeparator="\\"
                            extensions={[javascript(), yaml()]}
                            editable={false}
                            readOnly={true}
                            basicSetup={{
                                lineNumbers: true,
                                foldGutter: true,
                                highlightActiveLine: true,
                                dropCursor: true,
                                allowMultipleSelections: true,
                                indentOnInput: true
                            
                                
                            }}
                            style={{ overflow: 'auto' }}
                            />
                        </div>
                      }
                  
                        
                    </TabPane>
                    <TabPane tab="源" key="responseBodyOrigin">
                        <p style={{position:'absolute',right:'0px',top:'-50px'}}>  
                            <Button 
                            type="primary" 
                            icon={<DownloadOutlined />}
                            onClick={downloadResponse}
                        
                            style={{marginLeft:'10px',backgroundColor:'#eff7ff',color:'#1a9fe7'}}
                            >
                            下载
                            </Button>
                        </p>
                        {
                            sizeData > 5? <p style={{color:'#1a9fe7',textAlign:'center'}}>响应体过大，请下载查看！</p>: 
                            detainInfo.responseBody  
                          }
                           
                    </TabPane>
                
                </Tabs> 
                      }
                ]} 
                 />
                
            </div>        
          </div>
        </Sider>
      </div>
     
    </div>
  );
};

export default SubscripLogDetail;