/* HtmlFormatter.css */
.html-formatter {
    border: 1px solid #e1e4e8;
    border-radius: 6px;
    margin: 20px 0;
    background-color: #f6f8fa;
  }
  
  .controls {
    display: flex;
    justify-content: flex-end;
    padding: 10px;
    background-color: #e1e4e8;
    border-bottom: 1px solid #d0d7de;
  }
  
  .toggle-btn, .copy-btn {
    margin-left: 10px;
    padding: 5px 10px;
    border: 1px solid #d0d7de;
    border-radius: 4px;
    background-color: #ffffff;
    cursor: pointer;
    font-size: 14px;
  }
  
  .toggle-btn:hover, .copy-btn:hover {
    background-color: #f3f4f6;
  }
  
  .code-container {
    padding: 16px;
    overflow: auto;
  }
  
  pre {
    margin: 0;
    font-family: 'SFMono-Regular', Consolas, 'Liberation Mono', Menlo, monospace;
    font-size: 14px;
    line-height: 1.45;
    color: #24292e;
  }
  
  code {
    white-space: pre-wrap;
  }
  
  .tag {
    color: #22863a;
  }
  
  .attr-name {
    color: #6f42c1;
  }
  
  .attr-value {
    color: #032f62;
  }
  
  .doctype {
    color: #6a737d;
  }