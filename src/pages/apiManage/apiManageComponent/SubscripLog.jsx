import React, { useEffect, useState } from 'react';
import { useParams, history,useSearchParams  } from 'umi';
import { LeftOutlined, LoadingOutlined, CheckCircleFilled, MinusCircleFilled, ExclamationCircleOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { yaml } from '@codemirror/lang-yaml';
import BreadcrumbNav from '@/components/BreadcrumbNav/index';
import './MySubscriptions.less';
import { getMiddlewareVersion, getMiddlewareMeta, getMiddlewareFileList, getMiddlewareFileContent, getMiddlewareOperatorStatuss, CONFIG_MIDDLEWARE, API_MIDDLEWARE } from '@/services';
import { MiddlewareType } from '@/utils/const';
import { Select , DatePicker ,Input,Button ,Table,message,Col, Row,Pagination } from 'antd';
import  apiLogoIMage from '@/assets/images/apiLogo.png';
import { getSubscribeLogs } from '@/services';
import SubscripLogDetail from './SubscripLogDetail';
import moment from "moment"
import dayjs from 'dayjs';
// 开始
const { RangePicker } = DatePicker;
const { Search } = Input;
const SubscripLog = () => {
  const location = useParams();
  const [searchParams] = useSearchParams();
  const env = searchParams.get('env') || ''; // 'uat'
  // 从路由中获取 type 和 chartName 参数
  const { id } = location || {};
  const apiId = id;
  const handleBack = () => {
    history.back();
  };

 
  ///////开始//////////////
  const linkList = [
    {
      title: 'API管理'
    },
    {
      title: `订阅日志查询`
    },
  ]
  

  const [data, setData] = useState([]);
  const [responseStatus, setResponseStatus] = useState('');
  const [time, setTime] = useState([]);
  const [path, setPath] = useState('');
  const [requestBody, setRequestBody] = useState('');
  const [responseBody, setResponseBody] = useState('');
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [total, setTotal] = useState(0);
  
  // const yesterdayStart = moment().subtract(1, 'days').startOf('day');
  // console.log(yesterdayStart)
  // const yesterdayEnd = moment().subtract(1, 'days').endOf('day');
  const today = new Date();
  const oneMonthAgo = new Date();
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);
  const formattedDate = oneMonthAgo.toISOString().split('T')[0];
  const formatteToday = today.toISOString().split('T')[0];
  const formattedDateTime = `${formattedDate} 00:00:00`;
  const formatteTodayTime = `${formatteToday} 23:59:59`;
  console.log(formattedDate); // 例如：2023-05-20
  console.log(formatteToday)
  const [timeRange, setTimeRange] = useState( [formattedDateTime,formatteTodayTime]);
  
  const onPageChange = (page,pageSize) => {
    setCurrentPage(page);
    setPageSize(pageSize);
  };
  const convertToISOFormat = (dateString)=> {
    // 将输入字符串转换为Date对象
    const date = new Date(dateString);
    date.setHours(date.getHours() + 8);
    // 转换为ISO字符串
    return date.toISOString();
  }
  const getLogs =  (isSearch,param) => {
   
    if(isSearch){
        setCurrentPage(1)
    }
    let dataParam = {}
    if(param){
        dataParam = param;
    }else{
        let list = {
            pageNo:isSearch ? 1 : currentPage,
            pageSize:pageSize,
            path:path,
            requestBody:requestBody,
            responseBody:responseBody,
            responseStatus:responseStatus,
            startTime:timeRange?.[0] ? timeRange?.[0]: '',
            endTime:timeRange?.[1] ? timeRange?.[1]: ''
        }
        if(list.startTime){
          list.startTime = convertToISOFormat(list.startTime)
        }
        if(list.endTime){
          list.endTime = convertToISOFormat(list.endTime)
        }
        // const timeParam = {
        //   "createTime[0]":time?.[0] ? time?.[0] + ' 00:00:00' : '',
        //   "createTime[1]":time?.[1] ? time?.[1] + ' 23:59:59' : ''
        // };
        // const encodedTimeParam = {};
        // for (const key in timeParam) {
        //     const encodedKey = encodeURI(key);
        //     encodedTimeParam[encodedKey] = timeParam[key];
        // }
        dataParam = {...list}
    }
   

        console.log(dataParam);
    
   
    getSubscribeLogs(dataParam).then((res) => {
      if(res){
        setData(res.list || [])
        setTotal(res.total)
      }else{
        message.error(res?.message || res?.msg)
      }
    }).catch((data) => {
      message.error(data?.msg)
    })
  }
  useEffect(() => {
    getLogs()
}, [pageSize,currentPage]);
  

  
  
  
  
  


  const columns = [
    {
        title: '请求路径',
        dataIndex: 'uriPath',
        key: 'uriPath',
        className:'text-overflow-box',
        render: (text,record) => <div title={text} className='text-overflow'><a onClick={()=>{goDetail(record)}}>{text}</a></div>,
      },
    {
      title: '产品名称',
      dataIndex: 'productTitle',
      key: 'productTitle',
      render: (text) => <div>{text}</div>,
    },
    {
      title: '请求时间',
      dataIndex: 'datetime',
      key: 'datetime',
    },
    {
      title: '状态码',
      dataIndex: 'statusCode',
      key: 'statusCode',
    },
    {
      title: '请求码',
      key: 'requestMethod',
      dataIndex: 'requestMethod'
    },
    {
        title: '耗时（毫秒）',
        key: 'timeToServeRequest',
        dataIndex: 'timeToServeRequest'
      },
    {
      title: '操作',
      key: '操作',
      render: (api, record) => (
        <span size="middle" onClick={()=>{goDetail(record)}}>
          <a>查看</a>
        </span>
      ),
    },
  ];
  const goDetail = (record) => {
    history.push(`/apiManage/subscripLogDetail/${record.eventId}?env=${env}`)
  };
  const onChange = (dates, dateStrings) => {
    const start = dates[0].format('YYYY-MM-DD HH:mm:ss');
      const end = dates[1].format('YYYY-MM-DD HH:mm:ss');
    setTimeRange([start, end])
  
  };
 
  const formatDates = (dates) => {
    if (dates && dates.length === 2) {
      return dates.map(date => date.toISOString());
    }
    return [];
  };
  const resetSearch = () => {
    setCurrentPage(1)
    setTimeRange([formattedDateTime,formatteTodayTime])
    setTime(null);
    setPath(null);
    setRequestBody('')
    setResponseStatus('')
    setResponseBody('')
    getLogs(false,{
        pageNo:1,
        pageSize:pageSize,
        path:'',
        requestBody:'',
        responseBody:'',
        responseStatus:''
    })
    
  }

 

  return (
    <div className="mySubscriptions sub-app-box-container" >
      {/* 头部导航 */}
      <BreadcrumbNav list={linkList} />
      <div className='sub-app-box-container-content'>
        {/* 中间件标题区域 */}
        <div className="middleware-header">
          <div className="middleware-base-info">
          <Row>
            <Col span={8}>
                <div className='mySubscriptions-search'>
                    <span>接口编号/路径：</span>
                    <Input placeholder="请输入接口编号/路径" style={{width:'200px'}} value={path} onChange={(e)=>{setPath(e.target.value)}} />
                </div>
            </Col>
          
            <Col span={8}>
                <div className='mySubscriptions-search'>
                    <span>请求参数：</span>
                    <Input placeholder="请输入请求参数" style={{width:'200px'}} value={requestBody} onChange={(e)=>{setRequestBody(e.target.value)}} />
                </div>
            </Col>
            <Col span={8}>
              <div className='mySubscriptions-search'>
                  <span>响应body：</span>
                  <Input placeholder="请输入响应body" style={{width:'200px'}} value={responseBody} onChange={(e)=>{setResponseBody(e.target.value)}} />
              </div>
            </Col>
        </Row>
        <Row>
        <Col span={8}>
            <div className='mySubscriptions-search'>
                <span>响应状态码：</span>
                <Select
              
                style={{ width: 200 }}
                onChange={(value)=>{setResponseStatus(value)}}
                value={responseStatus}
                options={[
                  {
                    value: 'success' ,
                    label: '仅成功（20X）',
                  },
                  {
                    value: 'error',
                    label: '仅错误（40X  50X）',
                  }
                 
                ]}
                placeholder="请选择状态"
              />
            </div>
        </Col>
        <Col span={8}>
            <div className='mySubscriptions-search'>
                <span>请求时间：</span>
                
                    <RangePicker
                    defaultValue={[dayjs(timeRange[0], 'YYYY/MM/DD HH:mm:ss'), dayjs(timeRange[1], 'YYYY/MM/DD HH:mm:ss')]}
                    format={'YYYY-MM-DD HH:mm:ss'}
                    onChange={onChange}
                    showTime 
                  />
            </div>
        </Col>
        <Col span={8}>
            <div >
                <Button style={{marginRight:'5px',marginLeft:'5px'}} onClick={()=>{getLogs(true)}}>搜索</Button>
                <Button onClick={()=>{resetSearch()}}>重置</Button>
            </div>   
        </Col>
    </Row>
        
            

          </div>
        </div>
        {/* 基本信息区域 */}
        <div className="info-section">
          <div>
            <Table columns={columns} dataSource={data}  
              pagination={{
                  current:currentPage,
                  showTotal:()=>{return `共 ${total} 项数据`},
                  showSizeChanger:true,
                  onChange:(page,pageSize)=>{onPageChange(page,pageSize)},
                  total:total,
                  pageSize:pageSize
              }}
          
              />
           
          </div>
        </div>
      </div>
     
     
    </div>
  );
};



const SubscripLogC = () => {
   
    return (
      <>
        <SubscripLog />
      </>
    );
  };
  
  export default SubscripLogC;