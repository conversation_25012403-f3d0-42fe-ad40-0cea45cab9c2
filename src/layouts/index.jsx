import { useLocation, useSearchParams, useModel, history } from "umi";
import { useEffect, useState } from "react";
import { Spin } from "antd";
import BackLayout from '@/pages/layout/backLayout'
import { getUserInfo, getUserDetailInfo, getUserAuthMap } from "@/services";
import { getUrlwithoutToken, getQueryString } from "@/utils/url";
import { localStorageEnum } from "@/enums/storageEnum";
import { pathUrlMatch, getTopMenuPathMap, getAsyncTopMenus, collectChildPaths } from "@/utils/menus";
import { homeLayoutPagePathStatic } from "@/constants";
import { menuLayoutEnum } from "@/enums/menuEnum"
import _ from 'lodash'

export default function Layout() {
  const [ isHome, setIsHome ] = useState(true)
  const [ staticMenus, setStaticMenus ] = useState([])
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const {userInfo, setUserInfoStore, clearUserInfoStore, setTokenStore, setUserDetailInfo, setUserMapStore } = useModel("user");
  const { layoutConfig, setIsShowAdminEntry } = useModel("sysConfig");
  const { totalMenus, setTopMenus, setTopMenuPathMap, setSidebarMenus, getAsyncMenus, getStaticMenus, setCurrentPath } = useModel('menu')
  const { initialState } = useModel("@@initialState");

  const TOKEN_ENUM = localStorageEnum.TOKEN

  
  const handleLogin = async () => {
    try {
      const res = await getUserInfo();
      if(res){
        const currTenantInfo = res.tenants?.find(item => item.id === res.tenantId)
        if(currTenantInfo){
          res.tenantCode = currTenantInfo?.code
          res.tenantName = currTenantInfo?.name
        }
        clearUserInfoStore()
        setUserInfoStore(res)
      }
    } catch (error) {
      console.log(error);
    }
  }

  useEffect(() => {
    // 如链接上携带token  先存到本地
    let searchToken = ""
    if(window.location.href.indexOf("/#/") > -1){// hash
      searchToken = searchParams.get(TOKEN_ENUM);
    }else{
      searchToken = getQueryString(TOKEN_ENUM)
    }
    if(searchToken){
      setTokenStore(searchToken)
      const url = getUrlwithoutToken();
      window.history.replaceState(null, null, url);
    }
    handleLogin().then(() => {
      // 获取用户详细数据
      getUserDetailInfo().then((data) => {
        if(data){
          setUserDetailInfo(data)
        }
      })
      getUserAuthMap().then((data) => {
        if (data) {
          setUserMapStore(data)
        }
      }) 
      // 获取动态菜单
      getAsyncMenus()
    })
  }, []);

  // 获取静态菜单
  useEffect(() => {
    if(initialState?.appInfo?.mainAppId){
      const menusData = getStaticMenus(initialState?.appInfo.mainAppId);
      setStaticMenus(menusData)
    }
  },[initialState?.appInfo])

  // 计算顶部菜单和侧边栏菜单数据
  useEffect(() => {
    // 存下当前访问路径，保证子组件和父组件同时监听location.pathName的location.pathName一致
    setCurrentPath(location.pathname+location.search)
    // 组装门户首页所需要的所有path，即homeLayoutPagePath集合
    const tempPaths = collectChildPaths([...staticMenus, ...totalMenus], homeLayoutPagePathStatic)
    const homeLayoutPagePath = [...homeLayoutPagePathStatic, ...tempPaths]
    let isHomeFlag = true
    if(location.pathname === '/' || !location.pathname || pathUrlMatch(location.pathname, homeLayoutPagePath)){// 判断是否是门户首页
      isHomeFlag = true
    }else{
      isHomeFlag = false
    }
    setIsHome(isHomeFlag)
    // 设置所有菜单路径跟顶部菜单的id的对应关系map
    // 如果是门户首页合并静态菜单和门户首页渲染的动态菜单，不是门户首页，剔除门户首页需要渲染的动态菜单
    const backEndMenus = getAsyncTopMenus(totalMenus)
    // setIsShowAdminEntry(backEndMenus?.length > 0 && userInfo?.admin)// 没有后台管理菜单就不展示后台管理菜单入口
    setIsShowAdminEntry(backEndMenus?.length > 0)// 没有后台管理菜单就不展示后台管理菜单入口
    let staticAndAsyncMenus = isHomeFlag ? [...staticMenus, ...getAsyncTopMenus(totalMenus, isHomeFlag)] : getAsyncTopMenus(totalMenus, isHomeFlag)
    staticAndAsyncMenus =  _.cloneDeep(staticAndAsyncMenus)
    setTopMenuPathMap(getTopMenuPathMap(staticAndAsyncMenus))
    if(layoutConfig?.menuLayout === menuLayoutEnum.mix){// 混合布局
      // 侧栏菜单在topMenu模块设置
      setTopMenus(staticAndAsyncMenus)
    }else if(layoutConfig?.menuLayout === menuLayoutEnum.header){// 顶栏布局
      setTopMenus(staticAndAsyncMenus)
      setSidebarMenus([])
    }else {// 侧栏布局
      setTopMenus([])
      setSidebarMenus(staticAndAsyncMenus)
    }
  },[ totalMenus, staticMenus, layoutConfig.menuLayout, location ])

  return (
      <>
      { userInfo ? <BackLayout isHome={isHome} /> : 
        <div style={{width:"100vw",height:"100vh",display:"flex", flexDirection:"column", justifyContent:"center",alignItems:"center"}}>
          <Spin size="large"></Spin>
          <span style={{fontSize:"14px", marginTop:"16px", color:"#008BD6"}}>检查登录状态中...</span>
        </div> 
      }
      </>
  )
}
