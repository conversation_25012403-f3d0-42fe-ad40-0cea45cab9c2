import enUS from 'antd/es/locale/en_US';

// 业务页面词条
enUS.HC = {
	locale: 'en-us',
	common: {
		org: 'Organization',
		project: 'Project',
		cluster: 'Cluster',
		cloudService: 'Cloud service',
		subsystem: 'Subsystem',
		or: 'or',
		day: ' day',
		space: ' space'
	},
	notice: {
		warn: 'Warning',
		err: 'Error',
		success: 'Success',
		no_access_permission: 'You currently do not have access permissions',
		connect_fail: 'Connection to server failed!',
		no_connect:
			'There is no network connection, please check and try again!',
		url_not_empty: 'url cannot be empty',
		http400: 'Bad request(400)',
		http401: 'Unauthorized, please log in again(401)',
		http403: 'No access(403)',
		http404: 'Request error(404)',
		http408: 'Request timeout(408)',
		http417: 'Login user switched',
		http501: 'Service not implemented(501)',
		http502: 'Network error(502)',
		http503: 'The service is unavailable(503)',
		http504: 'Network Timeout(504)',
		http505: 'HTTP version not supporte(505)'
	},
	error: {
		noOrg: "Please contact the administrator to add tenant information in the system settings",
		noApp: "Please ask the tenant administrator to assign application permissions",
		noAuth: "No permission",
		noAccess: "Access Denied",
		noAccessReason: "Access is restricted. Please contact the tenant administrator for processing. The reasons for the restriction include:",
		noAccessDesc1: "The current accessed tenant has been deactivated;",
		noAccessDesc2: "The current accessing IP is not within the IP whitelist of the current accessing tenant;",
		noIframeUrl: "Check the current menu configuration",
		noResource: "Please ask the tenant administrator to assign resource permissions",
		noMenu: "The menu is empty",
		noMenuDescripe: "The current application return menu is empty, please contact the tenant administrator to assign an application menu",
		goBack: "Go Back"
	},
	header: {
		modify_password: 'Update Password',
		switch_language: 'Switch Language',
		logout: 'Logout',
		terminal: 'Terminal',
		workbench: 'Workbench',
		product_service: 'Product Service',
		suffixManagementBackground: " Management Background",
		productConfig: "Product Configuration",
		messageCenter: "Message Center",
		costCenter: "Cost Center",
		managementBackground: "Management Background"
	},
	user: {
		new_pwd: 'New Password',
		input_new_pwd: 'Please input new password',
		pwd_format_error: 'The password format is error!',
		pwd_twice_diff: 'First password and second password are different!',
		pwd_modify_success: 'Password update success',
		old_pwd: 'Original Password',
		input_old_pwd: 'Please input original password',
		pwd_format_one: 'English Letter',
		pwd_format_two: 'Number',
		pwd_format_length: 'The length needs to be between 8-16',
		pwd_format_all:
			'Requirement: The password must meet the above four conditions',
		pwd_length: 'The current length is ',
		new_pwd_confirm: 'Confirm Password',
		input_user_password: 'Please input account and password',
		pwd_expired: 'Password has expired',
		contact_admin:
			'Please contact the administrator to change or reset the password',
		input_user: 'Please input account',
		input_pwd: 'Please input password',
		modify_now: 'Update now',
		modify_next_time: 'Update next time',
		modify_tip_title: 'The reminder for update password',
		modify_tip:
			'Reminder: To ensure the security of your account, please regularly change the password',
		modify_tip_1: 'Your account password has been used ',
		modify_tip_2: 'can used',
		modify_tip_3:
			'Failure to change the password upon expiration will prevent normal login. Do you want to change the password now？',
		pwd: 'Password',
		account: 'Account',
		login: 'Login'
	},
	spaceSwitcher: {
		associated: 'Associated',
		switch: 'switch',
		input: 'Please input ',
		searchWithName: ' name to search',
		hostArc: 'Host Architecture',
		supportNetwork: 'Support Network',
		online: 'online',
		offline: 'offline',
		state: 'Status',
		nodeCount: 'Node Count',
		k8sVersion: 'K8s Version',
		ipv6Tip: 'This project has IPv6 network resources'
	},
	message: {
	  notice: 'Notices',
	  latestMessage: 'Latest Messages',
	  subscription: 'Subscription Management',
	  viewMore: 'More Messages',
	  noMessage: 'No unread message'
	}
};

export default enUS;
