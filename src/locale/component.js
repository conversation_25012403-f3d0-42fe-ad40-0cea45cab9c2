import Local from '@/utils/storage';
import zhCN from './zh_CN';
import zhHK from './zh_HK';
import enUS from './en_US';

const languageCodeMap = {
	'zh-CN': zhCN,
	'zh-HK': zhHK,
	'en-US': enUS
};

const languageCode = Local.getLocal('language') || 'zh-CN';

const locale = languageCodeMap[languageCode];
const componentLocale = { ...locale };
delete componentLocale.HC;
export default componentLocale;
