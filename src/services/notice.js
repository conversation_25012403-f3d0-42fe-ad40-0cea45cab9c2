import request from "@/utils/axiosInstance";
import { API_AMP } from '@/services';
import { BaseUrlEnum } from "@/enums/httpEnum";

// 获取用户未读消息
export async function fetchNotices(params) {
  const { userId, ...rest } = params;
  return request.get(`${API_AMP}/messages/pull/${userId}`, { params: rest });
}

// 获取用户未读消息数量
export async function fetchNoticeList(params) {
  const { userId, ...rest } = params;
  return request.get(`${API_AMP}/inbox/${userId}`, { params: rest });
}

// 修改消息状态
export async function putStatus(id, params) {
  return request.post(`${API_AMP}/inbox/put/${id}`, {
    params,
    headers: { 'Amp-Organ-Id': params.tenantId },
  });
}

export async function queryAllNoticeList(params) {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/notice`, { params });
}

// 全部设置为已读
export async function setAllNoticeRead(params) {
  return request.put(`${BaseUrlEnum.KEPLER}/nuz/notice/inner/change/status`, { params });
}

// 新增通知
export async function addSystemNotice(params) {
  return request.post(`${BaseUrlEnum.KEPLER}/nuz/notice/inner/save/notice`, { params });
}

// 管理员查询所有系统通知
export async function getManageNoticeList(params) {
  return request.get(`${BaseUrlEnum.KEPLER}/nuz/notice/inner/page/all`, { params });
}

// 当前用户查询当前通知
export async function getCurrentUserNotice(params) {
  return request.get(`${BaseUrlEnum.KEPLER}/nuz/notice/inner/current/page/user`, { params });
}

export const getAllUserList = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/users`,{params})
};
