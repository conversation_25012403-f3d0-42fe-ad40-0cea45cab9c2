import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'


// 获取分类
export const getCategories = () => {
  
    return request.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/categories`)
  };
// 获取基线列表
export const getBaselineList = (params) => {
   
    return request.post(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/list`,{params});
  
  };
  // 创建基线
export const createBaseline= (params) => {
    return request.post(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline`,{params});
  };
    // 更新基线
export const updateBaseline= (params) => {
    return request.put(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline`,{params});
  };
  //删除基线
  export const delBaseline= (params) => {
    return request.delete(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/${params.id}`);
  };
// 获取开源组件
export const getOpensourceList = (params) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/list`,{params});

};
 // 根据组件查询
export const getComponentDimension = (name) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/component-dimension?name=${name}`);

};
// 获取应用系统
export const getAppList = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/aims/apps`,{params});

};
 // 根据应用系统查看
 export const getAppDimension = (id) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/opensource/baseline/use/app-dimension?projectId=${id}`);

};

 