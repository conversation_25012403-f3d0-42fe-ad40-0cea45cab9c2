import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'

// 获取订阅日志
export const getSubscribeLogs = (params) => {
//   return Promise.resolve({
//     list:[
//       {uriPath:'111',clientId:'123',clientSecret:'456'}
//     ]
//   })
    return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/subscribe/logs`,{params})
  };
// 获取日志详情
export const getSubscribeLogsDetail = (params) => {
//   return Promise.resolve({
//    event:  {
//         "apiId": "f0b453a6-75de-441a-a113-26501261f60a",
//         "apiName": "bpm-cm",
//         "apiVersion": "1.0",
//         "datetime": "2025-07-16T07:08:33.556Z",
//         "developerOrgId": "4b88b6d5-9e81-4915-9b61-8581cf906355",
//         "developerOrgName": "sandbox-test-org",
//         "developerOrgTitle": "Sandbox Test Organization",
//         "productId": "4a680173-dbbd-4bfe-a858-6bb3c5b13abc",
//         "productName": "6650282f52a437d395a0e58b",
//         "productTitle": "BPM-合同管理",
//         "productVersion": "1.0-6650292252a437d395a0e58d",
//         "queryString": "dateFrom=2025-07-01&dateTo=2025-07-30",
//         "requestBody": "",
//         "responseBody": "",
//         "requestMethod": "GET",
//         "statusCode": "500 Internal Server Error",
//         "timeToServeRequest": "1094",
//         "uriPath": "/63002140210b96af78f374b8/sandbox/ma0197/v1/apis/findMUContracts",
//         "showGwUrl": "https://apigw-stg.trinasolar.com",
//         "showGwName": "se-outer-gw",
//         "gatewayIp": "***********",
//         "gatewayServiceName": "se-inner-gw"
//     }
   
//   })
  return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/subscribe/logs/detail`,{params})
};
// 获取订阅列表
export const getOrderPage = (params) => {
    // return Promise.resolve({
    //   list:[
    //     {productName:'111',clientId:'123',clientSecret:'456'}
    //   ]
    // })
  return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/subscribe/order-page`,{params})
};
// api请求发送
export const getApiSend = (params) => {
  return request.post(`${BaseUrlEnum.KEPLERAPI}/app-market/api/send`,{params})
};
 //api市场详情
//  export const getApiDetailInfoData = (params) => {
//   return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/api/detail`,{params});
// };
export const getApiDetailInfoData = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/api/detail`,{params});
};
export const getDownload = (params) => {
  return request.post(`${BaseUrlEnum.KEPLERAPI}/app-market/download`,{params});
};




  

