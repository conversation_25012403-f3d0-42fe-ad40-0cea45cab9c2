import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'


// 获取应用系统列表
export const getAppSystemListApi = () => {
  return request.get(`${BaseUrlEnum.KEPLER}/aims/apps/simple/list`);
};

// 获取应用程序列表
export const getAppProgramListApi = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/program/base/list`,{params});
};

// 根据用用程序id获取应用程序详情
export const getAppProgramDetailApi = (id) => {
  return request.get(`${BaseUrlEnum.KEPLER}/program/base/detail/${id}`);
};

// 获取应用程序总数
export const getAppProgramCountApi = () => {
  return request.get(`${BaseUrlEnum.KEPLER}/program/base/app/count`);
};

// 获取应用系统总数
export const getAppSystemCountApi = () => {
  return request.get(`${BaseUrlEnum.KEPLER}/aims/apps/statistic/num`);
};
// 获取组件个数
export const getComponent = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/product/component`);
};

// 获取应用系统统计
export const getHomepageCount = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/homepage/count`)
};