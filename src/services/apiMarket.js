import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'


// 通过当前应用链接获取应用信息
export const getApiTags = (params) => {
    return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/api/tags`,{params})
  };
  // api市场列表
  export const getApiList = (params) => {
    return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/api/page`,{params});
  };
  
// 订阅组列表
export const getApiOrg = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/api/org`,{params});
};
// 订阅
export const subscribe = (params) => {
  return request.post(`${BaseUrlEnum.KEPLERAPI}/app-market/api/subscribe`,{params});
};
 // api市场详情
 export const getApiDetail = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/app-market/api/detail`,{params});
};

  


// /**
//  * 获取应用自定义头部
//  * @param params 应用id
//  */
// export function getHeaderBar(params) {
//   return request.get(`${BaseUrlEnum.KEPLERAPI}/aims/apps/headBar/${params}`);
// }


// /**
//  * 切换租户
//  */
// export const changeTenant = params => {
//   return request.post(`${BaseUrlEnum.KEPLER}/upms/u/users/change/tenant/${params}`,{});
// };


// /**
//  * 获取租户tree
//  */
// export function getTenantTree(params) {
//   return request.get(`${BaseUrlEnum.KEPLER}/upms/u/tenants/tree`,params);
// }


// /**
//  * 获取应用菜单
//  * @param params 应用id
//  */
// export function getAppMenu(params) {
//   // return request.get(`${BaseUrlEnum.KEPLER}/expose/menu/appId/${params}`,{});
//   return request.get(`${BaseUrlEnum.KEPLER}/upms/p/reso/cloud/permissions/current/user`);
// }

// /**
//  * 获取微应用列表
//  * @param params 应用id
//  */
// export function getMicroApps(params) {
//   return request.get(`${BaseUrlEnum.KEPLER}/aims/micro/apps`, {params});
// }