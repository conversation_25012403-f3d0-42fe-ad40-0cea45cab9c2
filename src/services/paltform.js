import request from "@/utils/axiosInstance";
export const API_AMP = "/app-management";


// 主题配置获取
export const getSysConfig = (params) => {
  return request.get(`${API_AMP}/system/loginPageSettings`, params || {});
};

// 获取平台是否配置前台工作台页面
export const getWorkspaceUrl = () => {
  return request.get(`${API_AMP}/system/workspace`);
}

// 获取二类菜单资源列表
export const getSubSourceList = (reqInfo, headers) => {
  const { url, params } = reqInfo; 
  return request.get(url, {
    params,
    headers,
  });
};

// 获取对应资源应用下的第一个资源
export const getFirstResource = (params, headers) => {
  return request.get(`${API_AMP}/resourceInstances/organ/init`, { params, headers });
};

/**
 * 用户事件埋点
 * eventType: organ_view | app_view | project_view | sub_system_view
 * eventData: 对应资源类型实例的Id
 */
export const saveUserEvent = (params) => {
  return request.post(`${API_AMP}/userEvent`, {
    params,
    headers: { "Amp-Organ-Id": storage.getSession('organizationId', true) }
  });
};