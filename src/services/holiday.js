import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'
// 获取分类
export const getHolidayList = (params) => {
     
    return request.get(`${BaseUrlEnum.BASEHOLIDAY}/api/holiday/manager/list`,{params})
  };
  //获取国家
  export const getCountry = (params) => {
   
  return request.get(`${BaseUrlEnum.BASEHOLIDAY}/api/holiday/country/list`,{params})
};
export const customizeHoliday = (params) => {
    return request.post(`${BaseUrlEnum.BASEHOLIDAY}/api/holiday/add/customize`,{params})
  };
  //获取导入填写规则
  export const importContentRule = (params) => {
    return request.get(`${BaseUrlEnum.BASEHOLIDAY}/api/import/config/content`,{params})
  };
  //校验是否填写正确
  export const checkContent = (params) => {
    return request.postF(`${BaseUrlEnum.BASEHOLIDAY}/api/import/check?type=1`,{params})
  };
  //导入数据
  export const importContent = (params) => {
    return request.post(`${BaseUrlEnum.BASEHOLIDAY}/api/import/data?type=1`,{params})
  };
  //节假日删除
  export const deleteHoliday = (params) => {
    return request.post(`${BaseUrlEnum.BASEHOLIDAY}/api/holiday/delete/customize`,{params})
  };
  //下载模板
  export const downloadExcel = () => {
    return request.get(`${BaseUrlEnum.BASEHOLIDAY}/api/import/template?type=1`,{responseType: 'blob'})
  };
  
  
  
  
