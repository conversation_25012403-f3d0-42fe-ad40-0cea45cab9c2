import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'

export const getWorkbenchInfo = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/userInfo`)
};

export const getWorkbenchData = (id) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/info/${id}`)
};

export const getWorkbenchConfig = () => {
    return request.get(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/getConfig/1`)
}

export const saveWorkbenchConfig = (params) => {
    return request.post(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/saveConfig`, {params})
}

export const getNoticeList = (params) => {
    return request.post(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/notice`, {params})
}

// 工作事项列表查询
export const getWorkItemList = (id, params) => {
    return request.post(`${BaseUrlEnum.KEPLERAPI}/personal/workspace/workItem/${id}`, {params})
}

export const getDictChildren = (params) => {
    return request.get(`${BaseUrlEnum.KEPLER}/upms/dict/parentType/children/tree`, {params})
}