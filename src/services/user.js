import request from "@/utils/axiosInstance";
import { API_AMP } from "@/services";
import { BaseUrlEnum } from '@/enums/httpEnum'

// 获取用户信息
export const getUserInfoByUserCode = (userCode) => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/users/info/${userCode}`);
};


// 获取用户信息
export const getUserInfo = () => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/users/current`);
};

// 获取用户详细信息
export function getUserDetailInfo() {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/users/current/info`);
}

// 获取用户权限
export function getUserAuthMap() {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/p/reso/cloud/permissions/current/user/auth`);
}

// 用户登出
export const loginOut = () => {
  return request.delete(`${BaseUrlEnum.KEPLER}/auth/logout`);
}




// 获取用户系统设置下的应用列表
export const getUserSystemApp = () => {
  return request.get(`${API_AMP}/apps/type?type=2`, {
    headers: { "Amp-Organ-Id": "1" },
  });
};
// 根据系统应用列表判断是否展示系统设置
export const getSystemShowOrNot = (codes) => {
  return request.get(`${API_AMP}/apps/organ/check/appCodes?appCodes=${codes}`, {
    headers: { "Amp-Organ-Id": "1" },
  });
};

export const fetchWorkbenchOrgList = (token) => {
  return request.get(`${API_AMP}/user-organizations/users/`, {
    headers: { "Amp-Organ-Id": "1", Authorization: token },
  });
};

// 获取当前用户下的组织列表
export const fetchUserOrgList = () => {
  return request.get(`${API_AMP}/organizations/users/adaption/tree`, {
    headers: { "Amp-Organ-Id": "1" },
  });
};

// 通知服务端更新cookie里面的租户Id
export const updateOrgIdInCookie = (orgId) => {
  return request.get(`${API_AMP}/organizations/cookie`, {
    params: {
      organId: orgId,
    }
  });
};

// 获取系统设置组织类型
export const getSystemSettingOrganType = () => {
  return request.get(`${API_AMP}/user-organizations/single/user`);
};

export const changeUserPassword = (params, token = "") => {
  return request.post(`${API_AMP}/users/password`, {
    params,
    headers: {
      "Amp-Organ-Id": params.organId,
      "Amp-App-Code": "application",
      Authorization: token,
    },
  });
};

export const changeUserInfoData = (params) => {
  return request.post(`${API_AMP}/users/update`, {
    params,
  });
};

// 用户登录
export const accountSignIn = (params) => {
  return request.post(`${API_AMP}/provider/users/v2/noCode/login`, {
    params,
  });
  // return request.post(`${API_AMP}/users/noCode/login`, { params });
};

// 用户登出
export const accountSignOut = () => {
  return request.post(`${API_AMP}/users/logout`, {
    headers: { "Amp-Organ-Id": "1", "Amp-App-Code": "application" },
  });
};

export const querySingleOrganAndApp = () => {
  return request.get(`${API_AMP}/users/single/organ/app`);
};

// 定时器 token续签
export const refreshToken = () => {
  return request.get(`${API_AMP}/users/token/refresh`);
};

// 获取头部部门 + 项目列表
export const fetchDepartmentProjects = (orgId) => {
  return request.get(
    `${API_AMP}/resourceInstances/organ/user/resourceTypeCode?resourceTypeCode=project`,
    {
      headers: { "Amp-Organ-Id": orgId },
    }
  );
};
