import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'
import axios from 'axios'
import Local from "@/utils/storage";
import {localStorageEnum} from "@/enums/storageEnum";
const TOKEN_ENUM = localStorageEnum.TOKEN


// 对象存储申请
export const applyObjectStorage = (params) => {
  return request.post(`${BaseUrlEnum.BASELINEAPI}/object/storage/apply`,{params});
};

// 对象存储申请查询详情
export const getObjectStorageDetail = (bucketName) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/object/storage/statistics/list/bucket`,{params:{bucketName}});
};

// 上传文件
export const uploadFileForObjectStorage = (data) => {
  return axios.post(`${BaseUrlEnum.BASELINEAPI}/object/storage/apply/upload`,data,{
    headers: {
      'Content-Type': 'multipart/form-data',
      Authorization: `Bearer ${Local.getLocal(TOKEN_ENUM)}`,
    },
  });
};