// import { apiPrefixMap } from '@/utils/constant';
// import request from '@/utils/request';
// import { cancelPromise } from '@/utils/utils';

// export async function getApp(): API.PageResp<InteAppInfo> {
//     return request.get(`/aims/apps/simple/list`);
// }
// export async function getReviewAppList(id: any): API.PageResp<InteAppInfo> {
//     return request.get(
//         `/program/open-source/compt/baseline/review/app/list?applicationSystemId=${id}`,
//     );
// }
// export async function updateReview(
//     params?: any,
//     options?: any,
// ): API.ArrResp<InteAppInfo> {
//     return request.put(
//         `/program/open-source/compt/baseline/review/app/update`,
//         params,
//         options,
//     );
// }
// export function getReviewList(params: any) {
//     return request.get(
//         `/program/open-source/compt/baseline/review/list`,
//         params,
//     );
// }
// // 文档下载-无需认证(提供给全局文档库)接口
// export const download = (
//     applicationSystemName: string,
//     responseType?: ResponseType,
// ): Promise<any> => {
//     return request.get(
//         `/program/open-source/compt/baseline/review/export?applicationSystemName=${applicationSystemName}`,
//         { responseType: responseType },
//         { skipBase: true, isBlob: true },
//     );
// };
import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'

// export const getApp = (params) => {
//     return request.get(`${BaseUrlEnum.KEPLER}/aims/apps/simple/list`,{params})
//   };
export const getApp = () => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/open-source/compt/baseline/review/no-review/list`)
};
  export const getReviewAppList = (id) => {
    return request.get(`${BaseUrlEnum.BASELINEAPI}/open-source/compt/baseline/review/app/list?applicationSystemId=${id}`)
  };

  export const updateReview= (params,headers) => {
    return request.put(`${BaseUrlEnum.BASELINEAPI}/open-source/compt/baseline/review/app/update`,
    {
    params:{ ...params},
    headers: {...headers},
  });
  };
  // export const addFavorite = (params,headers) => {
  //   return request.post(`${Kepler_Base}/add-favorite/${params.id}`,{
  //     params: { ...params, noCache: new Date().getTime() },
  //     headers: {...headers, usertoken: usertoken},
  //   });
  // };
//   export async function updateReview(
//     params,
//     options
// ): API.ArrResp<InteAppInfo> {
//     return request.put(
//         `/program/open-source/compt/baseline/review/app/update`,
//         params,
//         options,
//     );
// }
  
  export const getReviewList = (params) => {
    return request.get(`${BaseUrlEnum.BASELINEAPI}/open-source/compt/baseline/review/list`,{params});
  };
