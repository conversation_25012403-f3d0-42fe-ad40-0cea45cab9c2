import request from "@/utils/axiosInstance";
import { BaseUrlEnum } from '@/enums/httpEnum'
// 脚手架使用情况
export const scaffoldTemplate = (params) => {
    return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/scaffold-template`,{params})
  };
 // 技术栈使用情况
export const techStackRank = (params) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/tech-stack-rank`,{params})
}; 
export const docCount = (params) => {
  return request.get(`${BaseUrlEnum.PLATDOC}/provider/document/count`,{params})
};
export const apiCount = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/api/count`,{params})
};
export const statisticApp = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/apps/statistic/app`,{params})
};
export const statisticDomain = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/apps/statistic/businessDomain`,{params})
};

export const domainMapList = (params) => {
  return request.get(`${BaseUrlEnum.UPMS}/dict/type/business_domain`,{params})
};
export const levelMapList = (params) => {
  return request.get(`${BaseUrlEnum.UPMS}/dict/type/app_service_level`,{params})
};
export const constructMapList = (params) => {
  return request.get(`${BaseUrlEnum.UPMS}/dict/type/construction_type`,{params})
};

export const statisticMiddle = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/apps/statistic/middle`,{params})
};
export const getUserList = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/u/users`,{params})
};
export const getUserSysCommit = (params) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/user-sys-commit`,{params})
};

//中间件实例数/中间件饼图/中间资源统计 3个公用接口
export const getMiddlewareInfos = () => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/infos`)
};

//集群列表接口
export const getMiddlewareCluster = () => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/clusters`)
};
//中间件集群资源使用情况
export const getMiddlewareResources = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/cluster/resources`,{params})
};

//应用系统前10使用情况
export const getAppRank = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/apps/rank`,{params})
};
//应用域资源占用情况
export const getDomainsRank = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/domains/rank`,{params})
};

//域资源使用趋势图
export const getResourcesTrend = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/resources/trend`,{params})
};



//中间件列表，分页查询
export const getAppsList = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/apps/list`,{params})
};

//中间件类型
export const getMiddleList = () => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/list`)
};

//应用-中间件资源总量统计，仅显示查询后结果
export const getSummary = (params) => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/apps/list/summary`,{params})
};

//获取应用列表，应用域接口
export const getDomainsApp = () => {
  return request.get(`${BaseUrlEnum.PLATAPP}/dashboard/middleware/domains/apps`)
};
//获取应用域对应的label
export const getDomainslabel = () => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/dict/parentType/children/tree?type=app_domain&label=&current=1&size=999`)
};




//应用程序统计

export const getStatistics = () => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/statistics`)
};
//用户访问统计
export const getVisitCount = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/visit/count`,{params})
};
//文档浏览统计
export const getDocCount = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/knowledge/count`)
};
//api统计
export const getApiCount = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/api/info`)
};
// 开源组件使用情况统计
export const getOpenSourceCount = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/openSourceCompt`)
};
//pass统计
export const getPassCount = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/paas/count`)
};
//共享组件
export const getShareCount = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/share-component/count`)
};
//脚手架
export const getScaffoldRank = () => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/scaffoldTemplateInfo/rank`)
};
//流水线
export const getPipelineCount = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/pipeline`,{params})
};
// 获取全部应用系统
export const getAppSystemListAll = (params) => {
  return request.get(`${BaseUrlEnum.KEPLER}/aims/apps/simple/list`,{params});
};
//代码提交
export const codeCount = (params) => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/dashboard/api/count`,{params})
}
//代码提交
export const getUserSysCommitInfo = (params) => {
  return request.get(`${BaseUrlEnum.BASELINEAPI}/base/dashboard/sys-commit`,{params})
};
//获取域名
export const getBussinessDomainslabel = () => {
  return request.get(`${BaseUrlEnum.KEPLER}/upms/dict/parentType/children/tree?type=business_domain&label=&current=1&size=999`)
};


// 价值看板
export const getValueDashboardTool = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/tool`)
};
export const getValueDashboardSystem = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/sync-systems`)
};
export const getValueDashboardScaffold = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/scaffold`)
};
export const getValueDashboardService = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/component-service`)
};

export const getValueDashboardSecurity = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/security-statistics`)
};

export const getValueDashboardPaas = () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/paas-statistics`)
};

export const getValueDashboardApi= () => {
  return request.get(`${BaseUrlEnum.KEPLERAPI}/value-dashboard/api-statistics`)
};
