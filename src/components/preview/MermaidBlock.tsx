import React, { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';

export default function MermaidBlock({ code }: { code: string }) {
    const ref = useRef<HTMLDivElement>(null);
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (!ref.current) return;
        if (typeof window === 'undefined') return;

        let isMounted = true;

        try {
            mermaid.initialize({
                startOnLoad: false,
                theme: 'neutral',
                themeVariables: {
                    primaryColor: '#e5e7eb',
                    primaryTextColor: '#333333',
                    primaryBorderColor: '#bdbdbd',
                    lineColor: '#bdbdbd',
                    fontFamily: 'inherit'
                }
            });

            const id = 'mermaid-svg-' + Math.random().toString(36).slice(2);
            mermaid.render(id, code).then(({ svg }) => {
                if (!isMounted || !ref.current) return;
                ref.current.innerHTML = svg;
                console.log('mermaid svg rendered');
            }).catch((err) => {
                if (!isMounted || !ref.current) return;
                setError(err.message);
                console.error('mermaid render error:', err);
            });
        } catch (e) {
            if (!isMounted || !ref.current) return;
            setError(e instanceof Error ? e.message : String(e));
            console.error('mermaid catch error:', e);
        }

        return () => {
            isMounted = false;
        };
    }, [code]);

    if (error) {
        return (
            <div className="my-4 p-4 bg-red-50 border border-red-200 rounded-lg">
                <pre className="text-red-600 text-sm whitespace-pre-wrap break-words">{error}</pre>
                <pre className="mt-2 text-sm text-gray-600 whitespace-pre-wrap break-words">{code}</pre>
            </div>
        );
    }

    return <div ref={ref} className="mermaid my-4" />;
}