import React, { lazy, useMemo } from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { vscDarkPlus } from 'react-syntax-highlighter/dist/esm/styles/prism';
import 'katex/dist/katex.min.css';
import mermaid from 'mermaid';

interface MarkdownRendererProps {
  content: string;
  showToc?: boolean;
}

// 动态导入 MermaidBlock 组件
const MermaidBlock = lazy(() => import('./MermaidBlock'));

export const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, showToc = true }) => {
  // 1. 解析标题
  const headings = useMemo(() => {
    if (!showToc) return [];
    const regex = /^(#{1,6})\s+(.+)$/gm;
    const result: { level: number; text: string; id: string }[] = [];
    let match;
    while ((match = regex.exec(content))) {
      const level = match[1].length;
      const text = match[2].trim();
      const id = encodeURIComponent(text.replace(/\s+/g, '-'));
      result.push({ level, text, id });
    }
    return result;
  }, [content, showToc]);

  // 2. 渲染
  return (
    <div className="flex gap-8">
      {/* 主体 - 添加 min-w-0 确保内容可以收缩 */}
      <div className="flex-1 min-w-0">
        <ReactMarkdown
          remarkPlugins={[remarkGfm, remarkMath]}
          rehypePlugins={[rehypeKatex]}
          components={{
            // 标题样式
            h1: ({ node, ...props }) => {
              const text = String(props.children);
              const id = encodeURIComponent(text.replace(/\s+/g, '-'));
              return <h1 id={id} className="text-3xl font-bold !mb-4" {...props} />;
            },
            h2: ({ node, ...props }) => {
              const text = String(props.children);
              const id = encodeURIComponent(text.replace(/\s+/g, '-'));
              return <h2 id={id} className="text-2xl font-bold !mb-3" {...props} />;
            },
            h3: ({ node, ...props }) => {
              const text = String(props.children);
              const id = encodeURIComponent(text.replace(/\s+/g, '-'));
              return <h3 id={id} className="text-xl font-bold !mb-2" {...props} />;
            },

            // 段落和列表样式
            p: ({ node, ...props }) => <p className="!mb-4 break-words" {...props} />,
            ul: ({ node, ...props }) => <ul className="list-disc pl-6 !mb-4" {...props} />,
            ol: ({ node, ...props }) => <ol className="list-decimal pl-6 !mb-4" {...props} />,
            li: ({ node, ...props }) => <li className="!mb-1" {...props} />,

            // 代码块样式
            code: ({ node, inline, className, children, ...props }: any) => {
              const match = /language-(\w+)/.exec(className || '');
              if (!inline && match && match[1] === 'mermaid') {
                return <MermaidBlock code={String(children)} />;
              }
              return !inline && match ? (
                <div className="w-full overflow-x-auto">
                  <SyntaxHighlighter
                    style={vscDarkPlus}
                    language={match[1]}
                    PreTag="pre"
                    className="rounded-lg !mb-4 !w-full"
                    useInlineStyles={true}
                    customStyle={{
                      margin: 0,
                      padding: '1rem',
                      width: '100%',
                      maxWidth: '100%',
                      overflow: 'auto',
                      // wordWrap: 'break-word',
                      whiteSpace: 'pre-wrap',
                    }}
                    codeTagProps={{
                      style: {
                        whiteSpace: 'pre-wrap',
                        wordBreak: 'break-word',
                        overflowWrap: 'anywhere',
                      }
                    }}
                    {...props}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                </div>
              ) : (
                <code className="bg-muted px-1.5 py-0.5 rounded text-sm whitespace-pre-wrap break-words" {...props}>
                  {children}
                </code>
              );
            },

            // 其他样式
            blockquote: ({ node, ...props }) => (
              <blockquote className="border-l-4 border-muted-foreground pl-4 italic !mb-4 break-words" {...props} />
            ),
            a: ({ node, ...props }) => <a className="text-primary hover:underline" {...props} />,
            img: ({ node, ...props }) => <img className="max-w-full rounded-lg !mb-4" {...props} />,

            // 表格样式
            table: ({ node, ...props }: any) => (
              <div className="overflow-x-auto !mb-6 rounded-lg border">
                <table className="w-full border-collapse" {...props} />
              </div>
            ),
            thead: ({ node, ...props }: any) => <thead className="bg-muted/50" {...props} />,
            tbody: ({ node, ...props }: any) => <tbody className="divide-y divide-border" {...props} />,
            tr: ({ node, ...props }: any) => <tr className="hover:bg-muted/30 transition-colors" {...props} />,
            th: ({ node, ...props }: any) => <th className="px-6 py-3 text-left font-medium border-b" {...props} />,
            td: ({ node, ...props }: any) => <td className="px-6 py-4" {...props} />,
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default MarkdownRenderer;