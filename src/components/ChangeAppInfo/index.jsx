import { useState } from "react";
import { useModel, history } from 'umi';
import { LeftOutlined, SwapOutlined, LoadingOutlined } from "@ant-design/icons";
import EllipsisTooltip from "@/components/ellipsis";
import AppInfoDialog from "./AppInfoDialog";
import { appSystemAndProgramTopMenus } from "@/constants";
import { Spin } from 'antd';
import "./index.less";
import { color } from "@uiw/react-codemirror";

const ChangeAppInfo = () => {
  const [visible, setVisible] = useState(false);
  const [ isProgram, setIsProgram ] = useState(false);
  const { selectAppInfo, setSelectAppInfo, setCurrentAppSystemAndProgramDetail, isLinkChange, setIsLinkChange } = useModel('appSystemInfo')
  const { jumpAndUpdateSideBarMenus } = useModel('menu');
  const [cardLoading, setCardLoading] = useState(false);

  const jumpSystem = () => {
    if(isProgram){// 跳转到应用程序列表页
      jumpAndUpdateSideBarMenus("/kepler-webpage/svc/integrationService/applicationIntegration/app/:id", {
        id: selectAppInfo.appSystemId
      });
    }else{// 跳转到应用系统列表页
      history.push(appSystemAndProgramTopMenus[0])
    }
  };
  return (
    <>
      <div className="change-app-info-wrapper">
        <Spin spinning={cardLoading} indicator={<LoadingOutlined spin />} size="small" style={{ color: 'white'}}>
          <div className="change-app-info">
            <LeftOutlined className="change-app-info-icon" onClick={jumpSystem}/>
              <div className="change-app-info-content">
                  {isProgram ? (
                    <>
                      <EllipsisTooltip maxWidth={140} title={selectAppInfo.appProgramName} placement="right">
                        <span className="change-app-info-content-title">
                          {selectAppInfo.appProgramName}
                        </span>
                      </EllipsisTooltip>
                      <EllipsisTooltip maxWidth={140} title={selectAppInfo.appSystemName} placement="right">
                        <span className="change-app-info-content-sub-title">
                          {selectAppInfo.appSystemName}
                        </span>
                      </EllipsisTooltip>
                    </>
                  ) : 
                  <EllipsisTooltip maxWidth={140} title={selectAppInfo.appSystemName} placement="right">
                    <span className="change-app-info-content-title">
                      {selectAppInfo.appSystemName}
                    </span>
                  </EllipsisTooltip>}
              </div>
            <SwapOutlined className="change-app-info-icon" onClick={() => setVisible(true)}/>
          </div>
        </Spin>
      </div>
      <AppInfoDialog
        selectAppInfo={selectAppInfo}
        setSelectAppInfo={setSelectAppInfo}
        visible={visible}
        setVisible={setVisible}
        isProgram={isProgram}
        setIsProgram={setIsProgram}
        setCurrentAppSystemAndProgramDetail={setCurrentAppSystemAndProgramDetail}
        isLinkChange={isLinkChange}
        setIsLinkChange={setIsLinkChange}
        setCardLoading={setCardLoading}
      />
    </>
  );
};

export default ChangeAppInfo;
