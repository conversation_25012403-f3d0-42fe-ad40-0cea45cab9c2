.change-app-info{
  width: 100%;
  padding: 12px 14px;
  border-radius: @border-radius-large;
  font-family: PingFang SC;
  font-size: @font-size-normal;
  color: #FFFFFF;
  min-height: 68px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: url(@/assets/images/changeAppBg.png) no-repeat center;
  background-size: cover;
  display: flex;
  justify-content: space-between;
  align-items: center;
  &-icon{
    cursor: pointer;
    font-size: @font-size-normal;
    color: #fff;
  }
  &-content{
    overflow: hidden;
    padding: 0 8px;
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    transform: translateY(-1px);
    &-title{
      display: inline;
      font-size: @font-size-normal;
    }
    &-sub-title{
      display: inline;
      font-size: @font-size-small;
    }
  }
}
.change-app-info-wrapper{
  margin-bottom: 14px;
}