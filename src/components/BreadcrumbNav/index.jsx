import { useState } from "react";
import { Breadcrumb } from 'antd';
import { history } from 'umi';
import { LeftOutlined } from '@ant-design/icons';
import style from './index.less';
const BreadcrumbNav = (props) => {
  const { list, isNeedBack } = props
  return (
    <div className={style.platformBreadcrumb}>
      <Breadcrumb>
        {list.map((item) =>   
          <Breadcrumb.Item key={item.title} href={item.href}>{item.title}</Breadcrumb.Item>
        )}
      </Breadcrumb>
      { isNeedBack &&
        <div className={style.backButton} onClick={() => history.back()}>
          <LeftOutlined />
          <span>返回</span>
        </div>}
    </div>
  );
};

export default BreadcrumbNav;
