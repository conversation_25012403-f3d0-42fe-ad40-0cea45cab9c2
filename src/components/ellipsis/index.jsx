import React, { useRef, useState, useEffect } from 'react';
import { Tooltip } from 'antd';

const EllipsisTooltip = ({ children, title, maxWidth, placement='topLeft' }) => {
  const textRef = useRef(null);
  const [isOverflow, setIsOverflow] = useState(false);

  useEffect(() => {
    const checkOverflow = () => {
      if (textRef.current) {
        setIsOverflow(textRef.current.scrollWidth > textRef.current.clientWidth);
      }
    };
    checkOverflow();
  }, [title, children]);

  return (
    <Tooltip title={isOverflow ? title : ''} placement={placement} color="#fff">
      <div
        ref={textRef}
        className="com-text-ellipsis"
        style={{ maxWidth: maxWidth || '100%' }}
      >
        {children}
      </div>
    </Tooltip>
  );
};

export default EllipsisTooltip;
