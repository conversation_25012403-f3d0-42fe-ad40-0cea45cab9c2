import { useState, useEffect } from "react";

// 应用程序、应用系统状态
export default function appSystemInfo() {
  // 选中的应用系统、应用程序信息（只包含id，name字段）
  const [ selectAppInfo, setSelectAppInfo ] = useState({
    appSystemId: null,
    appSystemName: null,
    appProgramId: null,
    appProgramName: null
  });
  // 当前选中的应用程序和应用系统的详细信息
  const [ currentAppSystemAndProgramDetail, setCurrentAppSystemAndProgramDetail] = useState({
    appSystemDetail: null, // 应用系统
    appProgramDetail: null, // 应用程序
  });
  // 是否展示切换组件
  const [showChangeAppInfo, setShowChangeAppInfo] = useState(false);

  // 设置当前是否是链接触发的更改
  const [isLinkChange, setIsLinkChange] = useState(false);
  
  return {
    selectAppInfo,
    showChangeAppInfo,
    currentAppSystemAndProgramDetail,
    isLinkChange,
    setSelectAppInfo,
    setCurrentAppSystemAndProgramDetail,
    setShowChangeAppInfo,
    setIsLinkChange
  };
}
