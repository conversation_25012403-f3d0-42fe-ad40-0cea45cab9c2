
import { useState } from "react";
import { menuLayoutEnum } from "@/enums/menuEnum"

const isInFrame = window.top != window.self;
export default function sysConfig() {
  const [bgImageName, setBgImageName] = useState();
  const [copyRight, setCopyRight] = useState();
  const [platformName, setPlatFormName] = useState();
  const [slogan, setSlogan] = useState();
  const [tabTitle, setTabTitle] = useState();
  const [enableWatermark, setEnableWatermark] = useState();
  
  // 布局配置
  const [layoutConfig, setLayoutConfig] = useState({
    // 菜单---------布局模式
    // mix：混合模式，顶部和侧栏都渲染菜单，并且侧栏菜单由顶部菜单决定
    // header: 顶部菜单模式，顶部渲染完整的菜单，侧栏不渲染菜单
    // sidebar: 侧栏菜单模式，侧栏渲染完整的菜单，顶部不渲染菜单
    menuLayout: menuLayoutEnum.mix,
    // 是否隐藏侧边栏
    hiddenSideBar: true, // 默认不展示
    // 是否隐藏标签栏
    hideTabs: isInFrame,
    // 是否隐藏表头
    hiddenHeader: isInFrame
  })

  // 是否展示后台管理入口
  const [ isShowAdminEntry, setIsShowAdminEntry ] = useState(false);

  // 改变侧边栏展示
  const changeSideBarVisible = (visible) => {
    setLayoutConfig({
      ...layoutConfig,
      hiddenSideBar: !visible
    })
  }

  return {
    bgImageName,
    setBgImageName,
    copyRight,
    setCopyRight,
    platformName,
    setPlatFormName,
    slogan,
    setSlogan,
    tabTitle,
    setTabTitle,
    enableWatermark,
    setEnableWatermark,

    layoutConfig,
    setLayoutConfig,
    changeSideBarVisible,
    isShowAdminEntry,
    setIsShowAdminEntry
  };
}
