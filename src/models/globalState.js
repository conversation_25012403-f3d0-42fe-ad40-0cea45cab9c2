import { useState } from "react";

export default function globalState() {
  // 乾坤全局状态监听，组织/项目，在切换时，需要先切换name,在切换id 因为需要显示name, 但监听的是id的变更
  const [authElements, setAuthElements] = useState();
  const [orgId, setOrgId] = useState();
  const [orgName, setOrgName] = useState();
  const [projectId, setProjectId] = useState();
  const [projectName, setProjectName] = useState();
  const [appId, setAppId] = useState();
  const [clusterName, setClusterName] = useState();

  const clearGlobalState = () => {
    setAuthElements(null);
    setOrgId(null);
    setOrgName(null);
    setProjectId(null);
    setProjectName(null);
    setAppId(null);
    setClusterName(null);
  }

  return {
    authElements,
    setAuthElements,
    orgId,
    setOrgId,
    orgName,
    setOrgName,
    projectId,
    setProjectId,
    projectName,
    setProjectName,
    appId,
    setAppId,
    clusterName,
    setClusterName,
    clearGlobalState
  };
}
