import { useState, useEffect } from "react";
import storage from "@/utils/storage";
import { localStorageEnum } from '@/enums/storageEnum';

const USERINFO = localStorageEnum.USERINFO; // 保存用户信息
const TOKEN = localStorageEnum.TOKEN; // 保存token信息
const USERAUTHMAP = localStorageEnum.USERAUTHMAP; // 保存token信息

export default function user() {
  const [token, setToken] = useState(storage.getLocal(TOKEN) || null);
  const [userInfo, setUserInfo] = useState(storage.getLocal(USERINFO) || null);
  const [userAuthMap, setUserAuthMap] = useState(storage.getLocal(USERINFO) || null);
  const [userDetailInfo, setUserDetailInfo] = useState(null);
  
  const setUserInfoStore = (userInfo) => {
    setUserInfo(userInfo);
    storage.setLocal(USERINFO, userInfo);
  }
  const clearUserInfoStore = () => {
    setUserInfo(null)
    storage.removeLocal(USERINFO)
  };

  const setTokenStore = (token) => {
    setToken(token);
    storage.setLocal(TOKEN, token);
  };

  const clearTokenStore = () => {
    setToken(null);
    storage.removeLocal(TOKEN);
  };

  const setUserMapStore = (mapData) => {
    setUserAuthMap(mapData)
    storage.setLocal(USERAUTHMAP, mapData);
  }

  const clearUserMapStore = () => {
    setUserAuthMap(null)
    storage.removeLocal(USERAUTHMAP)
  }

  useEffect(() => {
    const handleUnauthorized = () => {
      storage.removeLocal(TOKEN);
      storage.removeLocal(USERINFO);
      clearTokenStore();
      clearUserInfoStore();
      setUserDetailInfo(null)
    };

    // 监听自定义事件
    window.addEventListener('unauthorized', handleUnauthorized);

    return () => {
      window.removeEventListener('unauthorized', handleUnauthorized);
    };
  }, []);
  return {
    token,
    userInfo,
    userDetailInfo,
    setUserInfoStore,
    clearUserInfoStore,
    setTokenStore,
    clearTokenStore,
    setUserDetailInfo,
    setUserMapStore,
    clearUserMapStore,
  };
}
