// 是否是叶子节点
export const isLeaf = (tree) => {
  return !tree.children?.length;
};

// 递归获取第一个叶子节点
export const getFirstLeaf = (tree) => {
  if (!tree.children?.length) {
    return tree;
  } else {
    return getFirstLeaf(tree.children[0]);
  }
};

// 是否菜单叶子节点，不包含页面元素
export const isMenuLeaf = (tree) => {
  const children = (tree.children || []).filter((child) => child.type === 1);
  return !children.length;
};

// 默认获取最后一层第一个非页面元素节点 [默认跳转]
export const getFirstUrlNode = (tree) => {
  if (
    (!tree.children?.length || tree.children[0].type === 2) &&
    tree.type !== 2 &&
    tree.url
  ) {
    return tree;
  } else if (tree.children?.length && tree.children[0].type === 1) {
    return getFirstUrlNode(tree.children[0]);
  }
};

//树打平
export const flatten = (data) => {
  const newData = [...data];
  return newData.reduce(
    (
      arr,
      item
    ) =>
      arr.concat(
        [
          {
            ...item
          },
        ],
        flatten(item?.children ?? [])
      ),
    []
  );
};

// 根据id获取树状数据中对应的节点数据
export function getNodeInTree(tree, id) {
  for (let item of tree) {
    if (item.id === id) {
      return item;
    }

    if (item.children && item.children.length > 0) {
      const result = getNodeInTree(item.children, id);
      if (result) {
        return result;
      }
    }
  }

  return null;
}