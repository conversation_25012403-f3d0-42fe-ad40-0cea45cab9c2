// 动态注册子应用, 以及路由，返回qiankun需要的数据对象
import { getMicroApps } from "@/services";
import unifyHcIconToGray from "@/utils/iconColor";
import { judgeRagEnable, hideRag } from "./rag";
export const registerMicroApps = async(appId) => {
  if (!appId) return { apps: [], routes: [] };
  try {
    const res = await getMicroApps({ appId });
    let microList = res || [];
    // microList = [{
    //   "id": "1869725745832529922",
    //   "name": "集成中台",
    //   "code": "kepler-webpage",
    //   "route": "/kepler-webpage/*",
    //   // "entry": "//*************:33435/kepler-webpage/index.html"// 海创园环境
    //   //"entry": "https://tasp-dev.trinasolar.com/kepler-webpage/index.html"// 天合环境
    //   "entry": "//localhost:8081/kepler-webpage/index.html"
    // },{
    //   "id": "1869725745832529920",
    //   "name": "应用程序管理",
    //   "code": "system",
    //   "route": "/system/*",
    //   // "entry": "//*************:33436/system/index.html"// 海创园环境
    //   //"entry": "https://tasp-dev.trinasolar.com/system/index.html"// 天合环境
    //   "entry": "//localhost:8082/system/index.html"
    // },{
    //   "id": "1869725745832529923",
    //   "name": "技术文档库管理",
    //   "code": "dochub",
    //   "route": "/dochub/*",
    //   // "entry": "//*************:33443/dochub/index.html" // 海创园环境
    //   "entry": "https://tasp-dev.trinasolar.com/dochub/index.html"// 天合环境
    //   // "entry": "//localhost:8001/dochub/index.html"
    // },{
    //   "id": "1869725745832529924",
    //   "name": "前端技术组件库",
    //   "code": "trinaCli",
    //   "route": "/trinaCli/*",
    //   "entry": "//localhost:3000/trinaCli/index.html"
    // },{
    //   "id": "1869725745832529923",
    //   "name": "知识库",
    //   "code": "knowledge",
    //   "route": "/knowledge/*",
    //   "entry": "https://tasp-dev.trinasolar.com/knowledge/index.html" // 天合环境
    //   // "entry": "//localhost:5173/knowledge/index.html"
    // },{
    //   "id": "1869725745832529925",
    //   "name": "效能平台",
    //   "code": "efficiency",
    //   "route": "/efficiency/*",
    //   // "entry": "//*************:34000/efficiency/index.html" // 海创园环境
    //   "entry": "https://tasp-dev.trinasolar.com/efficiency/index.html" // 天合环境
    //   // "entry": "//localhost:8003/efficiency/index.html"
    // },{
    //   "id": "1869725745832529926",
    //   "name": "IPass平台",
    //   "code": "apim",
    //   "route": "/apim/*",
    //   "entry": "//localhost:49090/apim/index.html",
    // }]
    let apps = [],
      routes = [];
    microList.forEach((item) => {
      const itemRoutes = item.route.split(",");
      apps.push({
        name: item.code,
        entry: `${item.entry}?t=${new Date().getTime()}`
      });
      itemRoutes.forEach((route) => {
        routes.push({
          path: route,
          microApp: item.code,
          microAppProps: {
            autoSetLoading: true,
            autoCaptureError: true,
          },
        });
      });
    });
    return {
      apps,
      routes,
      prefetch: "all",
      lifeCycles: {
        beforeMount: (props) => {
        },
        afterMount: (props) => {
          props.props?.setLoading && props.props.setLoading(false);
          // setImmediate(() => {
          //   unifyHcIconToGray();
          // });
          setTimeout(() => {
            judgeRagEnable(props.name)
            unifyHcIconToGray();
          }, 0);
        },
        afterUnmount: (props) => {
          hideRag();
        },
      },
    };
  } catch (error) {
    return {
      apps: [],
      routes: [],
    }
  }
};