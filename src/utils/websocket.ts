import WebsocketHeartbeatJs from './websocketHeartbeatJs';

let createWebSocket = (url: string) => {
  const options = {
    url,
    pingTimeout: 15000,
    pongTimeout: 10000,
    reconnectTimeout: 15000,
    pingMsg: 'heartbeat',
  };
  const websocket = new WebsocketHeartbeatJs(options);

  websocket.onopen = function () {
    console.log('成功连接!');
  };
  websocket.onreconnect = () => {
    console.log('重连中...');
  };
  websocket.onclose = (e: any) => {
    console.log('socket 断开: ', e);
  };
  websocket.onerror = (e: any) => {
    console.log('socket错误：', e);
  };
  return websocket;
};

export { createWebSocket };
