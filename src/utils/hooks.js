import { useEffect, useCallback, useRef } from 'react';

export function useDebounce(fn, delay = 500) {
    const { current } = useRef({ fn, timer: null });
    useEffect(
        function () {
            current.fn = fn;
        },
        [current.fn, fn]
    );
    return useCallback(
        function f(...args) {
            if (current.timer) {
                clearTimeout(current.timer);
            }
            current.timer = setTimeout(() => {
                current.fn.call(this, ...args);
            }, delay);
        },
        [current.fn, current.timer, delay]
    );
}

export function useThrottle(fn, delay = 500) {
    const { current } = useRef({ fn, timer: null });
    useEffect(
        function () {
            current.fn = fn;
        },
        [current.fn, fn]
    );
    return useCallback(
        function f(...args) {
            if (!current.timer) {
                current.timer = setTimeout(() => {
                    delete current.timer;
                }, delay);
                current.fn.call(this, ...args);
            }
        },
        [current.fn, current.timer, delay]
    );
}

// 跨标签页监听localstorage发生改变的的hook函数
export function useLocalStorageListener(key, callback) {
    useEffect(() => {
        const storageListener = (event) => {
            if (event.key === key) {
                callback(event.newValue, event.oldValue);
            }
        };

        window.addEventListener('storage', storageListener);

        return () => {
            window.removeEventListener('storage', storageListener);
        };
    }, [key, callback]);
}
