

export const loadRagJS = () => {
  const _RAG = window?._th_appInfo?._RAG ?? null;
  if(!_RAG?.url) return
  const script = document.createElement('script');
  script.src = window?._th_appInfo?._RAG?.url ?? null;
  script.defer = true;
  script.onload = () => {// 原js将embedChatbot方法绑定到window.onload事件上的，等动态js加载完成onload事件早执行了（谜之操作），所以这儿手动调用下初始化方法
    embedChatbot && embedChatbot()
  };
  document.body.appendChild(script);
};

// 判断应用是否启用智能助手
export const judgeRagEnable = (appName) => {
  const _RAG = window?._th_appInfo?._RAG ?? null;
  const el = document.getElementById('embedrag')
  if(el){
    el.style.display =  _RAG?.apps?.includes(appName) ? 'block' : 'none';
  }
};
// 隐藏智能助手
export const hideRag = () => {
  const el = document.getElementById('embedrag')
  if(el){
    el.style.display = 'none';
  }
};