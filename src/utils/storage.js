const getLocal = (key) => {
  const value = window.localStorage.getItem(key) || "";
  try {
    return JSON.parse(value);
  } catch (error) {
    return value;
  }
};

const setLocal = (key, value) => {
  if (typeof value === "object" || Array.isArray(value)) {
    window.localStorage.setItem(key, JSON.stringify(value));
  } else {
    window.localStorage.setItem(key, value);
  }
};

const removeLocal = (key, isClear = false) => {
  if (isClear) {
    window.localStorage.clear();
    return;
  }
  window.localStorage.removeItem(key);
};

const getSession = (key, returnStr) => {
  const value = window.sessionStorage.getItem(key) || "";
  if (returnStr) {
    // 大数字存取 parse 丢失进度， 如组织id
    return value;
  } else {
    try {
      return JSON.parse(value);
    } catch (error) {
      return value;
    }
  }
};

const setSession = (key, value) => {
  if (typeof value === "object" || Array.isArray(value)) {
    window.sessionStorage.setItem(key, JSON.stringify(value));
  } else {
    window.sessionStorage.setItem(key, value);
  }
};

const removeSession = (key, isClear = false) => {
  if (isClear) {
    window.sessionStorage.clear();
    return;
  }
  window.sessionStorage.removeItem(key);
};

export default {
  getLocal,
  setLocal,
  removeLocal,
  getSession,
  setSession,
  removeSession,
};
