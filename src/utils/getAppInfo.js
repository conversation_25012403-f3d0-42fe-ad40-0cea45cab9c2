import { getAppInfoByUrl, getHeaderBar } from "@/services";
export const getAppInfo = async () => {
  const appBackUrl = !window.isHcy ? "https://tasp-dev.trinasolar.com" : "http://*************:33434" // 本地备选地址
  const visitUrl = window.location.origin?.indexOf("localhost") > -1 ? appBackUrl : window.location.origin
  try {
    const params = {
      visitUrl
    }
    const appInfo = await getAppInfoByUrl(params)
    if(appInfo){
      return appInfo
    }else{
      console.log('获取应用信息失败！');
    }
    return res
  } catch (error) {
    console.log("获取应用信息失败：", error);
  }
}


export const getAppConfig = async (appId) => {
  try {
    const appConfig = await getHeaderBar(appId)
    if(appConfig){
      return appConfig
    }else{
      console.log('获取应用配置失败！');
    }
  } catch (error) {
    console.log("获取应用配置失败：", error);
  }
}


export const updateHtmlTitleAndFavicon = (appConfig) => {
  const link = document.querySelector("link[rel*='icon']");
  if(appConfig?.logoUrl && appConfig?.title){
    if (link) link.href = appConfig?.logoUrl;
    document.title = appConfig?.title;
  }
};