import { history } from "umi";
import CryptoJS from 'crypto-js';

export const goToLogin = (goBackUrl) => {
  if (window.location.hash === "#/login") {
    return;
  }
  if (goBackUrl && goBackUrl !== "/" && goBackUrl !== "/login") {
    const query = {};
    query.back = goBackUrl;
    history.replace({
      pathname: "/login",
      search: new URLSearchParams(query).toString(),
    });
  } else {
    history.replace("/login");
  }
};

// 用于处理路由中带参数的地址
export const restfulAPI = function (url, urlData) {
  url = url?.replace(/\{(.*?)\}/g, function (match, key) {
    return urlData[key] ? urlData[key] : undefined;
  });
  url = url?.replace(/\:(.*?)\//g, function (match, key) {
    return urlData[key] ? `${urlData[key]}/` : "undefined/";
  });
  return url;
};

export const addPrefixToRoutes = function (
  routes,
  prefix,
  parentPath = prefix
) {
  return routes.map((route) => {
    const updatedRoute = { ...route };
    if (updatedRoute.path) {
      if (updatedRoute.path.startsWith("/")) {
        updatedRoute.path = prefix + updatedRoute.path;
      } else {
        updatedRoute.path = parentPath + "/" + updatedRoute.path;
      }
    } else {
      updatedRoute.path = parentPath;
    }
    if (updatedRoute.redirect) {
      updatedRoute.redirect = prefix + updatedRoute.redirect;
    }

    if (updatedRoute.children) {
      updatedRoute.children = addPrefixToRoutes(
        updatedRoute.children,
        prefix,
        updatedRoute.path
      ); // 递归处理子路由
    }
    return updatedRoute;
  });
};

function parseQueryString(queryString) {
  let urlParams = new URLSearchParams(queryString);
  let params = {};
  for (let [key, value] of urlParams.entries()) {
    params[key] = value;
  }
  return params;
}
function sortParams(params) {
  const sortedKeys = Object.keys(params).sort();

  const sortedObj = {};
  sortedKeys.forEach((key) => {
    if (params[key] !== undefined && params[key] !== null) {
      sortedObj[key] = decodeURIComponent(params[key]);
    }
  });
  return sortedObj;
}

/**
 * 根据请求的请求头配置生成CSRF令牌。基于请求各种属性（如方法、URL、头部和数据）的MD5值，
 * 该值可以用于CSRF（跨站请求伪造）保护。
 * 
 * @param {Object} config 请求的配置对象，包含方法（method）、URL（url）、头部（headers）、参数（params）和数据（data）。
 * @returns {String} 生成的CSRF令牌的MD5字符串。
 */
export const CSRFTokenBuild = (config) => {
  let { method, url, headers, params = {}, data = {} } = config;
  if (data instanceof FormData) {
    return '';
  }
  const headerKeyList = ['Amp-Organ-Id', 'Amp-Resource-Type-Code', 'Amp-Resource-Instance-Id'];
  let headerValueList = [
    headers['Amp-Organ-Id'],
    headers['Amp-Resource-Type-Code'] || '',
    headers['Amp-Resource-Instance-Id'] || '',
  ];
  let queryParams = { ...params };
  if (url.indexOf('?') > -1) {
    let [newUrl, urlParamsStr] = url.split('?');
    url = newUrl;
    let urlParams = parseQueryString(urlParamsStr);
    queryParams = { ...queryParams, ...urlParams };
  }
  let queryString = new URLSearchParams(sortParams(queryParams)).toString();
  queryString = decodeURIComponent(queryString);

  let postDataMd5 = '';
  if (data && Object.keys(data).length > 0 && method.toLocaleLowerCase() !== 'get') {
    postDataMd5 = CryptoJS.MD5(Object.keys(data).length > 0 ? JSON.stringify(data) : '').toString();
  }
  const str = `${method.toLocaleUpperCase()}|${headerKeyList.toString()}|${headerValueList.toString()}|${url}|${queryString}|${postDataMd5}`;
  const strMd5 = CryptoJS.MD5(str).toString();
  return strMd5;
};
