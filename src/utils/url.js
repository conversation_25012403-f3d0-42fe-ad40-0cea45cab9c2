import { history } from 'umi';

export const pathUrlMatch = (url, pathname) => {
  // 将 "{test}" 替换为匹配任意值的正则表达式
  var regexPattern = url.replaceAll(/\{([^}]+)\}/g, "([^/]+)");
  // 构建正则表达式
  var regex = new RegExp("^" + regexPattern + "(/[^/]+)*$");
  return regex.test(pathname) || url.startsWith(pathname);
};

// 用于处理路由中带参数的地址
export const restfulAPI = function (url, urlData) {
  url = url?.replace(/\{(.*?)\}/g, function (match, key) {
    return urlData[key] ? urlData[key] : undefined;
  });
  url = url?.replace(/\:(.*?)\//g, function (match, key) {
    return urlData[key] ? `${urlData[key]}/` : 'undefined/';
  });
  return url;
};

export const restfulMenu = function(items, params) {
  return items.map(item => {
    const url = restfulAPI(item.originUrl || item.url, params);
    const processedItem = {
      ...item,
      originUrl: item.originUrl || item.url,
      url,
    };
    if (item.children && item.children.length > 0) {
      processedItem.children = restfulMenu(item.children, params);
    }
    return processedItem;
  });
}

export function getParamsFromSearchParams(search) {
  const searchParams = new URLSearchParams(search);
  const params = {};
  for (const [key, value] of searchParams.entries()) {
    params[key] = value;
  }
  return params;
}

export const getCommonParams = () => {
  const preHash = window.location.href.split("#")[0];
  const params = preHash.split("?")[1];
  const obj = {};
  if (params) {
    params.split("&").forEach((par) => {
      const [key, value] = par.split("=");
      obj[key] = value;
    });
    return obj;
  } else {
    return {};
  }
};

export const updateCommonParams = (key, value) => {
  let params = getCommonParams();
  let isParamArray = Array.isArray(key);
  let targetParams = [];
  if (isParamArray) {
    targetParams = key;
  } else {
    targetParams = [
      {
        key: key,
        value: value,
      },
    ];
  }
  targetParams.forEach((targetParam) => {
    const { key, value } = targetParam;
    params[key] = value;
  });

  let parStr = "?";
  let index = 0;
  Object.keys(params).forEach((key) => {
    if (params[key]) {
      const itemStr = `${key}=${params[key]}`;
      const appendStr = index ? `&${itemStr}` : itemStr;
      index++;
      parStr += appendStr;
    }
  });

  const res = parStr.length > 1 ? parStr : "";
  return res;
};

export const getSearch = (str) => {
  if (str == undefined) return;
  str = str.substr(1);
  var arr = str.split("&"),
    obj = {},
    newArr = [];
  arr.map(function (value, index, arr) {
    newArr = value.split("=");
    if (newArr[0] != undefined) {
      obj[newArr[0]] = newArr[1];
    }
  });
  return obj;
};

export const getAllUrlParams = () => {
  const historyParams = getCommonParams();
  const hashParams = getParamsFromSearchParams(history.location.search);
  return { ...historyParams, ...hashParams };
}

export const openWindow = (href, param = '_blank') => {
  const { pathname, search } = window.location;
  const url = `${pathname}${search}#${href}`
  window.open(url, param)
}

export function funcUrlDel(loca, name) {
  let baseUrl = '';
  if (loca.indexOf('&') > -1) {
      baseUrl = loca.split('?')[0] + '?';
  } else {
      baseUrl = loca.split('?')[0];
  }
  let query = loca.split('?')[1];
  if (query && query.indexOf(name) > -1) {
      var obj = {};
      var arr = query.split("&");
      for (var i = 0; i < arr.length; i++) {
          arr[i] = arr[i].split("=");
          obj[arr[i][0]] = arr[i][1];
      }
      delete obj[name];
      var url = baseUrl + JSON.stringify(obj).replace(/[\"\{\}]/g, "").replace(/\:/g, "=").replace(/\,/g, "&");
      return url;
  } else {
      return loca;
  }
}

export const getUrlwithoutToken = () => {
  const href = window.location.href;
  return funcUrlDel(href, 'token');
}

// 获取url传参
export const  getQueryString=(name,url)=> {
  let reg = new RegExp('(^|&)' + name + '=([^&]*)(&|$)', 'i');
  var r = decodeURI(window.location.search).substr(1).match(reg);
  if(url){
      r=url.split('?')[1]?.match(reg)
  }
  if (r != null) {
      return unescape(r[2]);
  }
  return null;
}