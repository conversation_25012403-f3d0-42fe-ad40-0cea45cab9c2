// 组装topMenuPathMap 映射表，将路径前缀与顶部菜单 id 关联：
import { match } from 'path-to-regexp';
import { history, generatePath, matchPath } from "umi";
import { ContainerOutlined } from '@ant-design/icons';
import { homeLayoutPagePathStatic, hiddenTopMenusPaths } from "@/constants";

/**
 * 将menus数据转化成antd需要的形式
 * @param {*} menus 接口获取的menus数据
 * @param {*} isNotDealChildren 是否不处理子节点
 * @param {*} isNotNeedIcon 是否不需要icon配置
 * @returns 
 */
export const formatMenus = (menus, isNotDealChildren, isNotNeedIcon) => {
  if(!menus?.length) return []
  return menus.map((menu) =>{
    let newItem = {
      key:menu.id,
      label:menu.name,
      path:menu.path,
      children:undefined
    }
    if(menu?.children?.length && !isNotDealChildren){
      newItem.children = formatMenus(menu.children) ?? undefined
    }
    if(!isNotNeedIcon){
      newItem.icon = <ContainerOutlined />
    }
    return newItem
  })
}

export const filterHiddenTopMenus = (menus) => {
  if(!menus?.length) return []
  return menus.filter((menu) => !hiddenTopMenusPaths.includes(menu.path))
}


export const getTopMenuPathMap = (menuData) => {
  const topMenuMap = {};

  // 遍历每个顶级菜单项
  menuData.forEach(topItem => {
    // 递归收集所有子路径
    const collectPaths = (nodes) => {
      nodes.forEach(node => {
        if (node.path) {
          // 关键点：将子路径绑定到当前顶级菜单的 key
          topMenuMap[node.path] = topItem.id;
        }
        if (node.children) {
          collectPaths(node.children);
        }
      });
    };

    // 从顶级菜单的子节点开始收集
    if (topItem.children && topItem.children.length > 0) {
      collectPaths(topItem.children);
    }

    // 收集顶部菜单path
    if(topItem.path){
      topMenuMap[topItem.path] = topItem.id;
    }
  });

  return topMenuMap;
}

 // 安全路径匹配函数
export function safePathMatch(pathPattern, pathname) {
  try {
      // 分离路径和查询参数
      const [basePathname] = pathname?.split('?') ?? ['/'];
      const [basePattern] = pathPattern?.split('?') ?? ['/'];
      // 使用path-to-regexp匹配路径部分（不含查询参数）
      const matcher = match(basePattern, { decode: decodeURIComponent });
      const matchResult = matcher(basePathname);
      return matchResult;
  } catch (error) {
      // 出错时降级为精确匹配路径部分
      const [basePathname] = pathname?.split('?') ?? ['/'];
      const [basePattern] = pathPattern?.split('?') ?? ['/'];
      return basePattern === basePathname;
  }
}

// 根据路劲匹配顶部菜单的id
export const findTopMenuKey = (pathname, topMenuMap) => {
  let matchedKey = null;
  let maxLength = 0;

  Object.entries(topMenuMap).forEach(([pathPattern, topKey]) => {
    if (safePathMatch(pathPattern, pathname)) {
      // 选择最长路径匹配（更精确）
      if (pathPattern.length > maxLength) {
        maxLength = pathPattern.length;
        matchedKey = topKey;
      }
    }
  });

  return matchedKey;
};

// 扁平化菜单数据，将树形菜单转换为 路径 → 菜单项 和 ID → 父节点 的映射表
export const flattenMenuData = (menuData) => {
  const pathMap = new Map();  // 路径 → 菜单项
  const nodeMap = new Map();  // ID → 节点信息（含 parentId）

  const traverse = (nodes, parentId = null) => {
    nodes.forEach((item) => {
      // 记录路径映射
      pathMap.set(item.rowPath, item);
      // 记录节点父子关系
      nodeMap.set(item.id, { ...item, parentId });
      // 递归处理子节点
      if (item.children) traverse(item.children, item.id);
    });
  };

  traverse(menuData);
  return { pathMap, nodeMap };
};

// 通过当前路径查找对应的菜单项，并回溯父节点链：
export const getMatchedKeys = (pathname, pathMap, nodeMap) => {
  // 1. 尝试匹配静态路径
  let currentMenuItem = pathMap.get(pathname);

  // 2. 处理动态路由（如 `/user/:id`）
  if (!currentMenuItem) {
    for (const [path, item] of pathMap.entries()) {
      if (safePathMatch(path, pathname)) {
        currentMenuItem = item;
        break;
      }
    }
  }

  // 3. 正常匹配逻辑
  if (currentMenuItem) {
    const openKeys = [];
    let currentId = currentMenuItem.id;
    while (currentId) {
      const node = nodeMap.get(currentId);
      if (!node?.parentId) break;
      openKeys.push(node.parentId);
      currentId = node.parentId;
    }
    return {
      selectedKeys: [currentMenuItem.id],
      openKeys: openKeys.reverse() // Ant Design 需要从顶层到子级
    };
  }

  // 4. 未匹配时返回空（由组件处理默认项）
  return { selectedKeys: [], openKeys: [] };
};

// 递归遍历菜单树，找到第一个叶子节点（无子节点的项）或第一个有路径的节点
export const findDefaultMenuItem = (menuData) => {
  if (!menuData?.length) return null;
  const traverse = (nodes) => {
    for (const node of nodes) {
      // 优先返回第一个叶子节点
      if (!node.children || node.children.length === 0) {
        return node;
      }
      // 递归查找子节点
      const childResult = traverse(node.children);
      if (childResult) return childResult;
    }
    return null;
  };

  return traverse(menuData) || menuData[0]; // 兜底返回第一个节点
};

// 判断当前路劲是否匹配
export const pathUrlMatch = (pathname, pathArr) => {
  return pathArr.some((item) => {
    if (safePathMatch(item, pathname)) {
      return true;
    }
  });
}

// 校验是否是合法的path路劲
export function isValidRoutePath(path) {
  // 检查输入是否为非空字符串
  if (typeof path !== 'string' || !path.trim()) {
      return false;
  }
  const trimmedPath = path.trim();
  // 排除外部URL（http/https协议）
  if (/^(https?:)?\/\//.test(trimmedPath)) {
      return false;
  }
  // 排除危险协议（javascript:, data:, vbscript:等）
  if (/^[a-z]+:/.test(trimmedPath) && !trimmedPath.startsWith('/')) {
      return false;
  }
  // 基本路径格式验证
  // 允许：/path, /path/sub, /path/123, /path?query=value, /path#section
  if (!/^\/[a-zA-Z0-9\-_~!$&'()*+,;=:@%\/.??#\[\]]*$/.test(trimmedPath)) {
      return false;
  }
  // 所有检查通过，认为是有效路径
  return true;
}

// 路由跳转，跳转微应用 or iframe
export const jump = (menuItem) => {
  if(!menuItem?.path) return
  if(menuItem?.isIframe === 1){// 走iframe逻辑
    history.push({
      pathname: "/master-common-iframe",
      search: new URLSearchParams({ rowPath: menuItem.path }).toString(),
    });
  }else{// 走微应用处理
    if(isValidRoutePath(menuItem.path)){// 有效路劲才跳转
      history.push(menuItem.path)
      const event = new Event("hashchange");
      window.dispatchEvent(event);
    }else{// 无效地址跳转缺省页
      console.error('当前访问的路径不合法：', `菜单名称：${menuItem.name}；菜单地址：${menuItem.path}`)
      history.push({
        pathname: "/illegalPath",
        search: new URLSearchParams({rowPath: menuItem.path}).toString()
      })
    }
  }
}

/**
 * 格式化菜单数据，给所有菜单数据增加rowPath, rowPath为原始菜单数据
 * @param {Array} menus 菜单数据
 * @returns {Array} 格式化后的菜单数据
 */
export const formatMenuData = (menus) => {
	if(Array.isArray(menus) && menus.length > 0){
		return menus.map((menu) => {
			let newItem = {
				...menu,
				rowPath: menu.path
			}
			if(menu?.children?.length){
				newItem.children = formatMenuData(menu.children) ?? []
			}
			return newItem
		})
	}
	return []
}

/**
 * 通过参数批量生成侧栏菜单数据
 * @param {Array} menus 侧栏菜单数据
 * @param {Object} params 参数
 * @returns {Array} 侧栏菜单数据
 */
export const generateSidebarMenuByParams = (menus, params) => {
 if(Array.isArray(menus) && menus.length > 0 && Object.keys(params).length > 0){
		return menus.map((menu) =>{
			let newItem = {
				...menu,
				path: generatePath(menu.rowPath, params)
			}
			if(menu?.children?.length){
				newItem.children = generateSidebarMenuByParams(menu.children, params) ?? []
			}
			return newItem
		})
 }
 return []
}

/**
 * 根据当前path 匹配对应的路由参数
 * @param {String} path 当前访问路劲
 * @param {Map} pathMap 当前路劲映射表
 * @returns {Object} 路由参数
 */
export const findRouteParamsByPath = (path, pathMap) => {
  let routeParamsInfo = null;
  for (const [key, value] of pathMap.entries()) {
    if (safePathMatch(key, path)) {
      routeParamsInfo = matchPath(value?.rowPath, path);
      break;
    }
  }
  return routeParamsInfo
}

/**
 * 根据当前path 匹配对应的菜单原始path
 * @param {String} path 当前访问路劲
 * @param {Map} pathMap 当前路劲映射表
 * @returns {Object} 路由参数
 */
export const findRowPathByPath = (path, pathMap) => {
  let rowPath = null;
  for (const [key] of pathMap.entries()) {
    if (safePathMatch(key, path)) {
      rowPath = key
      break;
    }
  }
  return rowPath
}


/**
 * 根据配置的ids和当前是不是门户首页来组装顶部动态菜单数据
 * @param {Array} topMenus 总的菜单集合
 * @param {Boolean} isHome 是否是首页
 * @returns 
 */
export const getAsyncTopMenus = (topMenus, isHome) => {
  if(isHome){
    return topMenus.filter(item => homeLayoutPagePathStatic.includes(item.path)) ?? topMenus
  }
  return topMenus.filter(item => homeLayoutPagePathStatic?.indexOf(item.path) === -1) ?? topMenus
}

/**
 * 接收菜单数据和目标一级菜单路径数组，返回匹配的所有子菜单路径：
 * @param {*} menuData 菜单数据
 * @param {*} targetPaths 需要匹配的菜单path集合
 * @returns 返回匹配的所有子菜单路径组成的数组
 */
export const collectChildPaths = (menuData, targetPaths) =>{
  // 递归收集所有后代路径
  const collectDescendantPaths = (nodes) => {
    return nodes.flatMap(node => {
      const currentPath = node.path ? [node.path] : [];
      return node.children 
        ? [...currentPath, ...collectDescendantPaths(node.children)] 
        : currentPath;
    });
  };

  // 查找目标父级并收集所有后代路径
  const allPaths = targetPaths.flatMap(targetPath => {
    const parent = menuData.find(m => m.path === targetPath);
    return parent?.children ? collectDescendantPaths(parent.children) : [];
  });
  return [...new Set(allPaths)]; // 最终去重
}

/**
 * 获取URL中的搜索参数
 * @param {string} url - 要解析的URL，可以是完整URL或路径部分
 * @returns {Object} 包含所有搜索参数键值对的对象
 */
export function getSearchParams(url) {
  // 处理没有协议和域名的URL（如 /a/b?c=1）
  let fullUrl = url;
  if (!url.startsWith('http://') && !url.startsWith('https://') && !url.startsWith('//')) {
      // 创建一个临时URL用于解析
      fullUrl = 'https://example.com' + (url.startsWith('/') ? url : '/' + url);
  }
  
  try {
      // 创建URL对象
      const urlObj = new URL(fullUrl);
      
      // 获取URLSearchParams对象
      const searchParams = new URLSearchParams(urlObj.search);
      
      // 将参数转换为普通对象
      const params = {};
      for (const [key, value] of searchParams) {
          // 如果参数已经存在，转换为数组存储多个值
          if (params.hasOwnProperty(key)) {
              if (Array.isArray(params[key])) {
                  params[key].push(value);
              } else {
                  params[key] = [params[key], value];
              }
          } else {
              params[key] = value;
          }
      }
      
      return params;
  } catch (error) {
      console.error('解析URL参数时出错:', error);
      return {};
  }
}

/**
* 获取特定搜索参数的值
* @param {string} name - 参数名
* @param {string} url - 要解析的URL，可以是完整URL或路径部分
* @returns {string|string[]|null} 参数值，如果不存在返回null
*/
export function getSearchParam(name, url) {
  const params = getSearchParams(url);
  return params.hasOwnProperty(name) ? params[name] : null;
}
