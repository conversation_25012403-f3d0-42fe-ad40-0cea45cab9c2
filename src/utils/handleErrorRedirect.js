
import { history } from "umi";

export default {
  //404
  on404: () => {
    history.push("/404");
  },
  // 无网络连接，服务器未正确响应
  onNoNetwork: () => {
    if(history.location.pathname.indexOf("/noNetwork") > -1) return
    history.push({
      pathname: '/noNetwork',
      search: `?redirectUrl=${history.location.pathname}`
    });
  },
  // 没有权限访问某个页面
  onNoAuth: () => {
    history.push("/noAuth");
  }
} 