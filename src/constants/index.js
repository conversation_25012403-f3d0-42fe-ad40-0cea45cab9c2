// 不需要登录就可以访问的页面
export const noAuthPage = [];

// 当遇到重定向情况时，默认跳转的菜单路径
export const homePath = "/homePage";

// 需要渲染在门户首页的所有页面的path(1、需要渲染门户首页的静态菜单或内页需要配置；2、需要渲染门户首页的动态菜单需要配置，动态菜单只需要配置一级菜单path，所有的子级菜单path都会生效)
export const homeLayoutPagePathStatic = [
  "/homePage",
  "/myWorkbench",
  "/myWorkbench/set",
  "/productService",
  "/passService",
  "/apiMarket",
  "/apiManage",
  "/apiManage/subscrip",
  "/apiManage/subscripLog",
  "/apiManage/apiDebugger/:pid",
  "/apiManage/subscripLogDetail/:eid",
  "/passService/detail/:type/:id/:chartName",
  "/passService/orderCreate/:type",//对象存储申请
  "/passService/passAuthServerDetail/:type/:id/:chartName",
  "/passService/dataDesensitization/:type/:id/:chartName",
  "/passService/detail/apply/:type/:id/:chartName",
  "/passService/componentDetail/:type/:id",
  "/kepler-webpage/svc/integrationService/applicationIntegration", // 应用系统
  "/system/systemManage/parent-management", // 应用程序
  "/kepler-webpage/svc/integrationService/applicationIntegration/create", // 创建应用系统
  "/kepler-webpage/svc/integrationService/applicationIntegration/sysApply", // 同步应用系统信
  "/kepler-webpage/svc/integrationService/applicationIntegration/subsystem/:id/create",// 应用子系统创建
  "/kepler-webpage/svc/integrationService/applicationIntegration/subsystem/:id/edit/:subApplicationId",// 应用子系统编辑
  "/kepler-webpage/svc/integrationService/applicationIntegration/sysApply/history/:id", // 历史同步记录
  "/kepler-webpage/svc/integrationService/applicationIntegration/app/:id/createApp", // 不通过模板创建应用程序
  "/kepler-webpage/svc/integrationService/applicationIntegration/app/:id/createAppbyTempalte", // 通过模板创建应用程序
  "/kepler-webpage/svc/integrationService/applicationIntegration/app/:id/createAppbyTempalte/springboot", // 通过脚手架建应用程序
  "/kepler-webpage/svc/integrationService/applicationIntegration/config-app/:id/createApp", // 系统设置-不通过模板创建应用程序
  "/kepler-webpage/svc/integrationService/applicationIntegration/config-app/:id/createAppbyTempalte", // 系统设置-通过模板创建应用程序
  "/kepler-webpage/svc/integrationService/applicationIntegration/config/:id/developer", // 分配开发人员
  "/kepler-webpage/svc/integrationService/applicationIntegration/config/:id/:confType/:confParams", // 中间件申请
  "/kepler-webpage/svc/integrationService/applicationIntegration/paas/:id/detail",
  "/kepler-webpage/svc/integrationService/applicationIntegration/paasStatistic/:id/detail",
  "/efficiency/collaborationCodeManage/:systemId/code", // 代码仓库-代码
  "/efficiency/collaborationCodeManage/:systemId/branch", // 代码仓库-分支管理
  "/efficiency/collaborationCodeManage/:systemId/merge", // 代码仓库-合并请求
  "/efficiency/collaborationCodeManage/:systemId/merge/detail",
  "/efficiency/collaborationCodeManage/:systemId/merge/create",
  "/efficiency/collaborationCodeManage/:systemId/version", // 代码仓库-版本管理
  "/efficiency/collaborationCodeManage/:systemId/commit", // 代码仓库-代码提交
  "/efficiency/collaborationCodeManage/:systemId/graph", // 代码仓库-代码图谱
  "/efficiency/collaborationCodeManage/:systemId/compare", // 代码仓库-代码对比
  "/dochub/docs", // 技术文档库
  "/dochub/docs/search", // 技术文档库搜索
  "/dochub/docs/searchByAI", // 技术文档库AI搜索
  "/dochub/docs/detail", // 技术文档库全部文档
  "/dochub/docs/detail/:id", // 技术文档库文档详情
  "/passService/MessageComponentDetail/:id",
  // "/kepler-webpage/svc/procService/procCenter", // 审批中心
  // "/kepler-webpage/svc/procService/verproc", // 我的审批
  // "/kepler-webpage/svc/procService/verproc/:insId/:taskId", // 我的审批-详情
  // "/kepler-webpage/svc/procService/procMgmt", // 我的提交
  // "/kepler-webpage/svc/procService/procMgmt/:insId", // 我的提交-详情
  "/apiMarket/detail/:id",
  "/platformOperation",
  "/platformOperation/platformBoard",
  // "/openSourceComponent",
  "/apim/ipaassub/mysub", // api管理-我的订阅组
  "/apim/ipaaspub/mypubproduct", // api管理-我的产品
  "/apim/ipaaspub/mypub", // api管理-我的发布组
  "/apim/ipaas/org", // api管理-订阅组/发布组管理
  "/apim/ipaassub/mysuborder", // api管理-我的订阅
  "/holidayDetail", // 节假日组件详情
  "/fulltextSearchDetail",// 全文检索组件详情
];

// 需要根据url动态更新侧栏菜单的顶部菜单集合，如应用系统、应用程序两个顶部菜单的侧栏菜单数据是动态的
export const topMenusOfDynamicSideBarMenus = [
  "/kepler-webpage/svc/integrationService/applicationIntegration",
  "/system/systemManage/parent-management",
];

// 需要展示切换应用程序和切换应用系统组件到侧边栏的顶部菜单path集合（这两个毒瘤只能做私有化处理了o(╥﹏╥)o），如应用系统、应用程序的菜单
export const appSystemAndProgramTopMenus = [
  "/kepler-webpage/svc/integrationService/applicationIntegration",
  "/system/systemManage/parent-management",
];

// 需要隐藏不显示的顶部菜单的path集合
export const hiddenTopMenusPaths = ["/system/systemManage/parent-management"];
