// 公司海创园开发环境
window._th_appInfo = {
  _appId: "1897107602523611138",
  _middleware: {
    // clusterId: 'default--test'
    project: {
      "organId": "17da50d2422242b4",
      "projectId": "15321cf3f00a4e5c",
      "organName":'数字化产品研发部',
      "projectName":'应用服务平台'
    },
    clusterId: "",
    namespace: ""
  },
  // 智能助手相关配置
  _RAG: {
    // 智能助手的js资源地址，ip端口由nginx转发
    url: "http://***********/rag-api/applications/embed/js?protocol=http&host=***********&token=UBJQssCEjS7VgHc0dRFDQaW7xCE6dsN-Yk-rpXKJsfA",
    // 哪些应用启动智能助手，配置微应用的name
    apps:['dochub'],
    // rag应用的id
    appId: "3d68e6d2-16ef-4e8c-81e6-52d642c18e99",
    // rag应用的token
    token: "KJTdwK4u2W9AmMfasTdYtISVY61gOrfwy6W-PLb2zOY",
  },
  // 判断是否在海创园环境
  isHcy: true
}



