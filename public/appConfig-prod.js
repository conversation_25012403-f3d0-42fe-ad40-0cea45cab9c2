// 天合生产环境
window._th_appInfo = {
  _appId: "1",
  _middleware: {
    // 指定组织、项目、集群、命名空间
    defaultEnv: "prod",
    clusterId: "default--ne-prod",
    envMap: {
      // 环境配置 dev，test，uat，prod
      prod: {
        project: {
          organId: "dc9320e4e6674365",
          projectId: "9efaa7b0964c4931",
          organName: "数字化产品研发部",
          projectName: "应用服务平台中间件",
        },
      },
      test: {
        project: {
          organId: "dc9320e4e6674365",
          projectId: "0a69fafede674ee8",
          organName: "数字化产品研发部",
          projectName: "TASP中间件-TEST",
        },

        // 指定默认集群和命名空间
        // namespace: "tasp-test",
        // // 设置默认集群后，不可更换
        // clusterSeletable: false,
      },
    },
    _middlewareInfo: {
      defaultEnv: "test",
      envMap: {
        // 环境配置 dev，test，uat，prod
        prod: {
          middlewareIpPort: "http://***********:31088/#",
        },
        dev: {
          middlewareIpPort: "http://***********:31088/#",
        },
        test: {
          middlewareIpPort: "http://***********:31088/#",
        },
      },
    },
  },
  // 智能助手相关配置
  _RAG: {
    // 智能助手的js资源地址，ip端口由nginx转发
    url: "https://tasp.trinasolar.com/rag-api/applications/embed/js?protocol=https&host=tasp.trinasolar.com&token=GkHvnC6bPU6ffqxarruED5fZUn6eXj1fGzQuCvuxZSs",
    // 哪些应用启动智能助手，配置微应用的name
    apps:['dochub'],
    // rag应用的id
    appId: "0e9bda48-43ce-428d-9047-6988b93abae2",
    // rag应用的token
    token: "034fpYZCqFfJjAUTied6Au43TfcO9ULZ7FZfHgg_QQQ",
  }
};


