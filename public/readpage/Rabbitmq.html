<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RabbitMQ 使用与运维手册</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        .section-title {
            border-left: 4px solid #f97316;
            padding-left: 12px;
            margin: 24px 0 16px 0;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 16px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 4px 0;
            position: relative;
            padding-left: 20px;
        }
        .feature-list li:before {
            content: "•";
            color: #f97316;
            font-weight: bold;
            position: absolute;
            left: 0;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div class="mx-auto rounded-lg shadow-sm p-8">
        <!-- 标题 -->
        <h1 class="text-2xl font-bold text-gray-800 mb-6">
            🐰 RabbitMQ 使用与运维手册 (README)
        </h1>

        <!-- 一、RabbitMQ 简介 -->
        <div class="section-title">
            <h2 class="text-lg font-semibold text-gray-700">📖 一、RabbitMQ 简介</h2>
        </div>
        <p class="text-gray-600 mb-4">
            RabbitMQ 是一个开源的消息代理和队列服务器，用于通过普通协议在完全不同的应用之间共享数据。RabbitMQ是使用Erlang语言来编写的，并且RabbitMQ是基于AMQP协议的。
        </p>
        <ul class="feature-list text-gray-600 mb-6">
            <li>可靠性，RabbitMQ使用一些机制来保证可靠性，如持久化、传输确认、发布确认</li>
            <li>灵活的路由，在消息进入队列之前，通过Exchange来路由消息</li>
            <li>消息集群，多个RabbitMQ服务器可以组成一个集群，形成一个逻辑Broker</li>
            <li>高可用，队列可以在集群中的机器上进行镜像，使得在部分节点出问题的情况下队列仍然可用</li>
            <li>多种协议，RabbitMQ支持多种消息队列协议，比如STOMP、MQTT等</li>
            <li>多语言客户端，RabbitMQ几乎支持所有常用语言，比如Java、.NET、Ruby等</li>
            <li>管理界面，RabbitMQ提供了一个易用的用户界面，使得用户可以监控和管理消息Broker的许多方面</li>
        </ul>

        <!-- 二、基本概念 -->
        <div class="section-title">
            <h2 class="text-lg font-semibold text-gray-700">⚙️ 二、基本概念</h2>
        </div>
        <table>
            <thead>
                <tr>
                    <th>概念</th>
                    <th>说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Connection</td>
                    <td>网络连接，比如一个TCP连接。应用程序与Broker的网络连接</td>
                </tr>
                <tr>
                    <td>Channel</td>
                    <td>信道，多路复用连接中的一条独立的双向数据流通道。信道是建立在真实的TCP连接内的虚拟连接</td>
                </tr>
                <tr>
                    <td>Exchange</td>
                    <td>消息交换机，它指定消息按什么规则，路由到哪个队列。Exchange负责接收生产者发送的消息，并根据路由规则将消息路由给服务器中的队列</td>
                </tr>
                <tr>
                    <td>Queue</td>
                    <td>消息队列载体，每个消息都会被投入到一个或多个队列。Queue是消息的容器，也是消息的终点</td>
                </tr>
                <tr>
                    <td>Binding</td>
                    <td>绑定，用于消息队列和交换器之间的关联。一个绑定就是基于路由键将交换器和消息队列连接起来的路由规则</td>
                </tr>
                <tr>
                    <td>Routing Key</td>
                    <td>路由键，RabbitMQ决定消息该投递到哪个队列的规则。队列通过路由键绑定到交换器</td>
                </tr>
                <tr>
                    <td>Producer</td>
                    <td>消息生产者，就是投递消息的程序</td>
                </tr>
                <tr>
                    <td>Consumer</td>
                    <td>消息消费者，就是接受消息的程序</td>
                </tr>
                <tr>
                    <td>Virtual Host</td>
                    <td>虚拟主机，表示一批交换器、消息队列和相关对象。虚拟主机是共享相同的身份认证和加密环境的独立服务器域</td>
                </tr>
            </tbody>
        </table>

        <!-- 三、核心配置文件 -->
        <div class="section-title">
            <h2 class="text-lg font-semibold text-gray-700">📄 三、核心配置文件</h2>
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">1. RabbitMQ 配置文件（<code>rabbitmq.conf</code> - 通常无需修改）</h3>
        <div class="code-block">
            <!-- 添加换行格式化配置文件内容 -->
            # 监听端口<br>
            listeners.tcp.default = 5672<br><br>
            
            # 管理界面端口<br>
            management.tcp.port = 15672<br><br>
            
            # 日志级别<br>
            log.console.level = info<br><br>
            
            # 磁盘空间检查间隔<br>
            disk_free_limit.absolute = 2GB
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">2. 环境变量配置</h3>
        <div class="code-block">
            <!-- 添加换行格式化环境变量配置 -->
            # 设置节点名称<br>
            RABBITMQ_NODENAME=rabbit@localhost<br><br>
            
            # 设置数据目录<br>
            RABBITMQ_MNESIA_BASE=/var/lib/rabbitmq/mnesia<br><br>
            
            # 设置日志目录<br>
            RABBITMQ_LOG_BASE=/var/log/rabbitmq<br><br>
            
            # 启用管理插件<br>
            RABBITMQ_ENABLED_PLUGINS_FILE=/etc/rabbitmq/enabled_plugins
        </div>

        <!-- 四、日常运维命令 -->
        <div class="section-title">
            <h2 class="text-lg font-semibold text-gray-700">🔧 四、日常运维命令（使用 <code>rabbitmqctl</code>）</h2>
        </div>
        <p class="text-gray-600 mb-4">
            <code>rabbitmqctl</code> 是 RabbitMQ 的管理工具，位于 <code>sbin/</code> 目录下。使用前请确保已配置好环境变量 <code>RABBITMQ_HOME</code> 或者进入 <code>sbin</code> 目录。
        </p>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">1. 服务管理（使用 <code>rabbitmq-server</code> 和 <code>rabbitmqctl</code> 脚本）</h3>
        <div class="code-block">
            <!-- 添加换行格式化服务管理命令 -->
            # 启动 RabbitMQ 服务器<br>
            rabbitmq-server<br><br>
            
            # 后台启动 RabbitMQ 服务器<br>
            rabbitmq-server -detached<br><br>
            
            # 停止 RabbitMQ 服务器<br>
            rabbitmqctl stop<br><br>
            
            # 查看服务器状态<br>
            rabbitmqctl status
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">2. 用户管理</h3>
        <div class="code-block">
            <!-- 添加换行格式化用户管理命令 -->
            # 添加用户<br>
            rabbitmqctl add_user username password<br><br>
            
            # 删除用户<br>
            rabbitmqctl delete_user username<br><br>
            
            # 修改用户密码<br>
            rabbitmqctl change_password username newpassword<br><br>
            
            # 设置用户角色<br>
            rabbitmqctl set_user_tags username administrator<br><br>
            
            # 列出所有用户<br>
            rabbitmqctl list_users
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">3. 虚拟主机管理</h3>
        <div class="code-block">
            <!-- 添加换行格式化虚拟主机管理命令 -->
            # 创建虚拟主机<br>
            rabbitmqctl add_vhost vhost_name<br><br>
            
            # 删除虚拟主机<br>
            rabbitmqctl delete_vhost vhost_name<br><br>
            
            # 列出所有虚拟主机<br>
            rabbitmqctl list_vhosts<br><br>
            
            # 设置用户权限<br>
            rabbitmqctl set_permissions -p vhost_name username ".*" ".*" ".*"
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">4. 队列和交换器管理</h3>
        <div class="code-block">
            <!-- 添加换行格式化队列和交换器管理命令 -->
            # 列出所有队列<br>
            rabbitmqctl list_queues<br><br>
            
            # 列出所有交换器<br>
            rabbitmqctl list_exchanges<br><br>
            
            # 列出所有绑定<br>
            rabbitmqctl list_bindings<br><br>
            
            # 清空队列<br>
            rabbitmqctl purge_queue queue_name
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">5. 插件管理</h3>
        <div class="code-block">
            <!-- 添加换行格式化插件管理命令 -->
            # 启用管理插件<br>
            rabbitmq-plugins enable rabbitmq_management<br><br>
            
            # 禁用插件<br>
            rabbitmq-plugins disable plugin_name<br><br>
            
            # 列出所有插件<br>
            rabbitmq-plugins list
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">6. 监控命令</h3>
        <div class="code-block">
            <!-- 添加换行格式化监控命令 -->
            # 查看连接信息<br>
            rabbitmqctl list_connections<br><br>
            
            # 查看信道信息<br>
            rabbitmqctl list_channels<br><br>
            
            # 查看消费者信息<br>
            rabbitmqctl list_consumers<br><br>
            
            # 实时监控队列<br>
            rabbitmqctl list_queues name messages_ready messages_unacknowledged
        </div>

        <!-- 五、快速开始示例 -->
        <div class="section-title">
            <h2 class="text-lg font-semibold text-gray-700">🚀 五、快速开始示例</h2>
        </div>

        <h3 class="font-semibold text-gray-700 mt-4 mb-2">Python 示例（需要安装 pika 库）</h3>
        <div class="code-block">
            <!-- 添加换行格式化Python示例代码 -->
            # 安装依赖<br>
            pip install pika<br><br>
            
            # 生产者示例<br>
            import pika<br><br>
            
            connection = pika.BlockingConnection(pika.ConnectionParameters('localhost'))<br>
            channel = connection.channel()<br><br>
            
            channel.queue_declare(queue='hello')<br><br>
            
            channel.basic_publish(exchange='',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;routing_key='hello',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;body='Hello World!')<br>
            print(" [x] Sent 'Hello World!'")<br>
            connection.close()<br><br>
            
            # 消费者示例<br>
            import pika<br><br>
            
            def callback(ch, method, properties, body):<br>
            &nbsp;&nbsp;&nbsp;&nbsp;print(" [x] Received %r" % body)<br><br>
            
            connection = pika.BlockingConnection(pika.ConnectionParameters('localhost'))<br>
            channel = connection.channel()<br><br>
            
            channel.queue_declare(queue='hello')<br>
            channel.basic_consume(queue='hello',<br>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;auto_ack=True,<br>
            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on_message_callback=callback)<br><br>
            
            print(' [*] Waiting for messages. To exit press CTRL+C')<br>
            channel.start_consuming()
        </div>

        <div class="mt-8 p-4 bg-blue-50 border-l-4 border-blue-400 rounded">
            <p class="text-sm text-blue-700">
                <strong>提示：</strong> 更多详细信息请参考 RabbitMQ 官方文档：<a href="https://www.rabbitmq.com/documentation.html" class="text-blue-600 underline">https://www.rabbitmq.com/documentation.html</a>
            </p>
        </div>
    </div>
</body>
</html>
