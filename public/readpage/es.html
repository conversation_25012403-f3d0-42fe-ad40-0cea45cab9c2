<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Elasticsearch 使用与运维手册</title>
  <style>
    body {
      font-family: "Microsoft YaHei", sans-serif;
      background-color: #f9f9f9;
      padding: 40px;
      color: #333;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    pre, code {
      background-color: #eee;
      padding: 4px 6px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      padding: 10px;
      overflow-x: auto;
    }
    ul {
      margin-top: 0.5em;
      padding-left: 1.5em;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 10px;
    }
    table, th, td {
      border: 1px solid #ccc;
      padding: 8px;
      text-align: left;
    }
    a {
      color: #2980b9;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>Elasticsearch 使用与运维手册</h1>

  <h2>📖 一、Elasticsearch 简介</h2>
  <p>Elasticsearch 是一个基于 Lucene 的分布式搜索和分析引擎，广泛用于全文检索、日志分析、安全监控、业务分析等场景。</p>
  <ul>
    <li>分布式架构，高可用性与可扩展性</li>
    <li>JSON 格式存储数据，RESTful API 操作</li>
    <li>支持实时全文检索、聚合分析</li>
    <li>与 Beats、Logstash、Kibana 协同构成 ELK/Elastic Stack</li>
  </ul>

  <h2>⚙️ 二、Elasticsearch 基本组成</h2>
  <table>
    <thead>
      <tr><th>组件</th><th>说明</th></tr>
    </thead>
    <tbody>
      <tr><td>Node</td><td>单个 ES 实例</td></tr>
      <tr><td>Cluster</td><td>多个节点组成的集群</td></tr>
      <tr><td>Index</td><td>数据库（逻辑上的）</td></tr>
      <tr><td>Document</td><td>实际数据记录</td></tr>
      <tr><td>Shard / Replica</td><td>主分片 / 副本分片</td></tr>
    </tbody>
  </table>

  <h2>📁 三、常用配置</h2>
  <pre><code>cluster.name: my-es-cluster
node.name: node-1
network.host: 0.0.0.0
http.port: 9200
discovery.seed_hosts: ["127.0.0.1"]
cluster.initial_master_nodes: ["node-1"]
xpack.security.enabled: true</code></pre>

  <h2>🧰 四、日常运维命令</h2>
  <h3>1. 启动/停止服务</h3>
  <pre><code>systemctl start elasticsearch
systemctl stop elasticsearch
systemctl restart elasticsearch
systemctl status elasticsearch</code></pre>

  <h3>2. 基础集群操作</h3>
  <pre><code>curl -X GET 'http://localhost:9200/_cluster/health?pretty'
curl -X GET 'http://localhost:9200/_cat/nodes?v'
curl -X GET 'http://localhost:9200/_cat/indices?v'
curl -X PUT 'http://localhost:9200/my-index'
curl -X DELETE 'http://localhost:9200/my-index'
curl -X GET 'http://localhost:9200/_cluster/state?pretty'</code></pre>

  <h3>3. 数据操作</h3>
  <pre><code>curl -X POST 'localhost:9200/my-index/_doc/1' -H 'Content-Type: application/json' -d'{
  "name": "张三",
  "age": 30,
  "created": "2024-01-01"
}'

curl -X GET 'localhost:9200/my-index/_doc/1?pretty'

curl -X GET 'localhost:9200/my-index/_search' -H 'Content-Type: application/json' -d'{
  "query": {
    "match": {
      "name": "张三"
    }
  }
}'</code></pre>

  <h3>4. 快照与恢复</h3>
  <pre><code>curl -X PUT 'localhost:9200/_snapshot/my_backup' -H 'Content-Type: application/json' -d'{
  "type": "fs",
  "settings": {
    "location": "/mnt/es_backup",
    "compress": true
  }
}'

curl -X PUT 'localhost:9200/_snapshot/my_backup/snapshot_1'

curl -X POST 'localhost:9200/_snapshot/my_backup/snapshot_1/_restore'</code></pre>

  <h3>5. 安全管理</h3>
  <pre><code>bin/elasticsearch-users useradd admin -p password -r superuser
bin/elasticsearch-users passwd admin</code></pre>

  <h2>🛠️ 五、排障与性能分析</h2>
  <pre><code>tail -f /var/log/elasticsearch/elasticsearch.log
curl -X GET 'localhost:9200/_nodes/stats/jvm?pretty'
curl -X GET 'localhost:9200/_nodes/stats/process?pretty'</code></pre>

  <h2>🔐 六、优化建议</h2>
  <ul>
    <li>合理设置分片与副本数</li>
    <li>使用 bulk 批量写入</li>
    <li>配置慢查询日志</li>
    <li>使用 ILM 管理索引生命周期</li>
    <li>使用 Kibana 和监控工具观测性能</li>
  </ul>

  <h2>📚 七、参考资料</h2>
  <p>
    👉 <a href="https://www.elastic.co/guide/en/elasticsearch/reference/index.html" target="_blank">Elasticsearch 官方文档</a><br />
    👉 <a href="https://www.elastic.co/guide/en/elasticsearch/reference/current/security-settings.html" target="_blank">安全配置指南</a>
  </p>
</body>
</html>