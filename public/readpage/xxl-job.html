<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XXL-Job 使用与运维手册</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans CJK SC", "Microsoft YaHei", sans-serif;
            line-height: 1.7;
            color: #2c3e50;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: #ffffff;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            animation: fadeInUp 0.8s ease-out;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translate(-50%, -50%) rotate(0deg); }
            100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

        .content {
            padding: 40px;
            position: relative;
        }

        h1 {
            font-size: 3em;
            font-weight: 700;
            margin-bottom: 20px;
            position: relative;
            z-index: 1;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            font-weight: 300;
            position: relative;
            z-index: 1;
        }

        h2 {
            font-size: 1.8em;
            color: #2c3e50;
            margin: 40px 0 20px 0;
            padding: 15px 0 15px 20px;
            border-left: 5px solid #667eea;
            background: linear-gradient(90deg, rgba(102, 126, 234, 0.1) 0%, transparent 100%);
            border-radius: 0 10px 10px 0;
            position: relative;
            font-weight: 600;
        }

        h2::before {
            content: '';
            position: absolute;
            left: -5px;
            top: 0;
            bottom: 0;
            width: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 3px;
        }

        h3 {
            font-size: 1.4em;
            color: #34495e;
            margin: 30px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
            font-weight: 600;
            position: relative;
        }

        h3::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 50px;
            height: 2px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 1px;
        }

        p {
            margin-bottom: 16px;
            text-align: justify;
            font-size: 1.05em;
        }

        .notice {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
            position: relative;
            overflow: hidden;
        }

        .notice::before {
            content: '⚠️';
            font-size: 1.5em;
            margin-right: 10px;
        }

        .notice p {
            margin-bottom: 8px;
            font-weight: 500;
        }

        .link-button {
            display: inline-block;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 25px;
            margin: 20px 0;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .link-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
            text-decoration: none;
            color: white;
        }

        code {
            font-family: "JetBrains Mono", "Fira Code", "SFMono-Regular", Consolas, "Liberation Mono", Menlo, Courier, monospace;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 4px 8px;
            margin: 0 2px;
            font-size: 0.9em;
            border-radius: 6px;
            color: #e83e8c;
            font-weight: 600;
            border: 1px solid #dee2e6;
        }

        pre {
            background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
            color: #e2e8f0;
            padding: 24px;
            overflow-x: auto;
            line-height: 1.6;
            border-radius: 12px;
            margin: 20px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            position: relative;
            border: 1px solid #4a5568;
        }

        pre::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            border-radius: 12px 12px 0 0;
        }

        pre code {
            padding: 0;
            margin: 0;
            font-size: 0.95em;
            background: transparent;
            color: #e2e8f0;
            border: none;
            font-weight: 400;
        }

        ul {
            padding-left: 0;
            list-style: none;
        }

        li {
            margin-bottom: 12px;
            padding: 12px 0 12px 40px;
            position: relative;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 8px;
            margin-bottom: 8px;
            transition: all 0.3s ease;
        }

        li:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(5px);
        }

        li::before {
            content: '✨';
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 1.2em;
        }

        li > b {
            color: #667eea;
            font-weight: 700;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }

        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 1.1em;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            body {
                padding: 10px;
            }

            .header {
                padding: 40px 20px;
            }

            .content {
                padding: 20px;
            }

            h1 {
                font-size: 2.2em;
            }

            h2 {
                font-size: 1.5em;
            }

            pre {
                padding: 16px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>XXL-Job 使用与运维手册</h1>
            <div class="subtitle">轻量级分布式任务调度平台完整指南</div>
        </div>

        <div class="content">
            <div class="notice">
                <p><strong>申请说明：</strong></p>
                <p>① XXL-JOB 需要自己准备一个空的数据库，并提前执行初始化脚本。</p>
                <p>② 平台支持单实例和集群模式，默认为单实例，如需集群，则设置副本数据为3。</p>
                <p>③ AccessToken是用于调度中心与执行器之间进行安全校验的重要凭证，默认值是default_token，请根据实际要求申请时自行修改。</p>
            </div>

            <a href="https://github.com/xuxueli/xxl-job/blob/3.1.1-release/doc/db/tables_xxl_job.sql"
               target="_blank" class="link-button">📄 获取 XXL-JOB 初始化脚本</a>

<h2>一、XXL-Job 简介</h2>

            <p>XXL-JOB 是一个轻量级分布式任务调度平台，其核心设计目标是开发迅速、学习简单、轻量级、易扩展。现已开放源代码并被广泛应用于多家公司的生产环境。</p>

            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🚀 简单易用</h4>
                    <p>支持通过Web页面对任务进行CRUD操作，操作简单，一分钟上手。</p>
                </div>
                <div class="feature-card">
                    <h4>⚡ 动态管理</h4>
                    <p>支持动态修改任务状态、启动/停止任务，以及终止运行中任务，即时生效。</p>
                </div>
                <div class="feature-card">
                    <h4>🛡️ 高可用</h4>
                    <p>调度中心和执行器均支持集群部署，保证了调度和任务执行的高可用性。</p>
                </div>
                <div class="feature-card">
                    <h4>📈 弹性扩缩容</h4>
                    <p>当有新的执行器实例上线或下线时，调度中心下次调度时会自动感知并重新分配任务。</p>
                </div>
                <div class="feature-card">
                    <h4>🎯 丰富的策略</h4>
                    <p>提供多种任务触发、阻塞处理和调度过期策略。</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 任务失败重试</h4>
                    <p>支持自定义任务失败时的重试次数。</p>
                </div>
            </div>

<h2>二、XXL-Job 配置文件示例</h2>
            <p>XXL-Job 分为“调度中心” (<code>xxl-job-admin</code>) 和“执行器” (<code>xxl-job-executor</code>) 两部分。以下是它们的核心配置 (通常在 <code>application.properties</code> 文件中)。</p>

            <h3>1. 调度中心 (xxl-job-admin) 配置</h3>
<pre><code>### web port
server.port=8080

### a database connection
spring.datasource.url=jdbc:mysql://127.0.0.1:3306/xxl_job?useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&serverTimezone=Asia/Shanghai
spring.datasource.username=root
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

### job log path
xxl.job.logpath=/data/applogs/xxl-job/jobhandler

### access token
xxl.job.accessToken=default_token

### login account
xxl.job.login.username=admin
xxl.job.login.password=123456
</code></pre>

            <h3>2. 执行器 (xxl-job-executor) 配置</h3>
<pre><code>### web port
server.port=8081

### log path
logging.config=classpath:logback.xml
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler

### xxl-job admin address list, such as "http://address1:port/xxl-job-admin,http://address2:port/xxl-job-admin"
xxl.job.admin.addresses=http://127.0.0.1:8080/xxl-job-admin

### xxl-job, access token
xxl.job.accessToken=default_token

### xxl-job executor app name
xxl.job.executor.appname=xxl-job-executor-sample
### xxl-job executor port, default is 9999
xxl.job.executor.port=9999
</code></pre>

            <h2>三、日常运维</h2>
            <h3>1. 启动与停止服务</h3>
            <p>XXL-Job 通常以后台进程方式运行 Java 应用。</p>
            <p><b>启动 (调度中心或执行器):</b></p>
<pre><code>nohup java -jar xxl-job-admin-&lt;version&gt;.jar > xxl-job-admin.log 2>&1 &
nohup java -jar xxl-job-executor-sample-&lt;version&gt;.jar > xxl-job-executor.log 2>&1 &
</code></pre>

            <p><b>停止:</b></p>
            <pre><code># 找到 XXL-Job 进程的 PID
ps -ef | grep xxl-job

# 优雅地关闭进程
kill -15 [PID]
</code></pre>

            <p><b>查看状态:</b></p>
            <pre><code># 查看进程是否存活
ps -ef | grep xxl-job

# 查看实时日志
tail -f /data/applogs/xxl-job/jobhandler/xxl-job-admin.log
tail -f /data/applogs/xxl-job/jobhandler/xxl-job-executor-sample.log
</code></pre>

            <h3>2. Web 界面核心操作</h3>
            <p>日常的任务管理和监控主要通过调度中心 Web 界面完成。访问地址：<code>http://&lt;调度中心IP&gt;:8080/xxl-job-admin</code></p>

            <ul>
                <li><b>执行器管理:</b> 在此页面注册和管理执行器。执行器项目启动后会自动注册到调度中心，也可手动录入。</li>
                <li><b>任务管理:</b> 核心功能页面，用于创建、启动、停止、编辑和手动触发任务。</li>
                <li><b>调度日志:</b> 查看任务的调度记录，包括每次调度的状态、执行结果和耗时等。</li>
                <li><b>执行日志:</b> 点击调度日志中的“查看执行日志”按钮，可以实时查看任务在执行器端输出的详细日志。</li>
                <li><b>用户管理:</b> 管理登录调度中心的账号和权限。</li>
            </ul>

            <h2>四、推荐运维实践</h2>
            <ul>
                <li><b>数据库独立部署:</b> 为调度中心使用独立的、高可用的数据库实例（如 MySQL HA），并定期备份。</li>
                <li><b>调度中心集群部署:</b> 部署多个调度中心实例并使用 Nginx 等负载均衡器进行代理，实现调度中心的高可用。</li>
                <li><b>执行器集群部署:</b> 对核心业务的执行器进行集群部署，避免单点故障。在任务配置中，路由策略选择“分片广播”或合适的负载均衡策略。</li>
                <li><b>访问令牌 (AccessToken):</b> 在调度中心和执行器中配置相同的、复杂的 <code>accessToken</code>，增强通信安全。</li>
                <li><b>日志与监控:</b> 监控调度中心和执行器的 JVM 状态、日志输出，并配置告警，以便及时发现问题。</li>
                <li><b>合理配置任务:</b> 根据业务需求合理设置任务的 “Cron表达式”、“阻塞处理策略” 和 “失败重试次数”，避免任务堆积或雪崩。</li>
            </ul>

        </div>
    </div>
</body>
</html>