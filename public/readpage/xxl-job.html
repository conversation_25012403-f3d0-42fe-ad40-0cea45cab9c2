<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>XXL-Job 使用与运维手册</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 40px;
            background: #f8f9fa;
        }

        h1, h2, h3 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: .3em;
        }

        h1 {
            font-size: 2em;
            color: #2c3e50; /* 深蓝灰色标题 */
        }

        h2 {
            font-size: 1.5em;
            color: #2c3e50; /* 深蓝灰色标题 */
            margin-top: 2em; /* 增加章节间距 */
        }

        h3 {
            font-size: 1.25em;
            color: #34495e; /* 较浅的蓝灰色 */
        }

        code {
            font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
            background-color: #f6f8fa;
            padding: .2em .4em;
            margin: 0;
            font-size: 85%;
            border-radius: 3px;
            color: #d73a49; /* 暗红色行内代码 */
        }

        pre {
            background-color: #f6f8fa;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
            border-radius: 3px;
            border: 1px solid #e1e4e8;
            border-left: 4px solid #007bff; /* 蓝色左边框高亮 */
        }

        pre code {
            padding: 0;
            margin: 0;
            font-size: 100%;
            background-color: transparent;
            color: inherit; /* 代码块内代码颜色继承，不使用红色 */
        }

        ul {
            padding-left: 20px;
        }

        li {
            margin-bottom: 0.5em;
        }

        li > b {
            color: #1d2129; /* 列表内关键词加深 */
        }
    </style>
</head>
<body>

<h1>XXL-Job 使用与运维手册</h1>

<p style="color: red;">申请说明：</p>
<p>① XXL-JOB 需要自己准备一个空的数据库，并提前执行初始化脚本。</p>
<p>② 平台支持单实例和集群模式，默认为单实例，如需集群，则设置副本数据为3。</p>
<p>③ AccessToken是用于调度中心与执行器之间进行安全校验的重要凭证，默认值是default_token，请根据实际要求申请时自行修改。</p>

<a href="https://github.com/xuxueli/xxl-job/blob/3.1.1-release/doc/db/tables_xxl_job.sql"
   target="_blank">XXL-JOB初始化脚本</a>

<h2>一、XXL-Job 简介</h2>

<p>XXL-JOB
    是一个轻量级分布式任务调度平台，其核心设计目标是开发迅速、学习简单、轻量级、易扩展。现已开放源代码并被广泛应用于多家公司的生产环境。</p>
<ul>
    <li><b>简单易用:</b> 支持通过Web页面对任务进行CRUD操作，操作简单，一分钟上手。</li>
    <li><b>动态管理:</b> 支持动态修改任务状态、启动/停止任务，以及终止运行中任务，即时生效。</li>
    <li><b>高可用:</b> 调度中心和执行器均支持集群部署，保证了调度和任务执行的高可用性。</li>
    <li><b>弹性扩缩容:</b> 当有新的执行器实例上线或下线时，调度中心下次调度时会自动感知并重新分配任务。</li>
    <li><b>丰富的策略:</b> 提供多种任务触发、阻塞处理和调度过期策略。</li>
    <li><b>任务失败重试:</b> 支持自定义任务失败时的重试次数。</li>
</ul>

<h2>二、XXL-Job 配置文件示例</h2>
<p>XXL-Job 分为“调度中心” (<code>xxl-job-admin</code>) 和“执行器” (<code>xxl-job-executor</code>) 两部分。以下是它们的核心配置
    (通常在 <code>application.properties</code> 文件中)。</p>

<h3>1. 调度中心 (xxl-job-admin) 配置</h3>
<pre><code>### web port
server.port=8080

### a database connection
spring.datasource.url=***************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=password
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

### job log path
xxl.job.logpath=/data/applogs/xxl-job/jobhandler

### access token
xxl.job.accessToken=default_token

### login account
xxl.job.login.username=admin
xxl.job.login.password=123456
</code></pre>

<h3>2. 执行器 (xxl-job-executor) 配置</h3>
<pre><code>### web port
server.port=8081

### log path
logging.config=classpath:logback.xml
xxl.job.executor.logpath=/data/applogs/xxl-job/jobhandler

### xxl-job admin address list, such as "http://address1:port/xxl-job-admin,http://address2:port/xxl-job-admin"
xxl.job.admin.addresses=http://127.0.0.1:8080/xxl-job-admin

### xxl-job, access token
xxl.job.accessToken=default_token

### xxl-job executor app name
xxl.job.executor.appname=xxl-job-executor-sample
### xxl-job executor port, default is 9999
xxl.job.executor.port=9999
</code></pre>

<h2>三、日常运维</h2>
<h3>1. 启动与停止服务</h3>
<p>XXL-Job 通常以后台进程方式运行 Java 应用。</p>
<p><b>启动 (调度中心或执行器):</b></p>
<pre><code>nohup java -jar xxl-job-admin-&lt;version&gt;.jar > xxl-job-admin.log 2>&1 &
nohup java -jar xxl-job-executor-sample-&lt;version&gt;.jar > xxl-job-executor.log 2>&1 &
</code></pre>

<p><b>停止:</b></p>
<pre><code># 找到 XXL-Job 进程的 PID
ps -ef | grep xxl-job

# 优雅地关闭进程
kill -15 [PID]
</code></pre>

<p><b>查看状态:</b></p>
<pre><code># 查看进程是否存活
ps -ef | grep xxl-job

# 查看实时日志
tail -f /data/applogs/xxl-job/jobhandler/xxl-job-admin.log
tail -f /data/applogs/xxl-job/jobhandler/xxl-job-executor-sample.log
</code></pre>

<h3>2. Web 界面核心操作</h3>
<p>日常的任务管理和监控主要通过调度中心 Web 界面完成。访问地址：<code>http://&lt;调度中心IP&gt;:8080/xxl-job-admin</code>
</p>
<ul>
    <li><b>执行器管理:</b> 在此页面注册和管理执行器。执行器项目启动后会自动注册到调度中心，也可手动录入。</li>
    <li><b>任务管理:</b> 核心功能页面，用于创建、启动、停止、编辑和手动触发任务。</li>
    <li><b>调度日志:</b> 查看任务的调度记录，包括每次调度的状态、执行结果和耗时等。</li>
    <li><b>执行日志:</b> 点击调度日志中的“查看执行日志”按钮，可以实时查看任务在执行器端输出的详细日志。</li>
    <li><b>用户管理:</b> 管理登录调度中心的账号和权限。</li>
</ul>

<h2>四、推荐运维实践</h2>
<ul>
    <li><b>数据库独立部署:</b> 为调度中心使用独立的、高可用的数据库实例（如 MySQL HA），并定期备份。</li>
    <li><b>调度中心集群部署:</b> 部署多个调度中心实例并使用 Nginx 等负载均衡器进行代理，实现调度中心的高可用。</li>
    <li><b>执行器集群部署:</b> 对核心业务的执行器进行集群部署，避免单点故障。在任务配置中，路由策略选择“分片广播”或合适的负载均衡策略。
    </li>
    <li><b>访问令牌 (AccessToken):</b> 在调度中心和执行器中配置相同的、复杂的 <code>accessToken</code>，增强通信安全。</li>
    <li><b>日志与监控:</b> 监控调度中心和执行器的 JVM 状态、日志输出，并配置告警，以便及时发现问题。</li>
    <li><b>合理配置任务:</b> 根据业务需求合理设置任务的 “Cron表达式”、“阻塞处理策略” 和 “失败重试次数”，避免任务堆积或雪崩。
    </li>
</ul>

</body>
</html>