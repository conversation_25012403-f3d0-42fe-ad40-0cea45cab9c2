<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Sentinel 使用与运维手册</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 40px;
            background: #f8f9fa;
        }
        h1, h2, h3 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: .3em;
        }
        h1 {
            font-size: 2em;
            color: #2c3e50; /* 深蓝灰色标题 */
        }
        h2 {
            font-size: 1.5em;
            color: #2c3e50; /* 深蓝灰色标题 */
            margin-top: 2em; /* 增加章节间距 */
        }
        h3 {
            font-size: 1.25em;
            color: #34495e; /* 较浅的蓝灰色 */
        }
        code {
            font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
            background-color: #f6f8fa;
            padding: .2em .4em;
            margin: 0;
            font-size: 85%;
            border-radius: 3px;
            color: #d73a49; /* 暗红色行内代码 */
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
            border-radius: 3px;
            border: 1px solid #e1e4e8;
            border-left: 4px solid #fdac41; /* Sentinel 橙色左边框高亮 */
        }
        pre code {
            padding: 0;
            margin: 0;
            font-size: 100%;
            background-color: transparent;
            color: inherit; /* 代码块内代码颜色继承，不使用红色 */
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 0.5em;
        }
        li > b {
             color: #1d2129; /* 列表内关键词加深 */
        }
    </style>
</head>
<body>

    <h1>Sentinel 使用与运维手册</h1>

    <h2>一、Sentinel 简介</h2>
    <p>Sentinel 是阿里巴巴开源的，面向分布式服务架构的轻量级流量控制、熔断降级库。它以流量为切入点，从流量控制、熔断降级、系统负载保护等多个维度保护服务的稳定性。Sentinel 具有丰富的应用场景，已被广泛应用于阿里巴巴集团内部以及众多企业生产环境中。</p>
    <ul>
        <li><b>流量控制:</b> 丰富的流控规则，支持QPS、线程数等多种阈值类型，有效应对秒杀、突发流量等场景。</li>
        <li><b>熔断降级:</b> 当依赖的服务出现不稳定时，自动对调用进行熔断降级，避免级联失败。支持基于异常比例、异常数和慢调用比例等多种策略。</li>
        <li><b>系统负载保护:</b> 当系统负载过高时，自动开启保护，保证核心服务可用，防止系统被压垮。</li>
        <li><b>实时监控:</b> 提供功能强大的控制台 (Dashboard)，可以实时监控应用的各项指标，并动态修改规则。</li>
        <li><b>生态广泛:</b> 深度适配 Spring Cloud、Dubbo、gRPC 等主流微服务框架，开箱即用。</li>
    </ul>

    <h2>二、Sentinel 配置与接入示例</h2>
    <p>Sentinel 分为“核心库” (应用客户端) 和“控制台” (Dashboard) 两部分。</p>

    <h3>1. 应用客户端接入 (以 Spring Boot 为例)</h3>
    <p>在您的微服务项目 (如 Spring Boot) 的 <code>application.properties</code> 或 <code>application.yml</code> 中添加以下配置，使其与 Sentinel 控制台建立心跳连接。</p>
    <pre><code># 应用名称
spring.application.name=my-sentinel-service

# Sentinel 控制台地址
spring.cloud.sentinel.transport.dashboard=127.0.0.1:8080

# 客户端API端口，用于和Dashboard通信
spring.cloud.sentinel.transport.port=8719
</code></pre>

    <h3>2. 规则持久化配置 (以 Nacos 为例)</h3>
    <p>为防止规则在应用重启后丢失，生产环境必须将规则持久化。以下是将流控规则持久化到 Nacos 的配置示例。</p>
    <pre><code>spring.cloud.sentinel.datasource.ds1.nacos.server-addr=127.0.0.1:8848
spring.cloud.sentinel.datasource.ds1.nacos.data-id=${spring.application.name}-flow-rules
spring.cloud.sentinel.datasource.ds1.nacos.group-id=SENTINEL_GROUP
spring.cloud.sentinel.datasource.ds1.nacos.data-type=json
spring.cloud.sentinel.datasource.ds1.nacos.rule-type=flow
</code></pre>

    <h2>三、日常运维</h2>
    <h3>1. 启动与停止 Sentinel 控制台</h3>
    <p>Sentinel 控制台是一个标准的 Spring Boot 应用，以 JAR 包形式提供。</p>
    <p><b>下载:</b> 从 Sentinel 的 <a href="https://github.com/alibaba/Sentinel/releases" target="_blank">GitHub Releases</a> 页面下载最新的 <code>sentinel-dashboard-xxx.jar</code>。</p>
    <p><b>启动:</b></p>
    <pre><code># -Dserver.port=8080 指定运行端口
# -Dcsp.sentinel.dashboard.server=localhost:8080 指定控制台本身地址
# -Dproject.name=sentinel-dashboard 指定在 EUREKA 等注册中心显示的名字
nohup java -Dserver.port=8080 -Dcsp.sentinel.dashboard.server=localhost:8080 -Dproject.name=sentinel-dashboard -jar sentinel-dashboard-*.jar > sentinel.log 2>&1 &
</code></pre>

    <p><b>停止:</b></p>
    <pre><code># 找到 Sentinel Dashboard 进程的 PID
ps -ef | grep sentinel-dashboard

# 优雅地关闭进程
kill -15 [PID]
</code></pre>
    
    <h3>2. 控制台核心操作</h3>
    <p>Sentinel 的日常管理和监控主要通过控制台完成。访问地址：<code>http://<Dashboard-IP>:8080</code> (默认用户名/密码: sentinel/sentinel)</p>
    <ul>
        <li><b>实时监控:</b> 查看接入应用的实时 QPS、线程数、响应时间等曲线。需在应用端发起一次资源调用后，应用才会出现在左侧菜单。</li>
        <li><b>簇点链路:</b> 查看应用的所有受 Sentinel 监控的资源(API接口、方法等)列表。</li>
        <li><b>流控规则:</b> 为指定资源配置 QPS 或线程数限制，防止流量洪峰。</li>
        <li><b>降级规则:</b> 配置熔断策略，当资源出现过多异常或慢调用时自动熔断。</li>
        <li><b>系统规则:</b> 从整个应用的维度配置系统负载保护规则。</li>
    </ul>

    <h2>四、推荐运维实践</h2>
    <ul>
        <li><b>规则持久化:</b> 生产环境中，必须将动态修改的规则持久化到配置中心 (如 Nacos, Apollo) 或其他存储 (如 Redis, Zookeeper)，否则控制台重启或应用重启将导致规则丢失。</li>
        <li><b>控制台高可用:</b> Sentinel 控制台本身是无状态的，可以部署多个实例并通过负载均衡器代理，实现高可用。</li>
        <li><b>登录认证:</b> 及时修改控制台的默认登录密码。可以通过 Spring Security 等方式增强登录认证和权限管理。</li>
        <li><b>监控与告警:</b> 监控 Sentinel 控制台自身的健康状况，并可以考虑将其 Metrics 对接到统一的监控平台。</li>
        <li><b>合理设置 Fallback/BlockHandler:</b> 为受保护的资源定义清晰的降级处理逻辑 (Fallback) 和流控处理逻辑 (BlockHandler)，提升用户体验，避免向用户直接暴露错误。</li>
    </ul>

</body>
</html>