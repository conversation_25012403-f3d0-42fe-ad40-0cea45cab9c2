<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>Apache Kafka 使用与运维手册</title>
  <style>
    body {
      font-family: "Microsoft YaHei", sans-serif;
      padding: 40px;
      background-color: #f9f9f9;
      color: #333;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #2c3e50; /* Kafka/Confluent-like color */
    }
    pre, code {
      background-color: #eee;
      padding: 4px 6px;
      border-radius: 4px;
      font-family: monospace;
      border: 1px solid #ddd;
    }
    pre {
      padding: 10px;
      overflow-x: auto;
    }
    ul {
      margin-top: 0.5em;
      padding-left: 1.5em;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 10px;
    }
    table, th, td {
      border: 1px solid #ccc;
      padding: 8px;
      text-align: left;
    }
    a {
      color: #0079c1; /* Kafka/Confluent link color */
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>🌊 Apache Kafka 使用与运维手册（README）</h1>

  <h2>📖 一、Apache Kafka 简介</h2>
  <p>Apache Kafka 是一个开源的分布式事件流平台，能够处理万亿级别的事件。它被广泛用于构建实时数据管道和流式应用程序，具有高吞吐量、可扩展性、持久性和容错性。</p>
  <ul>
    <li><strong>高吞吐量</strong>：支持每秒数百万条消息。</li>
    <li><strong>可扩展性</strong>：分布式系统，易于通过增加节点进行水平扩展。</li>
    <li><strong>持久性</strong>：消息持久化到磁盘，并支持配置多副本以防止数据丢失。</li>
    <li><strong>容错性</strong>：集群中的节点故障可被容忍，服务持续可用。</li>
    <li><strong>解耦</strong>：生产者和消费者解耦，可以独立演化和扩展。</li>
    <li><strong>流处理集成</strong>：与 Kafka Streams 和 ksqlDB 等流处理框架紧密集成。</li>
  </ul>

  <h2>⚙️ 二、基本概念</h2>
  <table>
    <tr><th>概念</th><th>说明</th></tr>
    <tr><td>Broker</td><td>Kafka 集群中的一个服务器实例。负责存储数据、处理客户端请求。</td></tr>
    <tr><td>Topic</td><td>消息的类别或提要名称。生产者发布消息到特定的 Topic。</td></tr>
    <tr><td>Partition</td><td>Topic 的物理分区，一个 Topic 可以有多个 Partition。每个 Partition 是一个有序的、不可变的消息序列。Partition 允许 Topic 水平扩展和并发处理。</td></tr>
    <tr><td>Offset</td><td>Partition 内每条消息的唯一标识符，表示消息在 Partition 中的位置。</td></tr>
    <tr><td>Producer</td><td>向 Kafka Topic发布（写入）消息的应用程序。</td></tr>
    <tr><td>Consumer</td><td>从 Kafka Topic订阅（读取）消息的应用程序。</td></tr>
    <tr><td>Consumer Group</td><td>一个或多个 Consumer 组成的群组，共同消费一个或多个 Topic。一个 Partition 在同一时刻只能被一个 Consumer Group 内的一个 Consumer 消费。</td></tr>
    <tr><td>ZooKeeper</td><td>（传统模式）用于管理和协调 Kafka Broker 的分布式协调服务，存储集群元数据。</td></tr>
    <tr><td>KRaft (Kafka Raft metadata mode)</td><td>（新模式）Kafka 自行管理的基于 Raft 协议的元数据仲裁机制，旨在替代 ZooKeeper。</td></tr>
    <tr><td>Replica</td><td>Partition 的副本，用于数据冗余和高可用。每个 Partition 有一个 Leader Replica 和零个或多个 Follower Replicas。</td></tr>
    <tr><td>Leader</td><td>每个 Partition 的 Leader Replica 负责处理所有读写请求。</td></tr>
    <tr><td>Follower</td><td>Follower Replica 被动地复制 Leader Replica 的数据。</td></tr>
  </table>

  <h2>📁 三、核心配置文件</h2>
  <h3>1. Broker (<code>config/server.properties</code>)</h3>
  <pre><code>broker.id=0
listeners=PLAINTEXT://your.host.name:9092
advertised.listeners=PLAINTEXT://your.host.name:9092 # For clients to connect
log.dirs=/var/lib/kafka/data # Kafka 日志数据存储目录
num.partitions=1 # 默认 Topic 分区数
default.replication.factor=1 # 默认 Topic 副本因子 (生产环境建议 >=3)

# ZooKeeper (如果使用 ZooKeeper 模式)
zookeeper.connect=localhost:2181

# KRaft (如果使用 KRaft 模式 - 示例配置，具体配置较复杂)
# process.roles=broker,controller
# node.id=1
# controller.quorum.voters=1@localhost:9093
# listeners=PLAINTEXT://:9092,CONTROLLER://:9093
# advertised.listeners=PLAINTEXT://localhost:9092
# controller.listener.names=CONTROLLER
# listener.security.protocol.map=CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT,SSL:SSL,SASL_PLAINTEXT:SASL_PLAINTEXT,SASL_SSL:SASL_SSL
</code></pre>
  <h3>2. ZooKeeper (<code>config/zookeeper.properties</code> - 若使用)</h3>
  <pre><code>dataDir=/var/lib/zookeeper/data
clientPort=2181
maxClientCnxns=0
admin.enableServer=false # 建议设置为 false 除非需要 admin server
</code></pre>

  <h2>🧰 四、日常运维命令 (位于 Kafka <code>bin/</code> 目录下)</h2>
  <p>以下命令通常需要指定 <code>--bootstrap-server &lt;broker_list&gt;</code> (用于连接 Kafka Broker) 或 <code>--zookeeper &lt;zookeeper_list&gt;</code> (传统模式下用于某些管理操作，新版本倾向于 <code>--bootstrap-server</code>)。KRaft 模式下，Topic 等操作使用 <code>--bootstrap-server</code>。</p>

  <h3>1. 服务管理 (以 Systemd 为例，实际脚本可能不同)</h3>
  <h4>ZooKeeper (如果使用)</h4>
  <pre><code># 启动 ZooKeeper (使用 Kafka 自带脚本)
bin/zookeeper-server-start.sh config/zookeeper.properties

# 停止 ZooKeeper (使用 Kafka 自带脚本)
bin/zookeeper-server-stop.sh

# systemctl (如果配置为服务)
sudo systemctl start zookeeper
sudo systemctl stop zookeeper
sudo systemctl status zookeeper</code></pre>
  <h4>Kafka Broker / Controller (KRaft)</h4>
  <pre><code># 格式化存储目录 (仅在首次启动 KRaft Controller 时)
# KAFKA_CLUSTER_ID="$(bin/kafka-storage.sh random-uuid)"
# bin/kafka-storage.sh format -t $KAFKA_CLUSTER_ID -c config/kraft/server.properties

# 启动 Kafka Server (Broker/Controller)
bin/kafka-server-start.sh config/kraft/server.properties # (KRaft 模式，Controller 和 Broker 可能使用不同配置文件或同一配置)
# 或者 (ZooKeeper 模式)
bin/kafka-server-start.sh config/server.properties

# 停止 Kafka Server
bin/kafka-server-stop.sh

# systemctl (如果配置为服务)
sudo systemctl start kafka
sudo systemctl stop kafka
sudo systemctl status kafka</code></pre>

  <h3>2. Topic 管理</h3>
  <pre><code># 创建 Topic
bin/kafka-topics.sh --create --topic my-topic --partitions 3 --replication-factor 1 --bootstrap-server localhost:9092
# (传统模式下可能用 --zookeeper localhost:2181)

# 列出所有 Topic
bin/kafka-topics.sh --list --bootstrap-server localhost:9092

# 查看 Topic 详情
bin/kafka-topics.sh --describe --topic my-topic --bootstrap-server localhost:9092

# 修改 Topic (例如增加分区 - 注意：只能增加不能减少)
bin/kafka-topics.sh --alter --topic my-topic --partitions 6 --bootstrap-server localhost:9092

# 删除 Topic (需 broker 配置 delete.topic.enable=true)
bin/kafka-topics.sh --delete --topic my-topic --bootstrap-server localhost:9092</code></pre>

  <h3>3. 生产者/消费者测试</h3>
  <pre><code># 启动控制台生产者
bin/kafka-console-producer.sh --topic my-topic --bootstrap-server localhost:9092
>Hello Kafka
>Another message

# 启动控制台消费者 (从头开始消费)
bin/kafka-console-consumer.sh --topic my-topic --from-beginning --bootstrap-server localhost:9092

# 启动控制台消费者 (消费指定消费组)
bin/kafka-console-consumer.sh --topic my-topic --group my-consumer-group --bootstrap-server localhost:9092</code></pre>

  <h3>4. Consumer Group 管理</h3>
  <pre><code># 列出所有 Consumer Group
bin/kafka-consumer-groups.sh --list --bootstrap-server localhost:9092

# 查看特定 Consumer Group 详情 (包括 Lag)
bin/kafka-consumer-groups.sh --describe --group my-consumer-group --bootstrap-server localhost:9092

# 查看特定 Consumer Group 的 Offset
bin/kafka-consumer-groups.sh --describe --group my-consumer-group --members --verbose --bootstrap-server localhost:9092

# 重置 Consumer Group 的 Offset (例如到最早 --to-earliest, 最新 --to-latest, 或特定时间 --to-datetime YYYY-MM-DDTHH:mm:SS.sss)
bin/kafka-consumer-groups.sh --reset-offsets --group my-consumer-group --topic my-topic --to-earliest --execute --bootstrap-server localhost:9092
# (谨慎操作，确保 Consumer 已停止)

# 删除 Consumer Group (当 Group 内无成员且元数据在 Broker 中时)
bin/kafka-consumer-groups.sh --delete --group my-consumer-group --bootstrap-server localhost:9092</code></pre>

  <h3>5. 查看消息 (不推荐生产环境大规模使用)</h3>
  <pre><code># 查看特定 Topic 特定 Partition 的消息 (需要提供分区号和起始 offset)
# bin/kafka-console-consumer.sh --topic my-topic --partition 0 --offset 0 --max-messages 5 --bootstrap-server localhost:9092
# 更强大的工具如 kcat (原 kafkacat) 更适合此类操作
# kcat -b localhost:9092 -t my-topic -p 0 -o 0 -c 5</code></pre>

  <h3>6. Broker 信息/集群健康</h3>
  <pre><code># 获取集群元数据 (例如 Broker 列表，Controller ID)
# bin/zookeeper-shell.sh localhost:2181 ls /brokers/ids (ZooKeeper 模式)
# (KRaft 模式下可以通过 JMX 指标或管理 API 查看)

# 查看 Topic 的 Leader 和 ISR 信息
bin/kafka-topics.sh --describe --under-replicated-partitions --bootstrap-server localhost:9092
bin/kafka-topics.sh --describe --unavailable-partitions --bootstrap-server localhost:9092

# 查看 Broker 的配置
bin/kafka-configs.sh --describe --entity-type brokers --entity-name 0 --bootstrap-server localhost:9092
  </code></pre>

  <h2>🛠️ 五、常见问题与排查</h2>
  <table>
    <tr><th>问题</th><th>可能原因与处理方式</th></tr>
    <tr><td>无法连接 Broker</td><td>检查 Broker 是否运行、<code>advertised.listeners</code> 配置是否正确、网络防火墙、DNS 解析。</td></tr>
    <tr><td>Topic 创建失败</td><td>检查 Broker 是否有足够的资源、权限（如果启用了 ACL）、ZooKeeper/KRaft 是否正常。</td></tr>
    <tr><td>消息发送/接收超时</td><td>网络延迟、Broker 负载过高、Producer/Consumer 配置不当（如 `request.timeout.ms`）。</td></tr>
    <tr><td>Consumer Lag 过大</td><td>Consumer 处理速度慢、消息量突增、Consumer 实例数少于 Partition 数、Rebalance 频繁。</td></tr>
    <tr><td>Under-replicated Partitions</td><td>Broker 宕机、网络问题导致副本同步失败、磁盘 I/O 瓶颈。检查 Broker 日志。</td></tr>
    <tr><td>ZooKeeper/KRaft 问题</td><td>(ZooKeeper) Ensemble 未达到法定数量、磁盘问题。(KRaft) Controller 仲裁问题、网络分区。检查相应服务的日志。</td></tr>
    <tr><td>磁盘空间不足</td><td>检查 `log.dirs` 目录，调整 Topic 的保留策略 (`retention.ms`, `retention.bytes`)，或扩容磁盘。</td></tr>
  </table>

  <h2>🔐 六、安全建议</h2>
  <ul>
    <li><strong>启用认证 (Authentication)</strong>: 使用 SASL (Kerberos, PLAIN, SCRAM) 确保只有授权的客户端可以连接。</li>
    <li><strong>启用授权 (Authorization)</strong>: 使用 ACLs (Access Control Lists) 控制用户对 Topic 和 Consumer Group 的操作权限。</li>
    <li><strong>数据加密 (Encryption)</strong>: 使用 SSL/TLS 对 Broker 和客户端之间传输的数据进行加密 (data-in-transit)。考虑对静态数据进行加密 (data-at-rest)。</li>
    <li><strong>网络隔离</strong>: 将 Kafka 集群部署在受信任的网络中，使用防火墙限制不必要的端口访问。</li>
    <li><strong>禁用自动创建 Topic</strong>: 生产环境设置 `auto.create.topics.enable=false`，通过运维脚本或工具统一管理 Topic。</li>
    <li><strong>安全配置 ZooKeeper/KRaft</strong>: 确保 ZooKeeper/KRaft 的访问控制和安全配置。KRaft 模式下，Controller 之间的通信也应受保护。</li>
    <li><strong>定期审计和监控</strong>: 监控认证失败、授权拒绝等安全事件。定期审查 ACL 配置。</li>
  </ul>

  <h2>📚 七、参考资料</h2>
  <p>
    👉 <a href="https://kafka.apache.org/quickstart" target="_blank">Kafka 官方快速入门</a><br>
    👉 <a href="https://kafka.apache.org/documentation/" target="_blank">Kafka 官方文档</a><br>
    👉 <a href="https://kafka.apache.org/documentation/#operations" target="_blank">Kafka 运维操作文档</a><br>
    👉 <a href="https://kafka.apache.org/documentation/#security" target="_blank">Kafka 安全文档</a><br>
    👉 <a href="https://developer.confluent.io/learn-kafka/" target="_blank">Confluent Learn Kafka (丰富的学习资源)</a>
  </p>
</body>
</html>
