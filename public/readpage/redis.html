<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Redis 使用与运维手册</title>
  <style>
    body {
      font-family: "Microsoft YaHei", sans-serif;
      background-color: #f9f9f9;
      color: #333;
      line-height: 1.6;
      padding: 40px;
    }
    h1, h2, h3 {
      color: #c0392b;
    }
    code, pre {
      background-color: #eee;
      padding: 4px 6px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      padding: 10px;
      overflow-x: auto;
    }
    ul {
      margin-top: 0.5em;
      padding-left: 1.5em;
    }
    a {
      color: #2980b9;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>Redis 使用与运维手册</h1>

  <h2>📖 一、Redis 简介</h2>
  <p>Redis（Remote Dictionary Server）是一个开源的、基于内存的数据结构存储系统。它可用于数据库、缓存和消息代理。</p>
  <ul>
    <li>支持字符串、哈希、列表、集合、有序集合等多种数据结构</li>
    <li>支持持久化（RDB 和 AOF）</li>
    <li>支持主从复制、哨兵和集群</li>
    <li>单线程，性能极高</li>
    <li>支持多种语言客户端</li>
  </ul>

  <h2>⚙️ 二、Redis 配置文件示例</h2>
  <pre><code>bind 127.0.0.1
port 6379
requirepass your_password
daemonize yes
appendonly yes
appendfilename "appendonly.aof"
dir /var/lib/redis
logfile "/var/log/redis/redis-server.log"</code></pre>

  <h2>🧰 三、日常运维命令</h2>

  <h3>1. 启动与停止服务</h3>
  <pre><code>systemctl start redis
systemctl stop redis
systemctl restart redis
systemctl status redis
redis-server /etc/redis/redis.conf</code></pre>

  <h3>2. 登录命令行</h3>
  <pre><code>redis-cli
redis-cli -h 127.0.0.1 -p 6379 -a your_password</code></pre>

  <h3>3. 基础操作</h3>
  <pre><code>SET key value
GET key
DEL key
KEYS *
TYPE key
TTL key</code></pre>

  <h3>4. 管理命令</h3>
  <pre><code>CONFIG GET *
CONFIG SET maxmemory 256mb
FLUSHALL
FLUSHDB
INFO
CLIENT LIST</code></pre>

  <h2>🧪 四、持久化管理</h2>
  <h3>RDB：</h3>
  <pre><code>SAVE
BGSAVE</code></pre>
  <h3>AOF：</h3>
  <pre><code>BGREWRITEAOF</code></pre>

  <h2>🔐 五、安全管理</h2>
  <pre><code>CONFIG SET requirepass newpass
# 或在 redis.conf 中设置
requirepass your_password
AUTH your_password</code></pre>

  <h2>📦 六、备份与恢复</h2>
  <pre><code>cp /var/lib/redis/dump.rdb /backup/redis-backup.rdb
cp /backup/redis-backup.rdb /var/lib/redis/dump.rdb
systemctl restart redis</code></pre>

  <h2>🛠️ 七、排障与调试</h2>
  <pre><code>tail -f /var/log/redis/redis-server.log
netstat -tulnp | grep 6379
ps -ef | grep redis</code></pre>

  <h2>📌 八、推荐运维实践</h2>
  <ul>
    <li>限制外网访问并开启认证</li>
    <li>启用 RDB + AOF 双重持久化</li>
    <li>部署哨兵实现高可用</li>
    <li>合理设置 maxmemory 和淘汰策略</li>
    <li>使用 redis-cli --latency 和 INFO 监控性能</li>
  </ul>

  <p>👉 <a href="https://redis.io/documentation" target="_blank">Redis 官方文档</a></p>
</body>
</html>