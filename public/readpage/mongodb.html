<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>MongoDB 使用与运维手册</title>
  <style>
    body {
      font-family: "Microsoft YaHei", sans-serif;
      padding: 40px;
      background-color: #f9f9f9;
      color: #333;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    pre, code {
      background-color: #eee;
      padding: 4px 6px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      padding: 10px;
      overflow-x: auto;
    }
    ul {
      margin-top: 0.5em;
      padding-left: 1.5em;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 10px;
    }
    table, th, td {
      border: 1px solid #ccc;
      padding: 8px;
      text-align: left;
    }
    a {
      color: #2980b9;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>MongoDB 使用与运维手册（README）</h1>

  <h2>📖 一、MongoDB 简介</h2>
  <p>MongoDB 是一个基于文档的分布式 NoSQL 数据库系统，使用 BSON（二进制 JSON）格式存储数据，广泛用于大数据、内容管理、日志管理和实时分析等场景。</p>
  <ul>
    <li>面向文档，灵活的数据模型（JSON/BSON）</li>
    <li>支持高可用副本集与自动分片</li>
    <li>强大的查询与聚合能力</li>
    <li>原生支持分布式部署</li>
    <li>跨平台，社区活跃</li>
  </ul>

  <h2>⚙️ 二、基本概念</h2>
  <table>
    <tr><th>概念</th><th>说明</th></tr>
    <tr><td>Database</td><td>数据库，类似 MySQL 的 schema</td></tr>
    <tr><td>Collection</td><td>表结构（无模式）</td></tr>
    <tr><td>Document</td><td>BSON 格式的数据对象（类似行）</td></tr>
    <tr><td>Replica Set</td><td>副本集，提供高可用</td></tr>
    <tr><td>Shard</td><td>分片，用于水平扩展</td></tr>
    <tr><td>mongod</td><td>MongoDB 守护进程</td></tr>
    <tr><td>mongo</td><td>MongoDB 命令行客户端</td></tr>
  </table>

  <h2>📁 三、核心配置文件（mongod.conf）</h2>
  <pre><code>storage:
  dbPath: /var/lib/mongo
  journal:
    enabled: true
net:
  port: 27017
  bindIp: 0.0.0.0
security:
  authorization: enabled
replication:
  replSetName: rs0</code></pre>

  <h2>🧰 四、日常运维命令</h2>

  <h3>1. 服务管理</h3>
  <pre><code>systemctl start mongod
systemctl stop mongod
systemctl restart mongod
systemctl status mongod</code></pre>

  <h3>2. 登录 MongoDB Shell</h3>
  <pre><code>mongo
mongo admin -u root -p yourpassword</code></pre>

  <h3>3. 用户管理</h3>
  <pre><code>use admin
db.createUser({
  user: "root",
  pwd: "yourpassword",
  roles: ["root"]
})

use mydb
db.createUser({
  user: "appuser",
  pwd: "password",
  roles: [{ role: "readWrite", db: "mydb" }]
})</code></pre>

  <h3>4. 数据库操作</h3>
  <pre><code>show dbs
use mydb
db
show collections
db.dropDatabase()</code></pre>

  <h3>5. 文档操作</h3>
  <pre><code>db.users.insert({ name: "Alice", age: 25 })
db.users.find({ name: "Alice" })
db.users.update({ name: "Alice" }, { $set: { age: 30 } })
db.users.remove({ name: "Alice" })</code></pre>

  <h3>6. 备份与恢复</h3>
  <pre><code>mongodump -u root -p yourpassword --authenticationDatabase admin -d mydb -o /backup/

mongorestore -u root -p yourpassword --authenticationDatabase admin /backup/mydb/</code></pre>

  <h3>7. 查看副本集状态</h3>
  <pre><code>rs.status()
rs.initiate()
rs.add("host2:27017")</code></pre>

  <h3>8. 性能监控与诊断</h3>
  <pre><code>db.serverStatus()
db.currentOp()
db.hostInfo()
db.stats()</code></pre>

  <h2>🛠️ 五、常见问题与排查</h2>
  <table>
    <tr><th>问题</th><th>可能原因与处理方式</th></tr>
    <tr><td>无法连接数据库</td><td>检查端口、防火墙、bindIp 设置</td></tr>
    <tr><td>权限不足</td><td>确认用户授权与认证数据库</td></tr>
    <tr><td>数据写入失败</td><td>检查磁盘容量、查看日志</td></tr>
    <tr><td>副本集状态异常</td><td>检查网络与节点状态，重新配置副本集</td></tr>
  </table>

  <h2>🔐 六、安全建议</h2>
  <ul>
    <li>启用访问控制（authorization）</li>
    <li>限制 IP 访问（bindIp）</li>
    <li>开启副本集并启用 TLS</li>
    <li>禁止空口令登录</li>
    <li>定期备份数据</li>
  </ul>

  <h2>📚 七、参考资料</h2>
  <p>
    👉 <a href="https://www.mongodb.com/docs/" target="_blank">MongoDB 官方文档</a><br>
    👉 <a href="https://www.mongodb.com/docs/manual/administration/" target="_blank">MongoDB 运维手册</a><br>
    👉 <a href="https://www.mongodb.com/docs/database-tools/" target="_blank">数据库工具手册（dump/restore）</a>
  </p>
</body>
</html>