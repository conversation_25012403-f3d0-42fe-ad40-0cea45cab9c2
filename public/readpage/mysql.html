<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>MySQL 使用与运维手册</title>
  <style>
    body {
      font-family: "Microsoft YaHei", sans-serif;
      line-height: 1.6;
      margin: 40px;
      background-color: #f9f9f9;
      color: #333;
    }
    h1, h2, h3 {
      color: #2c3e50;
    }
    code, pre {
      background-color: #eee;
      padding: 4px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      padding: 10px;
      overflow-x: auto;
    }
    a {
      color: #2980b9;
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>MySQL 使用与运维手册</h1>

  <h2>📖 一、MySQL 简介</h2>
  <p>MySQL 是一个开源的关系型数据库管理系统，属于 RDBMS（Relational Database Management System）的一种，使用结构化查询语言 SQL 进行数据管理和操作。</p>
  <ul>
    <li>开源免费，社区活跃</li>
    <li>支持事务，支持 ACID 特性</li>
    <li>支持主从复制、读写分离</li>
    <li>支持多种存储引擎</li>
    <li>兼容主流开发语言和框架</li>
  </ul>

  <h2>⚙️ 二、基本配置文件</h2>
  <pre><code>[mysqld]
port = 3306
datadir = /var/lib/mysql
socket = /var/lib/mysql/mysql.sock
symbolic-links=0
log-error = /var/log/mysqld.log
pid-file = /var/run/mysqld/mysqld.pid</code></pre>

  <h2>🧰 三、日常运维常用命令</h2>
  <h3>1. 数据库服务管理</h3>
  <pre><code>systemctl start mysqld
systemctl stop mysqld
systemctl restart mysqld
systemctl status mysqld</code></pre>

  <h3>2. 登录数据库</h3>
  <pre><code>mysql -u root -p
mysql -h 127.0.0.1 -P 3306 -u user -p</code></pre>

  <h3>3. 查看数据库信息</h3>
  <pre><code>SHOW DATABASES;
USE your_database;
SHOW TABLES;
SHOW CREATE TABLE tablename;</code></pre>

  <h3>4. 用户与权限管理</h3>
  <pre><code>CREATE USER 'newuser'@'%' IDENTIFIED BY 'password';
GRANT ALL PRIVILEGES ON dbname.* TO 'newuser'@'%';
REVOKE ALL PRIVILEGES ON dbname.* FROM 'newuser'@'%';
SHOW GRANTS FOR 'newuser'@'%';
DROP USER 'newuser'@'%';</code></pre>

  <h3>5. 数据备份与恢复</h3>
  <pre><code>mysqldump -u root -p dbname > dbname.sql
mysqldump -u root -p --all-databases > all_databases.sql
mysqldump -u root -p -d dbname > schema.sql
mysql -u root -p dbname < dbname.sql</code></pre>

  <h3>6. 性能与状态查看</h3>
  <pre><code>SHOW STATUS;
SHOW PROCESSLIST;
SHOW VARIABLES;
SHOW ENGINE INNODB STATUS;</code></pre>

  <h3>7. 安全初始化</h3>
  <pre><code>mysql_secure_installation</code></pre>

  <h2>🛠️ 四、常见问题排查命令</h2>
  <pre><code>tail -f /var/log/mysqld.log
netstat -tulnp | grep 3306
ps -ef | grep mysqld</code></pre>

  <h2>📌 五、其他推荐操作</h2>
  <ul>
    <li>定期备份（使用 mysqldump + crontab）</li>
    <li>使用主从复制或 MGR 提升高可用性</li>
    <li>开启慢查询日志进行 SQL 优化</li>
    <li>使用 <code>pt-query-digest</code> 工具进行 SQL 分析</li>
  </ul>

  <p>👉 <a href="https://dev.mysql.com/doc/" target="_blank">MySQL 官方手册</a></p>
</body>
</html>