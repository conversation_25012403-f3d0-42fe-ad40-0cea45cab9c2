<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ZooKeeper 使用与运维手册 (README)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        }
        .code-block {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 12px;
            font-family: 'Courier New', Consolas, monospace;
            font-size: 13px;
            line-height: 1.4;
            overflow-x: auto;
        }
        .section-icon {
            display: inline-block;
            margin-right: 8px;
            font-size: 18px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
        }
        th {
            background-color: #f5f5f5;
            font-weight: 600;
        }
        tr:nth-child(even) {
            background-color: #f9f9f9;
        }
    </style>
</head>
<body class="bg-gray-50 p-6">
    <div class="mx-auto rounded-lg shadow-sm p-8">
        <!-- 标题 -->
        <h1 class="text-2xl font-bold text-gray-800 mb-8 border-b pb-4">
            🐘 ZooKeeper 使用与运维手册 (README)
        </h1>

        <!-- 一、ZooKeeper 简介 -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">
                <span class="section-icon">📚</span>一、ZooKeeper 简介
            </h2>
            <p class="text-gray-600 mb-4 leading-relaxed">
                Apache ZooKeeper 是一个开源的分布式协调服务，为分布式应用提供一致性服务。它是集群的管理者，监视着集群中各个节点的状态，根据节点提交的反馈进行下一步合理操作。
            </p>
            <ul class="list-disc list-inside text-gray-600 space-y-2">
                <li>高可用性，支持集群部署</li>
                <li>强一致性，基于ZAB协议保证数据一致性</li>
                <li>顺序访问，为每个更新请求分配全局唯一递增编号</li>
                <li>高性能，读写比例10:1时性能最佳</li>
                <li>原子性，数据更新要么成功要么失败</li>
                <li>实时性，客户端能够及时获得最新数据</li>
                <li>简单的数据模型，类似文件系统的树形结构</li>
            </ul>
        </section>

        <!-- 二、基本概念 -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">
                <span class="section-icon">⚙️</span>二、基本概念
            </h2>
            <table class="w-full">
                <thead>
                    <tr>
                        <th class="w-1/4">概念</th>
                        <th>说明</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>ZNode</td>
                        <td>ZooKeeper数据模型中的数据单元，类似文件系统中的文件和目录，每个ZNode都可以保存数据和拥有子节点。</td>
                    </tr>
                    <tr>
                        <td>Session</td>
                        <td>客户端与ZooKeeper服务器的连接会话，通过心跳机制保持连接，会话超时后临时节点会被删除。</td>
                    </tr>
                    <tr>
                        <td>Watcher</td>
                        <td>事件监听器，客户端可以在ZNode上设置监听器，当节点数据或子节点发生变化时会收到通知。</td>
                    </tr>
                    <tr>
                        <td>ACL</td>
                        <td>访问控制列表，用于控制ZNode的访问权限，包括创建、读取、写入、删除、管理权限。</td>
                    </tr>
                    <tr>
                        <td>Ensemble</td>
                        <td>ZooKeeper服务器集群，通常由奇数个服务器组成，通过选举算法选出Leader节点。</td>
                    </tr>
                    <tr>
                        <td>Leader</td>
                        <td>集群中的主节点，负责处理写请求和协调其他Follower节点，保证数据一致性。</td>
                    </tr>
                    <tr>
                        <td>Follower</td>
                        <td>集群中的从节点，处理读请求，参与Leader选举和写请求的投票过程。</td>
                    </tr>
                    <tr>
                        <td>Observer</td>
                        <td>观察者节点，只处理读请求，不参与选举和投票，用于扩展集群的读性能。</td>
                    </tr>
                    <tr>
                        <td>ZXID</td>
                        <td>ZooKeeper事务ID，全局唯一的递增编号，用于标识每个写操作的顺序。</td>
                    </tr>
                </tbody>
            </table>
        </section>

        <!-- 三、核心配置文件 -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">
                <span class="section-icon">📄</span>三、核心配置文件
            </h2>
            
            <h3 class="text-lg font-medium text-gray-700 mb-3">1. zoo.cfg（主配置文件）</h3>
            <div class="code-block mb-6">
                <!-- 添加换行格式化配置文件内容 -->
                # 基本配置<br>
                tickTime=2000<br>
                initLimit=10<br>
                syncLimit=5<br>
                dataDir=/var/lib/zookeeper<br>
                clientPort=2181<br>
                <br>
                # 集群配置<br>
                server.1=zoo1:2888:3888<br>
                server.2=zoo2:2888:3888<br>
                server.3=zoo3:2888:3888<br>
                <br>
                # 高级配置<br>
                maxClientCnxns=60<br>
                autopurge.snapRetainCount=3<br>
                autopurge.purgeInterval=1
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">2. myid（服务器标识文件）</h3>
            <div class="code-block mb-6">
                <!-- 添加换行格式化myid文件说明 -->
                # 在dataDir目录下创建myid文件，内容为服务器编号<br>
                echo "1" > /var/lib/zookeeper/myid
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">3. log4j.properties（日志配置）</h3>
            <div class="code-block">
                <!-- 添加换行格式化日志配置内容 -->
                # 根日志级别<br>
                zookeeper.root.logger=INFO, CONSOLE<br>
                <br>
                # 控制台输出<br>
                log4j.appender.CONSOLE=org.apache.log4j.ConsoleAppender<br>
                log4j.appender.CONSOLE.layout=org.apache.log4j.PatternLayout<br>
                log4j.appender.CONSOLE.layout.ConversionPattern=%d{ISO8601} [myid:%X{myid}] - %-5p [%t:%C{1}@%L] - %m%n<br>
                <br>
                # 文件输出<br>
                log4j.appender.ROLLINGFILE=org.apache.log4j.RollingFileAppender<br>
                log4j.appender.ROLLINGFILE.File=${zookeeper.log.dir}/zookeeper.log<br>
                log4j.appender.ROLLINGFILE.MaxFileSize=10MB<br>
                log4j.appender.ROLLINGFILE.MaxBackupIndex=10
            </div>
        </section>

        <!-- 四、日常运维命令 -->
        <section class="mb-8">
            <h2 class="text-xl font-semibold text-gray-700 mb-4">
                <span class="section-icon">🔧</span>四、日常运维命令（使用 zkCli.sh）
            </h2>
            <p class="text-gray-600 mb-4">
                zkCli.sh 是 ZooKeeper 的命令行客户端工具，位于 bin/ 目录下。使用前需要先连接到 ZooKeeper 服务器。
            </p>
            
            <div class="code-block mb-4">
                <!-- 添加换行格式化连接命令 -->
                # 连接到ZooKeeper服务器<br>
                ./zkCli.sh -server localhost:2181
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">1. 服务管理（使用 zkServer.sh）</h3>
            <div class="code-block mb-6">
                <!-- 添加换行格式化服务管理命令 -->
                # 启动ZooKeeper服务<br>
                ./zkServer.sh start<br>
                <br>
                # 停止ZooKeeper服务<br>
                ./zkServer.sh stop<br>
                <br>
                # 重启ZooKeeper服务<br>
                ./zkServer.sh restart<br>
                <br>
                # 查看ZooKeeper状态<br>
                ./zkServer.sh status
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">2. 节点操作</h3>
            <div class="code-block mb-6">
                <!-- 添加换行格式化节点操作命令 -->
                # 创建节点<br>
                create /mynode "mydata"<br>
                create -e /temp "tempdata"&nbsp;&nbsp;# 创建临时节点<br>
                create -s /seq "seqdata"&nbsp;&nbsp;&nbsp;&nbsp;# 创建顺序节点<br>
                <br>
                # 查看节点<br>
                ls /&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# 列出根目录下的子节点<br>
                ls /mynode&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# 列出指定节点的子节点<br>
                get /mynode&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# 获取节点数据和状态信息<br>
                <br>
                # 修改节点数据<br>
                set /mynode "newdata"<br>
                <br>
                # 删除节点<br>
                delete /mynode&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# 删除节点（必须没有子节点）<br>
                rmr /mynode&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;# 递归删除节点及其子节点
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">3. 监听器操作</h3>
            <div class="code-block mb-6">
                <!-- 添加换行格式化监听器操作命令 -->
                # 监听节点数据变化<br>
                get /mynode watch<br>
                <br>
                # 监听子节点变化<br>
                ls /mynode watch<br>
                <br>
                # 查看监听器统计信息<br>
                stat /mynode watch
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">4. 集群管理</h3>
            <div class="code-block mb-6">
                <!-- 添加换行格式化集群管理命令 -->
                # 查看集群状态<br>
                echo stat | nc localhost 2181<br>
                <br>
                # 查看集群配置<br>
                echo conf | nc localhost 2181<br>
                <br>
                # 查看连接信息<br>
                echo cons | nc localhost 2181<br>
                <br>
                # 查看环境信息<br>
                echo envi | nc localhost 2181<br>
                <br>
                # 重置统计信息<br>
                echo srst | nc localhost 2181
            </div>

            <h3 class="text-lg font-medium text-gray-700 mb-3">5. 快速开始示例</h3>
            <div class="code-block">
                <!-- 添加换行格式化快速开始示例 -->
                # 1. 连接到ZooKeeper<br>
                ./zkCli.sh -server localhost:2181<br>
                <br>
                # 2. 创建应用根节点<br>
                create /myapp "MyApplication"<br>
                <br>
                # 3. 创建配置节点<br>
                create /myapp/config "database.url=localhost:3306"<br>
                <br>
                # 4. 创建服务发现节点<br>
                create /myapp/services ""<br>
                <br>
                # 5. 注册服务实例<br>
                create -e /myapp/services/web-server-1 "*************:8080"<br>
                <br>
                # 6. 监听服务变化<br>
                ls /myapp/services watch<br>
                <br>
                # 7. 查看节点信息<br>
                get /myapp/config<br>
                stat /myapp/services
            </div>
        </section>
    </div>
</body>
</html>
