<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>RocketMQ 使用与运维手册</title>
  <style>
    body {
      font-family: "Microsoft YaHei", sans-serif;
      padding: 40px;
      background-color: #f9f9f9;
      color: #333;
      line-height: 1.6;
    }
    h1, h2, h3 {
      color: #2c3e50; /* RocketMQ Theme Color */
    }
    pre, code {
      background-color: #eee;
      padding: 4px 6px;
      border-radius: 4px;
      font-family: monospace;
    }
    pre {
      padding: 10px;
      overflow-x: auto;
    }
    ul {
      margin-top: 0.5em;
      padding-left: 1.5em;
    }
    table {
      border-collapse: collapse;
      width: 100%;
      margin-top: 10px;
    }
    table, th, td {
      border: 1px solid #ccc;
      padding: 8px;
      text-align: left;
    }
    a {
      color: #e74c3c; /* RocketMQ Link Color */
      text-decoration: none;
    }
    a:hover {
      text-decoration: underline;
    }
  </style>
</head>
<body>
  <h1>🚀 RocketMQ 使用与运维手册（README）</h1>

  <h2>📖 一、RocketMQ 简介</h2>
  <p>Apache RocketMQ™ 是一个开源的分布式消息和流处理平台，具有低延迟、高并发和高可靠性的特点。它广泛应用于异步通信、应用解耦、流量削峰和大数据集成等场景。</p>
  <ul>
    <li>低延迟、高吞吐量的消息传递</li>
    <li>支持万亿级消息堆积能力</li>
    <li>灵活的事务消息和顺序消息</li>
    <li>强大的消息查询和轨迹追踪功能</li>
    <li>原生支持分布式部署，易于水平扩展</li>
    <li>丰富的API和多语言客户端支持</li>
  </ul>

  <h2>⚙️ 二、基本概念</h2>
  <table>
    <tr><th>概念</th><th>说明</th></tr>
    <tr><td>NameServer (ns)</td><td>名字服务，提供路由元数据管理，服务发现和路由。Broker在此注册。</td></tr>
    <tr><td>Broker (broker)</td><td>消息存储和转发的核心组件，接收生产者发送的消息并为消费者提供拉取服务。</td></tr>
    <tr><td>Topic</td><td>消息的主题，用于对消息进行分类。生产者向特定Topic发送消息，消费者订阅特定Topic。</td></tr>
    <tr><td>Message Queue</td><td>Topic的分区，一个Topic可以有多个Queue，分布在不同的Broker上，用于并发。</td></tr>
    <tr><td>Producer</td><td>消息生产者，负责创建消息并发送到Broker。</td></tr>
    <tr><td>Consumer</td><td>消息消费者，负责从Broker拉取消息并进行处理。</td></tr>
    <tr><td>Consumer Group</td><td>消费者组，多个具有相同Group ID的Consumer实例共同消费一个Topic下的消息。</td></tr>
    <tr><td>Tag</td><td>消息标签，用于在Topic下进一步细分消息，方便消费者进行选择性消费。</td></tr>
    <tr><td>Message</td><td>消息传递的载体，包含消息体、Topic、Tag等属性。</td></tr>
  </table>

  <h2>📁 三、核心配置文件</h2>
  <h3>1. NameServer (<code>namesrv.properties</code> - 通常无需修改)</h3>
  <pre><code># listenPort=9876 (默认)
# rocketmqHome=/path/to/rocketmq (通常通过环境变量设置)</code></pre>

  <h3>2. Broker (<code>broker.conf</code>)</h3>
  <pre><code>brokerClusterName = DefaultCluster
brokerName = broker-a
brokerId = 0  # 0 for Master, >0 for Slave
deleteWhen = 04
fileReservedTime = 48
brokerRole = ASYNC_MASTER # ASYNC_MASTER, SYNC_MASTER, SLAVE
flushDiskType = ASYNC_FLUSH # ASYNC_FLUSH, SYNC_FLUSH
namesrvAddr = localhost:9876 # NameServer 地址列表，多个用分号隔开
storePathRootDir = /data/rocketmq/store
storePathCommitLog = /data/rocketmq/store/commitlog
storePathConsumeQueue = /data/rocketmq/store/consumequeue
storePathIndex = /data/rocketmq/store/index
autoCreateTopicEnable = true # 建议生产环境设为 false，通过命令或控制台创建
listenPort = 10911</code></pre>

  <h2>🧰 四、日常运维命令 (使用 <code>mqadmin</code>)</h2>
  <p><code>mqadmin</code> 是 RocketMQ 的管理工具，位于 <code>bin/</code> 目录下。使用前请确保已配置好环境变量 <code>NAMESRV_ADDR</code> 或者通过 <code>-n <namesrv_addr></code> 参数指定 NameServer 地址。</p>
  <pre><code>export NAMESRV_ADDR='localhost:9876'</code></pre>

  <h3>1. 服务管理 (使用 <code>mqnamesrv</code> 和 <code>mqbroker</code>脚本)</h3>
  <pre><code># 启动 NameServer
nohup sh bin/mqnamesrv &

# 启动 Broker (需指定配置文件)
nohup sh bin/mqbroker -c conf/broker.conf &

# 关闭 NameServer
sh bin/mqshutdown namesrv

# 关闭 Broker
sh bin/mqshutdown broker</code></pre>

  <h3>2. Topic 管理</h3>
  <pre><code># 创建/更新 Topic
sh bin/mqadmin updateTopic -n localhost:9876 -c DefaultCluster -t MyTopic -r 8 -w 8

# 查看 Topic 列表
sh bin/mqadmin topicList -n localhost:9876

# 查看 Topic 状态/路由信息
sh bin/mqadmin topicStatus -n localhost:9876 -t MyTopic

# 查看 Topic 消息队列 offset
sh bin/mqadmin topicClusterList -n localhost:9876 -t MyTopic

# 删除 Topic (需先在所有 Broker 上删除)
sh bin/mqadmin deleteTopic -n localhost:9876 -c DefaultCluster -t MyTopic</code></pre>

  <h3>3. 生产者/消费者连接查看</h3>
  <pre><code># 查看 Producer 连接
# (需要 Topic 名称，通常查看某个 Topic 的生产者连接情况)
sh bin/mqadmin producerConnection -n localhost:9876 -g YOUR_PRODUCER_GROUP -t YOUR_TOPIC

# 查看 Consumer 连接及状态
sh bin/mqadmin consumerProgress -n localhost:9876 -g YOUR_CONSUMER_GROUP
# 或者查看特定 Topic 的消费情况
sh bin/mqadmin consumerProgress -n localhost:9876 -g YOUR_CONSUMER_GROUP -t YOUR_TOPIC</code></pre>

  <h3>4. 消息查询</h3>
  <pre><code># 根据 Message ID 查询消息
sh bin/mqadmin queryMsgById -n localhost:9876 -i YOUR_MESSAGE_ID

# 根据 Key 查询消息 (需要开启消息索引 messageIndexEnable=true, messageIndexSafe=true)
sh bin/mqadmin queryMsgByKey -n localhost:9876 -t MyTopic -k YOUR_MESSAGE_KEY

# 查看消息轨迹 (需要开启消息轨迹 traceTopicEnable=true)
sh bin/mqadmin messageTrace -n localhost:9876 -i YOUR_MESSAGE_ID</code></pre>

  <h3>5. 集群状态</h3>
  <pre><code># 查看集群列表
sh bin/mqadmin clusterList -n localhost:9876

# 查看 Broker 状态
sh bin/mqadmin brokerStatus -n localhost:9876 -b broker_ip:port

# 查看 NameServer 状态
# (通常直接通过 nc -vz nameserver_ip 9876 检查端口)</code></pre>

  <h3>6. 消费者组管理</h3>
  <pre><code># 创建/更新消费者组
sh bin/mqadmin updateSubGroup -n localhost:9876 -c DefaultCluster -g MyConsumerGroup -s true -b 10911 # brokerId 或 brokerAddr

# 查看消费者组列表
sh bin/mqadmin consumerProgress -n localhost:9876 # 会列出所有消费者组

# 删除消费者组 (从指定 Broker)
sh bin/mqadmin deleteSubGroup -n localhost:9876 -c DefaultCluster -g MyConsumerGroup -b broker_ip:port</code></pre>

  <h3>7. 重置消费位点 (谨慎操作)</h3>
  <pre><code># 将消费位点重置到特定时间点
# 时间格式: yyyy-MM-dd#HH:mm:ss:SSS
sh bin/mqadmin resetOffsetByTime -n localhost:9876 -g MyConsumerGroup -t MyTopic -s "2023-10-26#10:00:00:000" -f true</code></pre>


  <h2>🛠️ 五、常见问题与排查</h2>
  <table>
    <tr><th>问题</th><th>可能原因与处理方式</th></tr>
    <tr><td>消息发送失败</td><td>检查 NameServer 和 Broker 是否正常运行、网络连接、Topic 是否存在、权限配置。</td></tr>
    <tr><td>消息消费不到</td><td>检查 Consumer Group ID 是否正确、Topic 订阅是否正确、Broker 是否有消息积压、消费位点是否正确。</td></tr>
    <tr><td>Broker 启动失败</td><td>检查配置文件路径、端口是否被占用、磁盘空间、JDK 版本、NameServer 地址配置。</td></tr>
    <tr><td>NameServer 无法连接</td><td>检查 NameServer 进程是否运行、防火墙设置、网络连通性。</td></tr>
    <tr><td>消息积压过多</td><td>评估消费者消费能力，考虑扩容消费者实例或增加 Topic 队列数。检查是否有慢消费或消费阻塞。</td></tr>
    <tr><td>磁盘空间不足</td><td>检查 Broker 的 `storePathRootDir` 目录，及时清理过期消息或扩容磁盘。配置合理的 `fileReservedTime`。</td></tr>
  </table>

  <h2>🔐 六、安全建议</h2>
  <ul>
    <li><strong>ACL (Access Control List)</strong>: 启用并配置 ACL，对生产者和消费者的 Topic 操作进行权限控制。</li>
    <li><strong>网络隔离</strong>: 将 RocketMQ 集群部署在内部网络，通过防火墙限制外部访问。</li>
    <li><strong>禁用自动创建 Topic</strong>: 生产环境设置 `autoCreateTopicEnable=false`，通过 `mqadmin` 或控制台统一管理 Topic 创建。</li>
    <li><strong>定期审计</strong>: 定期检查 NameServer 和 Broker 的日志，监控异常行为。</li>
    <li><strong>TLS 加密传输</strong>: 在需要公网传输或有安全合规要求的场景下，配置 Broker 和 Client 之间的 TLS 加密通信。</li>
    <li><strong>监控告警</strong>: 建立完善的监控体系，对关键指标（如消息积压、磁盘使用率、Broker 存活状态）设置告警。</li>
  </ul>

  <h2>📚 七、参考资料</h2>
  <p>
    👉 <a href="https://rocketmq.apache.org/docs/quickStart/01quickstart/" target="_blank">RocketMQ 官方快速开始</a><br>
    👉 <a href="https://rocketmq.apache.org/docs/rmq-arc/01overview" target="_blank">RocketMQ 官方文档 (架构概览)</a><br>
    👉 <a href="https://rocketmq.apache.org/docs/rmq-ops/01deploy/" target="_blank">RocketMQ 运维手册</a><br>
    👉 <a href="https://github.com/apache/rocketmq/tree/master/tools" target="_blank">RocketMQ 工具 (mqadmin 等)</a>
  </p>
</body>
</html>
