<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Nacos 使用与运维手册</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            line-height: 1.6;
            color: #333;
            padding: 40px;
            background: #f8f9fa;
        }
        h1, h2, h3 {
            border-bottom: 1px solid #eaecef;
            padding-bottom: .3em;
        }
        h1 {
            font-size: 2em;
            color: #2c3e50; /* 深蓝灰色标题 */
        }
        h2 {
            font-size: 1.5em;
            color: #2c3e50; /* 深蓝灰色标题 */
            margin-top: 2em; /* 增加章节间距 */
        }
        h3 {
            font-size: 1.25em;
            color: #34495e; /* 较浅的蓝灰色 */
        }
        code {
            font-family: "SFMono-Regular", <PERSON><PERSON><PERSON>, "Liberation Mono", <PERSON><PERSON>, Courier, monospace;
            background-color: #f6f8fa;
            padding: .2em .4em;
            margin: 0;
            font-size: 85%;
            border-radius: 3px;
            color: #d73a49; /* 暗红色行内代码 */
        }
        pre {
            background-color: #f6f8fa;
            padding: 16px;
            overflow: auto;
            line-height: 1.45;
            border-radius: 3px;
            border: 1px solid #e1e4e8;
            border-left: 4px solid #28a745; /* Nacos 绿色左边框高亮 */
        }
        pre code {
            padding: 0;
            margin: 0;
            font-size: 100%;
            background-color: transparent;
            color: inherit; /* 代码块内代码颜色继承，不使用红色 */
        }
        ul {
            padding-left: 20px;
        }
        li {
            margin-bottom: 0.5em;
        }
        li > b {
             color: #1d2129; /* 列表内关键词加深 */
        }
    </style>
</head>
<body>

    <h1>Nacos 使用与运维手册</h1>

    <h2>一、Nacos 简介</h2>
    <p>Nacos (Dynamic Naming and Configuration Service) 是一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台。它致力于帮助您发现、配置和管理微服务。Nacos 提供了一组简单易用的特性集，帮助您快速实现动态服务发现、服务配置、服务元数据及流量管理。</p>
    <ul>
        <li><b>服务发现与服务健康监测:</b> 支持基于DNS和基于RPC的服务发现，提供实时的健康检查，阻止向不健康的主机或服务实例发送请求。</li>
        <li><b>动态配置管理:</b> 允许您在所有环境中以集中和动态的方式管理所有应用程序或服务的配置。</li>
        <li><b>动态DNS服务:</b> 支持权重路由，让您更轻松地实现中间层负载均衡、更灵活的路由策略、流量控制以及DNS解析服务。</li>
        <li><b>服务和元数据管理:</b> 能够让您从微服务平台建设的视角管理数据中心的所有服务及元数据。</li>
    </ul>

    <h2>二、Nacos 配置文件示例</h2>
    <p>Nacos 的核心配置位于其解压目录的 <code>conf</code> 文件夹下。</p>

    <h3>1. 集群配置 (cluster.conf)</h3>
    <p>在生产环境中，Nacos 必须以集群模式部署。您需要编辑 <code>cluster.conf</code> 文件，列出所有节点 IP 和端口。</p>
    <pre><code># 每个节点一行，格式为 IP:PORT
************:8848
************:8848
************:8848
</code></pre>

    <h3>2. 核心配置 (application.properties)</h3>
    <p>此文件包含了 Nacos 服务的端口、运行模式以及最重要的数据库连接信息。生产环境强烈建议使用外部 MySQL 数据库。</p>
    <pre><code># 服务端口
server.port=8848

# 运行模式: standalone (单机) 或 cluster (集群)
nacos.standalone=false

# --- 数据库配置 (生产环境) ---
spring.datasource.platform=mysql
db.num=1
db.url.0=*************************************************************************************************************************
db.user=nacos
db.password=password
</code></pre>

    <h2>三、日常运维</h2>
    <h3>1. 启动与停止服务</h3>
    <p>Nacos 的启动和停止脚本位于其解压目录的 <code>bin</code> 文件夹下。</p>
    <p><b>启动 (Linux/macOS):</b></p>
    <pre><code># 单机模式启动
sh startup.sh -m standalone

# 集群模式启动
sh startup.sh
</code></pre>

    <p><b>停止:</b></p>
    <pre><code>sh shutdown.sh
</code></pre>
    
    <p><b>查看状态与日志:</b></p>
    <pre><code># 查看Nacos进程
ps -ef | grep nacos

# 查看启动日志
tail -f ${NACOS_HOME}/logs/start.out

# 查看运行日志
tail -f ${NACOS_HOME}/logs/nacos.log
</code></pre>

    <h3>2. Web 界面核心操作</h3>
    <p>Nacos 的日常管理主要通过其控制台完成。访问地址：<code>http://<Nacos节点IP>:8848/nacos</code> (默认用户名/密码: nacos/nacos)</p>
    <ul>
        <li><b>配置管理:</b> 在此可以创建、发布、编辑和删除应用的配置。支持多环境、多格式（Properties, YAML, JSON等）的配置管理。</li>
        <li><b>服务管理:</b> 查看当前注册到 Nacos 的所有服务及其健康状态。可以对服务实例进行上下线、编辑权重等操作。</li>
        <li><b>命名空间:</b> 用于进行租户隔离。不同的命名空间下的服务和配置是相互不可见的，常用于区分开发、测试、生产等不同环境。</li>
        <li><b>集群管理:</b> 查看 Nacos 集群中所有节点的状态和元数据信息。</li>
    </ul>

    <h2>四、推荐运维实践</h2>
    <ul>
        <li><b>集群化部署:</b> 生产环境必须使用至少3个节点的集群模式来保证高可用。</li>
        <li><b>外置数据库:</b> 必须使用外部高可用的 MySQL 数据库（推荐5.7+版本）进行数据持久化，并定期备份数据库。</li>
        <li><b>安全加固:</b> 及时修改默认的 `nacos/nacos` 密码。在生产环境中，通过防火墙或安全组限制 Nacos 端口的公网访问。</li>
        <li><b>命名空间隔离:</b> 充分利用命名空间来隔离不同环境（开发、测试、生产）或不同业务线的服务与配置，避免交叉影响。</li>
        <li><b>监控与告警:</b> 通过 Nacos 暴露的 Metrics 接口（如 /nacos/actuator/prometheus）接入监控系统，对 Nacos 集群的健康状况、服务和配置数量等关键指标进行监控并设置告警。</li>
        - <b>数据持久化:</b> 务必确认 `nacos.standalone` 设置为 `false` 并正确配置了外部MySQL数据库，以防止因单点故障导致数据丢失。
    </ul>

</body>
</html>