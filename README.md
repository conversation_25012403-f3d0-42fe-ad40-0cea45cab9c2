# 集成主框架
> 主项目，包括菜单和子项目和容器

## Node Version
V16

## Project setup
```
yarn
```

### Compiles and hot-reloads for development
```
yarn dev
```

### Compiles and minifies for production
```
yarn build
```

## 本地开发调试环境配置
> 本地开发调试时可配置成海创园环境和天合开发环境<br>
> 只需在以下文件中更改配置，具体请查看对应文件的配置<br>
> `config/proxy.js:4` 更改代理到对应的环境配置<br>
> `config/config.js:46` 更改headScripts配置项<br>

## 本地子应用调试
> 在`src/utils/microApps.js:10`中对微应用列表microList采用本地注释配置，具体请查看对应代码