import { defineConfig } from "umi";
import routes from "./router";
import theme from "./theme";
import proxy from "./proxy";
import zhCN from 'antd/locale/zh_CN';
import tailwindcss from "tailwindcss";
import autoprefixer from "autoprefixer";

export default defineConfig({
  title: "应用服务平台",
  favicons: [
    '/favicon.png'
  ],
  hash: true,
  alias: {
    "@": "/src",
  },
  history: { type: "hash" },
  mock: false,
  theme,
  routes: routes,
  proxy: proxy,
  npmClient: "yarn",
  plugins: ["@umijs/plugins/dist/initial-state", "@umijs/plugins/dist/qiankun", "@umijs/plugins/dist/model", "@umijs/plugins/dist/antd"],
  // extraBabelPlugins: [
  //   ...(process.env.NODE_ENV === "production"
  //     ? ["transform-remove-console"]
  //     : []),
  // ],
  extraBabelPlugins: [
    ...(process.env.NODE_ENV === "production"
      ? []
      : []),
  ],
  initialState: {},
  model: {},
  antd: {
    style: 'less',
    configProvider: {
      locale: zhCN,
      prefixCls: "micro",
      theme: {
        token: theme.token
      }
    }
  },
  links: [{ rel: "icon", href: "data:;base64,=" }], // 默认空，不加载umi图标
  headScripts: [
    "/languageSwitch.js", 
    "/appConfig.js",// 天合环境配置文件（部署天合环境时请务必用这个）
    // "/appConfig-hcy.js"// 海创园开发环境配置文件（只供在海创园环境调试使用，部署天合环境时请务必注释）
  ],
  qiankun: {
    master: {
      apps: [],
    },
  },
  extraPostCSSPlugins: [tailwindcss(), autoprefixer()],
  jsMinifier: "esbuild", // 确保使用 esbuild 作为压缩工具
  esbuildMinifyIIFE: true, // 添加此配置
});
