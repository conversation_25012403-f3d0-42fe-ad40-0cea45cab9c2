// 天合环境的代理配置
const proxy = {
  // 底座网关
  "/kepler": {
    target: 'https://tasp-gateway-dev.trinasolar.com',
    changeOrigin: true,
  },

  // 集成中台子应用api请求代理
  '/harmony/kepler': {
    target: 'https://tasp-gateway-dev.trinasolar.com',
    changeOrigin: true,
    pathRewrite: { '^/harmony': '' },
  },
  // 中间件应用api请求代理
  '/apimiddleware': {
    // target: 'http://***********:31089',
    target: 'https://tasp-dev.trinasolar.com/apimiddleware',
    changeOrigin: true,
    pathRewrite: { '/apimiddleware': '' },
  },
  // 技术文档库相应api转发
  '/api/doc':{
    target: 'https://tasp-gateway-dev.trinasolar.com/kepler/doc',
    changeOrigin: true,
    pathRewrite: { '^/api/doc': '' }
  },

  // 应用程序管理子应用转发
  "/system": {
    target: 'http://devops-system-app-svc-clusterip.trinasolar-platform--tasp--dev/system/',
    changeOrigin: true,
  },
  // 应用系统管理子应用转发
  "/kepler-webpage": {
    target: 'http://kepler-webpage-svc-clusterip.trinasolar-platform--tasp--dev/kepler-webpage/',
    changeOrigin: true
  },
  // 技术文档库子应用转发
  "/dochub": {
    target: 'http://tasp-dochub-ui-svc-clusterip.trinasolar-platform--tasp--dev/dochub/',
    changeOrigin: true
  },
  // 前端脚手架子应用转发
  "/trinaCli": {
    target:  "http://*************:33434/",
    changeOrigin: true
  },
  // 效能平台子应用转发
  "/efficiency": {
    target: 'http://tasp-scm-ui-svc-clusterip.trinasolar-platform--tasp--dev/efficiency/',
    changeOrigin: true
  },
  // rag智能AI的api转发
  '/rag-api': {
    target: 'http://***********',
    changeOrigin: true,
  },
  // IPass平台子应用转发
  "/apim": {
    target: 'http://localhost:49090/apim/',
    changeOrigin: true
  },
};

export default proxy;
