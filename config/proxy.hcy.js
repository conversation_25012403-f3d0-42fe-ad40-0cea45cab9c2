// 海创园环境的代理配置
const proxy = {
  // 底座网关
  "/kepler": {
    target: 'http://*************:33428/',
    changeOrigin: true,
  },

  // 集成中台子应用api请求代理
  '/harmony': {
    target: 'http://*************:33428',
    changeOrigin: true,
    pathRewrite: { '^/harmony': '' },
  },
  // 中间件应用api请求代理
  '/apimiddleware': {
    target: 'http://************:31089',
    changeOrigin: true,
    pathRewrite: { '/apimiddleware': '' },
  },
  // 技术文档库相应api转发
  '/api/doc':{
    target: 'http://*************:33442/',
    changeOrigin: true,
    pathRewrite: { '^/api/doc': '' }
  },

  // 应用程序管理子应用转发
  "/system": {
    target: 'http://localhost:8082/system/',
    // target: 'http://*************:33436',
    changeOrigin: true,
  },
  // 应用系统管理子应用转发
  "/kepler-webpage": {
    target: 'http://localhost:8081',
    changeOrigin: true
  },
  // 技术文档库子应用转发
  "/dochub": {
    target: 'http://*************:33443/dochub/',
    changeOrigin: true
  },
  // 前端脚手架子应用转发
  "/trinaCli": {
    target: 'http://localhost:3000/trinaCli/',
    changeOrigin: true
  },
  // 效能平台子应用转发
  "/efficiency": {
    target: 'http://localhost:8003/efficiency/',
    changeOrigin: true
  },
  // rag智能AI的api转发
  '/rag-api': {
    target: 'http://*************:32618',
    changeOrigin: true,
  },
  // IPass平台子应用转发
  "/apim": {
    target: 'http://localhost:49090/apim/',
    changeOrigin: true
  },
};

export default proxy;
