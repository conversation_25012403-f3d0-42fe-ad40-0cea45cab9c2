server {
        listen       80;
        server_name  localhost;
        #client_max_body_size 200M;
        #charset koi8-r;

        #access_log  logs/host.access.log  main;

        location / {
            root /opt/sinopec;
            try_files $uri $uri/ /index.html;
            add_header  Access-Control-Allow-Origin * ;
            add_header  Access-Control-Allow-Methods *;
            add_header  Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
        }       


        location ~ .*\.(js|css|htm|html|gif|jpg|jpeg|png|bmp|swf|ioc|rar|zip|txt|flv|mid|doc|ppt|pdf|xls|mp3|mp4|wma|svg|otf|ttf|ttc|eot|woff|woff2|json)$ {
            add_header  Access-Control-Allow-Origin   * ;
            add_header  Access-Control-Allow-Methods *;
            add_header  Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
            add_header  Cache-Control no-cache;
            root /opt/sinopec;
        }

        location /kepler/ {
                proxy_set_header real_ip $remote_addr;
                proxy_pass http://************:8088/kepler/;
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
        }
		
	location /kepler-webpage/ {
                proxy_set_header real_ip $remote_addr;
                proxy_pass http://***********:80/kepler-webpage/;
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
        }

        location /system/ {
                proxy_set_header real_ip $remote_addr;
                proxy_pass http://*************:33436/system/;
                proxy_connect_timeout 500s;
                proxy_read_timeout 500s;
                proxy_send_timeout 500s;
        }

        #error_page  404              /404.html;

        # redirect server error pages to the static page /50x.html
        #
        error_page   500 502 503 504  /50x.html;
        # To allow POST on static pages 允许静态页使用POST方法
        error_page  405     =200 $uri;
        location = /50x.html {
            root   html;
        }
}